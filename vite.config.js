import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/js/app.js'
            ],
            refresh: true,
            // Use the default 'build' subdirectory for the manifest file
            buildDirectory: 'build',
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: {
        alias: {
            vue: 'vue/dist/vue.esm-bundler.js',
            '@': path.resolve(__dirname, './resources/js')
        },
        extensions: ['.ts', '.vue', '.js'],
        modules: ['node_modules', 'resources/js']
    },
    build: {
        // Set outDir to public/build to match <PERSON><PERSON>'s expectations for the manifest file
        outDir: 'public/build',
        // Prevent Vite from emptying the output directory before building
        // This preserves important static assets like images, .htaccess, and index.php
        emptyOutDir: false,
        rollupOptions: {
            output: {
                // Use default output paths within the build directory
                entryFileNames: 'js/[name].js',
                chunkFileNames: 'js/[name].js',
                assetFileNames: (assetInfo) => {
                    if (assetInfo.fileName && assetInfo.fileName.endsWith('.css')) {
                        return 'css/[name][extname]';
                    }
                    return 'assets/[name][extname]';
                }
            }
        }
    },
    server: {
        host: '0.0.0.0',
        port: 5173,
        strictPort: true,
        hmr: {
            host: 'localhost',
            port: 5173,
        },
    },
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['./tests/Vue/setup-vitest.js'],
        include: ['tests/Vue/**/*.test.js'],
        coverage: {
            provider: 'v8',
            reporter: ['html', 'text-summary'],
            reportsDirectory: './coverage',
            include: ['resources/js/composables/**/*.js']
        },
        silent: true,
    },
});
