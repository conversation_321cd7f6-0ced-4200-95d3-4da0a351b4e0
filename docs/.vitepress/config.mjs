// import { defineConfig } from 'vitepress'
import { with<PERSON><PERSON>maid } from "vitepress-plugin-mermaid";

// https://vitepress.dev/reference/site-config
export default withMermaid({
    title: "CLASS technical docs",
    description: "For developers, by developers",
    themeConfig: {
        search: {
            provider: 'local'
        },
        // https://vitepress.dev/reference/default-theme-config
        nav: [
            { text: 'Home', link: '/' },
            { text: 'Components', link: '/components' },
            { text: 'Concepts', link: '/concepts' },
            { text: 'Test data', link: '/testdata' }
        ],

        sidebar: [
            {
                text: 'Components',
                items: [
                    { text: 'Class', link: '/components/Class' },
                    { text: 'ClassY', link: '/components/ClassY' },
                    { text: 'ClassE', link: '/components/ClassE' }
                ]
            },
            {
                text: "Test data",
                items: [
                    { text: "Registrations", link: '/testdata/students-and-courses' }
                ]
            },
            {
                text: "Concepts",
                items: [
                    { text: "Planning conflicts", link: '/concepts/planning-conflicts' },
                    { text: "Layered planning", link: '/concepts/layered-planning' },
                ]
            }
        ]
    }
})
