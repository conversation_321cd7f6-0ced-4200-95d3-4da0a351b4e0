# Student Components

## Student card components
- uri: /students/{id}/edit
- uri: /students/create

Please note: this is an old page and uses a <strong>HTML form submit</strong> instead of an API call.
We'll need to fix that at some point...

<img class="medium-zoom" src="./Class_Student_Card_Components.drawio.png" alt="Student Components Diagram" />

## List Students Components
uri: /students
<img class="medium-zoom" src="./Class_List_Students_Components.drawio.png" alt="Student Components Diagram" />

## Active Students Report Components
uri: /report-active-students-grouped (deprecated)
<img class="medium-zoom" src="./CLASS_Report_ActiveStudents_Components.drawio.png" alt="Student report active students components diagram">

## Student Access Codes Components
uri: /students/accesscodes
<img class="medium-zoom" src="./CLASS_StudentAccessCodes_Components.drawio.png" alt="Student Access Codes Components Diagram" />

## Student Default Checklists Components
uri: /defaultchecklists
<img class="medium-zoom" src="./Class_Default_Checklists_Components.drawio.png" alt="Student Default Checklists Components Diagram" />

## Student Report participating checklist Components 
uri: /report-registrations-open-checklists
<img class="medium-zoom" src="./CLASS_Report_Registrations_Open_Checklists_Components.drawio.png" alt="Student Report participating checklist Components Diagram" />

## Student Report registrations Components 
uri: /report-students
<img class="medium-zoom" src="./CLASS_Report_Students_Registrations_Components.drawio.png" alt="Student Report participating checklist Components Diagram" />

## StudentList Components
uri: /studentlists
<img class="medium-zoom" src="./Class_Studentlists_Components.drawio.png" alt="Student Default Checklists Components Diagram" />

## Student attendance options Components
uri: /attendanceoptions
<img class="medium-zoom" src="./Class_Attendanceoptions_Components.drawio.png" alt="Student Attendance Options Components Diagram" />

## Export student preferences Components
uri: /timetables/showstudentprefs
<img class="medium-zoom" src="./Class_Export_StudentPrefs_Components.drawio.png" alt="Export Student Preferences Components Diagram" />

