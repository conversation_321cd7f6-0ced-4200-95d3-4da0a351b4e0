# Dashboard
## Main page
uri: /home (or /)
<img class="medium-zoom" src="./Class_Dashboard_components.drawio.png" alt="Domain edit Components Diagram" />


## Alarms page
uri: /report-alarms
<img class="medium-zoom" src="./Class_Reports_Alarms_components.drawio.png" alt="Domain edit Components Diagram" />

## Manangement info page
View on Meta base reports. uses package tombenevides/metavel to display the data assembled in Meta Base.

uri: /dashboardoverview
<img class="medium-zoom" src="./Class_Reports_Management_Info.drawio.png" alt="Domain edit Components Diagram" />

