# Layered planning

CLASS uses repeatable tutoring events. For instance a student has every week or every two weeks a lesson on Thursday on 11:00 am.
In the planning stage all events are saved to the database for the whole schoolyear. 

But there are also date exceptions: dates on which there is a problem because: the location is already booked for an incidental event like a performance or simply because of a holiday. 
In older versions of CLASS we used to skip these dates during the planning phase. But if something changes during the school year (and it always does) we create a new planning conflict, leaving it to the planner to tackle this. 

That's where the layered planning comes in. 

## Layering in the calendar
The planning calendar shows all tutoring events that have been planned. On top of that we layer the date exceptions. Because the dateexceptions are layered on top, the will show in the calendar, blocking the tutoring events. There is no need to re-plan tutoring events because of conflicts. 

## Exceptions to the rule
There are two ways to prevent a tutoring event being blocked:
- setting it as "sticky"
- setting the dateexception as non-blocking

Conceptually it looks like this:
<img src="./layered-planning.drawio.png" alt="Conceptual view of layered planning" class="medium-zoom"/>

## Planning conflicts
See [Planning Conflicts](../planning-conflicts/index) for more information about detecting planning conflicts.
