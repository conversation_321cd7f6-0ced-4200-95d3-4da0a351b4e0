# ConflictChecker Documentation

## Overview

The ConflictChecker is a component in the CLASS application that identifies conflicts between DateExceptions and scheduled events. It implements the Strategy pattern to provide flexible conflict detection based on different criteria.

## Purpose

In the CLASS application, a DateException represents a period when normal scheduling cannot occur due to conflicts with other events. The ConflictChecker helps identify which existing events conflict with a DateException based on various criteria such as:

- Whole school events (affecting all locations and tutors)
- Location-specific events
- Tutor-specific events

## Implementation

The ConflictChecker uses the Strategy pattern combined with the Chain of Responsibility pattern to evaluate conflicts based on multiple criteria.

### Strategy Pattern

The Strategy pattern allows the ConflictChecker to use different algorithms for conflict detection without changing its core implementation. This makes it easy to add new conflict detection strategies in the future.

```mermaid
classDiagram
    class ConflictChecker {
        -ConflictStrategyInterface strategy
        +setStrategy(ConflictStrategyInterface strategy)
        +getConflictingEvents(DateException dateException)
        -getEventsInTimeRange(DateException dateException)
    }
    
    class ConflictStrategyInterface {
        <<interface>>
        +setNext(ConflictStrategyInterface next) ConflictStrategyInterface
        +hasConflict(DateException dateException, object event) bool
    }
    
    class BaseStrategy {
        <<abstract>>
        #ConflictStrategyInterface nextStrategy
        +setNext(ConflictStrategyInterface next) ConflictStrategyInterface
        #next(DateException dateException, object event) bool
        +hasConflict(DateException dateException, object event)* bool
    }
    
    class WholeSchoolStrategy {
        +hasConflict(DateException dateException, object event) bool
    }
    
    class LocationStrategy {
        +hasConflict(DateException dateException, object event) bool
    }
    
    class TutorStrategy {
        +hasConflict(DateException dateException, object event) bool
    }
    
    ConflictChecker --> ConflictStrategyInterface : uses
    ConflictStrategyInterface <|.. BaseStrategy : implements
    BaseStrategy <|-- WholeSchoolStrategy : extends
    BaseStrategy <|-- LocationStrategy : extends
    BaseStrategy <|-- TutorStrategy : extends
```

### Chain of Responsibility Pattern

The ConflictChecker also implements the Chain of Responsibility pattern, allowing multiple strategies to be chained together. Each strategy can either handle the conflict check or pass it to the next strategy in the chain.

## Key Components

### ConflictChecker

The main class that orchestrates conflict detection. It:

1. Accepts a strategy via `setStrategy()`
2. Retrieves events that overlap with a DateException's time range
3. Uses the strategy to filter events that have actual conflicts
4. Returns formatted conflict information

### ConflictStrategyInterface

The interface that all conflict strategies must implement:

- `setNext(ConflictStrategyInterface $next)`: Sets the next strategy in the chain
- `hasConflict(DateException $dateException, object $event)`: Determines if there's a conflict

### BaseStrategy

An abstract class that implements common functionality for all strategies:

- Maintains a reference to the next strategy in the chain
- Provides a `next()` method to delegate to the next strategy

### Concrete Strategies

#### WholeSchoolStrategy

Checks if the DateException affects the entire school (no specific tutor or location).

#### LocationStrategy

Checks if the DateException and event share the same location.

#### TutorStrategy

Checks if the DateException and event share the same tutor.

## Usage Example

```php
// Create strategies
$wholeSchoolStrategy = new WholeSchoolStrategy();
$locationStrategy = new LocationStrategy();
$tutorStrategy = new TutorStrategy();

// Chain strategies together
$wholeSchoolStrategy->setNext($locationStrategy);
$locationStrategy->setNext($tutorStrategy);

// Create ConflictChecker and set strategy
$conflictChecker = new ConflictChecker();
$conflictChecker->setStrategy($wholeSchoolStrategy);

// Check for conflicts
$dateException = DateException::find($id);
$conflicts = $conflictChecker->getConflictingEvents($dateException);
```
## Usage Example

Here's an example of how to use the ConflictChecker:

```php
// Create the strategies
$wholeSchoolStrategy = new WholeSchoolStrategy();
$locationStrategy = new LocationStrategy();
$tutorStrategy = new TutorStrategy();

// Chain the strategies
$wholeSchoolStrategy->setNext($locationStrategy);
$locationStrategy->setNext($tutorStrategy);

// Create the ConflictChecker and set the strategy
$conflictChecker = new ConflictChecker();
$conflictChecker->setStrategy($wholeSchoolStrategy);

// Check for conflicts
$dateException = new DateException();
$dateException->datetime_start = '2025-07-27 09:00:00';
$dateException->datetime_end = '2025-07-27 17:00:00';
$dateException->tutor_id = 1;
$dateException->location_id = 2;

$conflictingEvents = $conflictChecker->getConflictingEvents($dateException);
```

## Benefits

1. **Flexibility**: New conflict detection strategies can be added without modifying existing code
2. **Maintainability**: Each strategy has a single responsibility
3. **Testability**: Strategies can be tested in isolation
4. **Configurability**: The order of strategies can be changed at runtime

## Database Queries

The ConflictChecker uses a complex database query to retrieve events that overlap with a DateException's time range. It joins several tables:

- `events`: Contains event information
- `timetables`: Links events to course-student relationships
- `course_student`: Links courses to students
- `students`: Contains student information
- `courses`: Contains course information

The query filters events based on whether:
- The event starts during the DateException period
- The event ends during the DateException period

## Edge Cases

The ConflictChecker handles several edge cases:

1. **Student Groups**: Identified by a birth date of '1800-01-01' and a firstname of '-'
2. **Whole School Events**: DateExceptions with no specific tutor or location
3. **Sticky Events**: Events that are marked as "sticky" (cannot be moved) See the description of the [Layered planning](../layered-planning/index) for more information
