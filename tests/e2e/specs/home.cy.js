describe("Load Home page", () => {
    const testAccount = {
        secret: 'UX7E3PXBUKZE4VIB',
        email: '<EMAIL>',
        password: 'cladmin'
    }

    it("should load the home page", () => {
        cy.visit("/");
        cy.contains("CLASS").should("be.visible");
    });

    it("should navigate to the login page", () => {
        cy.visit("/");
        cy.get("a[class='nav-link']").contains("Login").click();
        cy.url().should("include", "/login");
        // should show the login form: 
        // - email (#inputEmailAddress), password(#inputPassword), 
        // - submit button (data-testid="login-button")
        cy.get("#inputEmailAddress").should("be.visible");
        cy.get("#inputPassword").should("be.visible");
        cy.get("[data-testid='login-button']").should("be.visible");
    });

    it("should show the dashboard on the homepage after logging in", () => {
        cy.login(testAccount);
        cy.visit("/home");
        cy.url().should("include", "/home");
       
        // Now check for the components
        cy.get("[data-testid='personal-timetable']").should("be.visible");
        cy.get("[data-testid='class-alerts']").should("be.visible");
        cy.get("[data-testid='class-birthdays']").should("be.visible");
        cy.get("[data-testid='class-search-students']").should("be.visible");
        cy.get("[data-testid='class-search-courses']").should("be.visible");
    });
});
