import { test, expect, testAccount } from '../fixtures/auth.js';
import { authenticator } from 'otplib';

/**
 * Test the login process
 * This test demonstrates the full TOTP authentication flow including failure and success scenarios
 */
test('should login, fail TOTP, then succeed with correct TOTP', async ({ page }) => {
    // Visit the login page
    await page.goto('/login');

    // Fill in email and password
    await page.fill('input[name="email"]', testAccount.email);
    await page.fill('input[name="password"]', testAccount.password);

    // Click login button
    await page.click('button[type="submit"]');

    // Assert that the TOTP field is shown
    await expect(page.locator('input[name="one_time_password"]')).toBeVisible();

    // /////////////////////////////
    // Wrong TOTP code
    // /////////////////////////////
    await page.fill('input[name="one_time_password"]', '123456');

    // Click the Authenticate button
    await page.click('button[data-testid="authenticate-btn"]');

    // Assert that an error is shown
    await expect(page.locator('div[class="alert alert-danger"]')).toBeVisible();

    // /////////////////////////////
    // Generate correct TOTP
    // /////////////////////////////
    const correctTotp = authenticator.generate(testAccount.secret);

    // Clear TOTP field and enter correct code
    await page.fill('input[name="one_time_password"]', '');
    await page.fill('input[name="one_time_password"]', correctTotp);
    
    // Force input event (equivalent to Cypress dispatchEvent)
    await page.dispatchEvent('input[name="one_time_password"]', 'input');

    // Set up network interceptor for 2FA verification
    const verifyTotpPromise = page.waitForResponse(response => 
        response.url().includes('/2fa/2faVerify') && response.request().method() === 'POST'
    );

    // Click Authenticate button again
    await page.click('button[data-testid="authenticate-btn"]');

    // Wait for the intercepted request to complete and verify status
    const verifyResponse = await verifyTotpPromise;
    expect(verifyResponse.status()).toBe(302);

    // Now re-navigate to the home page, it should now skip the 2FA page
    await page.goto('/home');
    
    // Wait a moment for navigation to complete
    await page.waitForTimeout(2000);
    
    // Assert that we are navigated to the home page
    await expect(page).toHaveURL(/.*\/home/<USER>
});
