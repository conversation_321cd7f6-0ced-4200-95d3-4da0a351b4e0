import { test, expect, testAccount, loginWithTOTP } from '../fixtures/auth.js';

test.describe('Student card', () => {
    test.beforeEach(async ({ page }) => {
        // Login before each test
        await loginWithTOTP(page, testAccount);
        await page.goto('/students/5/edit');
    });

    test('should show the student card and handle API calls', async ({ page }) => {
        // Set up API call interception
        const apiCallPromise = page.waitForResponse(response => 
            response.url().includes('/api/') && response.request().method() === 'GET'
        );

        // Check authentication state (equivalent to Cypress window checks)
        const hasToken = await page.evaluate(() => !!window.localStorage.getItem('token'));
        const cookies = await page.evaluate(() => document.cookie);
        console.log('Auth check:', hasToken);
        console.log('Session check:', cookies);

        // Should show the student card sections
        await expect(page.locator('[data-testid="student-card-generic"]')).toBeVisible();
        await expect(page.locator('[data-testid="student-card-contact"]')).toBeVisible();
        await expect(page.locator('[data-testid="student-card-bank"]')).toBeVisible();
        await expect(page.locator('[data-testid="student-card-logbook"]')).toBeVisible();
        await expect(page.locator('[data-testid="student-card-course-data"]')).toBeVisible();
        await expect(page.locator('[data-testid="student-card-student-lists"]')).toBeVisible();
        await expect(page.locator('[data-testid="student-card-student-tasks"]')).toBeVisible();

        // Wait for and check API responses
        try {
            const apiResponse = await apiCallPromise;
            console.log('API Response status:', apiResponse.status());
            
            if (apiResponse.status() === 500) {
                const responseBody = await apiResponse.text();
                console.log('API Error:', responseBody);
            }
        } catch (error) {
            console.log('No API calls intercepted or timeout occurred');
        }
    });

    test("should show the student's birthdate and age", async ({ page }) => {
        // Look for date_of_birth element and its child dp-input
        await expect(page.locator('[data-test-id="date_of_birth"]')).toBeVisible();
        await expect(page.locator('[data-test-id="date_of_birth"] [data-test-id="dp-input"]')).toBeVisible();
        
        // Check date format (DD-MM-YYYY)
        const dateValue = await page.locator('[data-test-id="date_of_birth"] [data-test-id="dp-input"]').inputValue();
        expect(dateValue).toMatch(/^\d{2}-\d{2}-\d{4}$/);
        
        // Check age is greater than 1
        const ageValue = await page.locator('#age').inputValue();
        const age = parseInt(ageValue);
        expect(age).toBeGreaterThan(1);
    });

    test("should show the student's contacts telephone and email", async ({ page }) => {
        await expect(page.locator('#contactValue_0')).toBeVisible();
        await expect(page.locator('#contactValue_1')).toBeVisible();
        
        // Check that contact values are not empty
        const contactValue0 = await page.locator('#contactValue_0').inputValue();
        const contactValue1 = await page.locator('#contactValue_1').inputValue();
        
        expect(contactValue0).not.toBe('');
        expect(contactValue1).not.toBe('');
    });

    test('should quick-jump to another student', async ({ page }) => {
        await expect(page.locator('[data-test-id="QJBtn_Zoek_leerling"]')).toBeVisible();
        await page.click('[data-test-id="QJBtn_Zoek_leerling"]');
        
        // Click the first student in the list (first button in .overflow-popup)
        await page.click('.overflow-popup button:first-child');
        
        // URL should no longer be /students/5/edit
        await expect(page).not.toHaveURL(/.*\/students\/5\/edit/);
        
        // But it should contain /students/ and /edit
        await expect(page).toHaveURL(/.*\/students\/.+\/edit/);
    });
});
