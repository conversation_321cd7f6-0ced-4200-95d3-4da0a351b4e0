import { test, expect, testAccount, loginWithTOTP } from '../fixtures/auth.js';

test.describe('Load Home page', () => {
    test('should load the home page', async ({ page }) => {
        await page.goto('/');
        await expect(page.locator('text=CLASS')).toBeVisible();
    });

    test('should navigate to the login page', async ({ page }) => {
        await page.goto('/');

        // Click on Login link - try multiple selectors to be more robust
        try {
            await page.click('a.nav-link:has-text("Login")');
        } catch {
            // Fallback to more general selector
            await page.click('a:has-text("Login")');
        }

        // Wait for navigation and verify URL includes /login
        await page.waitForURL(/.*\/login/);
        await expect(page).toHaveURL(/.*\/login/);

        // Verify login form elements are visible
        await expect(page.locator('#inputEmailAddress')).toBeVisible();
        await expect(page.locator('#inputPassword')).toBeVisible();
        await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
    });

    test('should show the dashboard on the homepage after logging in', async ({ page }) => {
        // Login using the auth fixture
        await loginWithTOTP(page, testAccount);
        
        // Navigate to home page
        await page.goto('/home');
        await expect(page).toHaveURL(/.*\/home/<USER>
        
        // Check for dashboard components
        await expect(page.locator('[data-testid="personal-timetable"]')).toBeVisible();
        await expect(page.locator('[data-testid="class-alerts"]')).toBeVisible();
        await expect(page.locator('[data-testid="class-birthdays"]')).toBeVisible();
        await expect(page.locator('[data-testid="class-search-students"]')).toBeVisible();
        await expect(page.locator('[data-testid="class-search-courses"]')).toBeVisible();
    });
});
