import { chromium } from '@playwright/test';
import { authenticator } from 'otplib';

const testAccount = {
    secret: 'UX7E3PXBUKZE4VIB',
    email: '<EMAIL>',
    password: 'cladmin'
};

async function globalSetup() {
    console.log('Setting up global authentication...');
    
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        // Navigate to login page
        await page.goto('http://localhost:8000/login');
        
        // Fill in email and password
        await page.fill('input[name="email"]', testAccount.email);
        await page.fill('input[name="password"]', testAccount.password);
        
        // Click login button
        await page.click('button[type="submit"]');
        
        // Wait for TOTP field to appear
        await page.waitForSelector('input[name="one_time_password"]', { timeout: 30000 });
        
        // Generate TOTP code
        const totp = authenticator.generate(testAccount.secret);
        console.log(`Generated TOTP for global setup: ${totp}`);
        
        // Fill in TOTP
        await page.fill('input[name="one_time_password"]', totp);
        
        // Trigger input event
        await page.dispatchEvent('input[name="one_time_password"]', 'input');
        
        // Set up request interception for 2FA verification
        const verifyTotpPromise = page.waitForResponse(response => 
            response.url().includes('/2fa/2faVerify') && response.request().method() === 'POST',
            { timeout: 30000 }
        );
        
        // Click authenticate button
        await page.click('button[data-testid="authenticate-btn"]');
        
        // Wait for 2FA verification response
        const verifyResponse = await verifyTotpPromise;
        console.log(`Global setup 2FA verification status: ${verifyResponse.status()}`);

        // Wait a bit for the session to be established
        await page.waitForTimeout(3000);

        // Navigate to home to ensure we're authenticated
        await page.goto('http://localhost:8000/home');

        // Wait for home page to load
        await page.waitForLoadState('networkidle');

        // Save the authentication state
        await context.storageState({ path: 'tests/e2e/playwright/.auth/user.json' });
        console.log('Global authentication setup complete');
        
    } catch (error) {
        console.error('Global setup failed:', error);
        throw error;
    } finally {
        await browser.close();
    }
}

export default globalSetup;
