import { test as base, expect } from '@playwright/test';
import { authenticator } from 'otplib';

// Test account configuration
export const testAccount = {
    secret: 'UX7E3PXBUKZE4VIB',
    email: '<EMAIL>',
    password: 'cladmin'
};

// Extend base test with authentication fixture
export const test = base.extend({
    // Create an authenticated page
    authenticatedPage: async ({ page }, use) => {
        await loginWithTOTP(page, testAccount);
        await use(page);
    },
});

/**
 * Login function that handles TOTP authentication
 * @param {Page} page - Playwright page object
 * @param {Object} account - Account credentials with secret, email, password
 */
export async function loginWithTOTP(page, account) {
    // Navigate to the login page
    await page.goto('/login');
    
    // Fill in email and password
    await page.fill('input[name="email"]', account.email);
    await page.fill('input[name="password"]', account.password);
    
    // Click login button
    await page.click('button[type="submit"]');
    
    // Wait for the TOTP field to appear
    await page.waitForSelector('input[name="one_time_password"]');
    
    // Generate TOTP code
    const totp = authenticator.generate(account.secret);
    console.log(`Generated TOTP: ${totp}`);
    
    // Fill in TOTP
    await page.fill('input[name="one_time_password"]', totp);
    
    // Trigger input event (equivalent to Cypress dispatchEvent)
    await page.dispatchEvent('input[name="one_time_password"]', 'input');
    
    // Set up request interception for 2FA verification
    const verifyTotpPromise = page.waitForResponse(response => 
        response.url().includes('/2fa/2faVerify') && response.request().method() === 'POST'
    );
    
    // Click the authenticate button
    await page.click('button[data-testid="authenticate-btn"]');
    
    // Wait for 2FA verification response
    const verifyResponse = await verifyTotpPromise;
    console.log(`2FA verification status: ${verifyResponse.status()}`);
    
    // Verify successful authentication (302 redirect)
    expect(verifyResponse.status()).toBe(302);
}

/**
 * Create authenticated browser context for session reuse
 * @param {Browser} browser - Playwright browser instance
 * @param {Object} account - Account credentials
 * @returns Authenticated browser context
 */
export async function createAuthenticatedContext(browser, account) {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    await loginWithTOTP(page, account);
    
    // Navigate to home to ensure session is established
    await page.goto('/home');
    await page.waitForURL('**/home');
    
    await page.close();
    return context;
}

export { expect };
