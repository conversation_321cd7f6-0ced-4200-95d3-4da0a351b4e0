import { test as base, expect } from '@playwright/test';
import { authenticator } from 'otplib';

// Test account configuration
export const testAccount = {
    secret: 'UX7E3PXBUKZE4VIB',
    email: '<EMAIL>',
    password: 'cladmin'
};

// Extend base test with authentication fixture
export const test = base.extend({
    // Create an authenticated page with session storage
    authenticatedPage: async ({ page }, use) => {
        // Use session storage to avoid repeated logins
        await page.context().storageState({ path: 'auth-state.json' }).catch(() => {
            // File doesn't exist yet, that's fine
        });

        await loginWithTOTP(page, testAccount);

        // Save the authentication state
        await page.context().storageState({ path: 'auth-state.json' });

        await use(page);
    },
});

/**
 * Login function that handles TOTP authentication
 * @param {Page} page - Playwright page object
 * @param {Object} account - Account credentials with secret, email, password
 */
export async function loginWithTOTP(page, account) {
    // Check if already authenticated by trying to access a protected page
    await page.goto('/home');

    // If we're redirected to login, we need to authenticate
    if (page.url().includes('/login') || page.url().includes('/2fa')) {
        console.log('Authentication required, logging in...');

        // Navigate to login page if not already there
        if (!page.url().includes('/login')) {
            await page.goto('/login');
        }

        // Fill in email and password
        await page.fill('input[name="email"]', account.email);
        await page.fill('input[name="password"]', account.password);

        // Click login button
        await page.click('button[type="submit"]');

        // Wait for the TOTP field to appear with longer timeout
        await page.waitForSelector('input[name="one_time_password"]', { timeout: 30000 });

        // Generate TOTP code
        const totp = authenticator.generate(account.secret);
        console.log(`Generated TOTP: ${totp}`);

        // Fill in TOTP
        await page.fill('input[name="one_time_password"]', totp);

        // Trigger input event (equivalent to Cypress dispatchEvent)
        await page.dispatchEvent('input[name="one_time_password"]', 'input');

        // Set up request interception for 2FA verification with timeout
        const verifyTotpPromise = page.waitForResponse(response =>
            response.url().includes('/2fa/2faVerify') && response.request().method() === 'POST',
            { timeout: 30000 }
        );

        // Click the authenticate button
        await page.click('button[data-testid="authenticate-btn"]');

        // Wait for 2FA verification response
        const verifyResponse = await verifyTotpPromise;
        console.log(`2FA verification status: ${verifyResponse.status()}`);

        // Verify successful authentication (302 redirect)
        expect(verifyResponse.status()).toBe(302);

        // Wait for redirect to complete
        await page.waitForURL(/.*\/home/<USER>
        console.log('Authentication successful');
    } else {
        console.log('Already authenticated, skipping login');
    }
}

/**
 * Create authenticated browser context for session reuse
 * @param {Browser} browser - Playwright browser instance
 * @param {Object} account - Account credentials
 * @returns Authenticated browser context
 */
export async function createAuthenticatedContext(browser, account) {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    await loginWithTOTP(page, account);
    
    // Navigate to home to ensure session is established
    await page.goto('/home');
    await page.waitForURL('**/home');
    
    await page.close();
    return context;
}

export { expect };
