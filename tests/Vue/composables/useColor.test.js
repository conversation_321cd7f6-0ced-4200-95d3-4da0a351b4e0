import useColor from "../../../resources/js/composables/generic/useColor.js";

const { getContrastColor, hex2rgb, rgb2hex } = useColor();

describe('useColor', () => {
    describe('getContrastColor', () => {
        it('should return "false" when input is "null"', () => {
            expect(getContrastColor(null)).toBe(false);
        });
        it('should return "false" when input is "undefined"', () => {
            expect(getContrastColor(undefined)).toBe(false);
        });
        it('should return "false" when input is ""', () => {
            expect(getContrastColor("")).toBe(false);
        });
        it('should return "black" when input is "#ffffff"', () => {
            expect(getContrastColor('#ffffff')).toBe("black");
        });
        it('should return "black" when input is "#00ff00"', () => {
            expect(getContrastColor('#00ff00')).toBe("black");
        });
        it('should return "white" when input is "#000000"', () => {
            expect(getContrastColor('#000000')).toBe("white");
        });
        it('should return "white" when input is "#ff0000"', () => {
            expect(getContrastColor('#ff0000')).toBe("white");
        });
        it('should return "false" when input is "10,15,12"', () => {
            expect(getContrastColor('10,15,12')).toBe(false);
        });
        it('should return "white" when input is "rgb(10,15,12)"', () => {
            expect(getContrastColor('rgb(10,15,12)')).toBe("white");
        });
    });
    describe('rgb2hex', () => {
        it('should return false when input is "null"', () => {
            expect(rgb2hex(null)).toBe(false);
        });
        it('should return false when input is "undefined"', () => {
            expect(rgb2hex(undefined)).toBe(false);
        });
        it('should return false when input is ""', () => {
            expect(rgb2hex("")).toBe(false);
        });
        it('should return false when input is "{r:10, b:10}" (g missing)', () => {
            expect(rgb2hex({r:10, b:10})).toBe(false);
        });
        it('should return false when input is "rgb(10,10)" (r,g or b missing)', () => {
            expect(rgb2hex('rgb(10,10)')).toBe(false);
        });
        it('should return "#ffffff" when input is "rgb(255,255,255)"', () => {
            expect(rgb2hex('rgb(255,255,255)')).toBe("#ffffff");
        });
        it('should return "#000000" when input is "rgb(0,0,0)"', () => {
            expect(rgb2hex('rgb(0,0,0)')).toBe("#000000");
        });
        it('should return "#ff0000" when input is "rgb(255,0,0)"', () => {
            expect(rgb2hex('rgb(255,0,0,0.15)')).toBe("#ff0000");
        });
        it('should return "#00ff00" when input is "rgb(0,255,0)"', () => {
            expect(rgb2hex('rgb(0,255,0)')).toBe("#00ff00");
        });
        it('should return "#0000ff" when input is "rgb(0,0,255)"', () => {
            expect(rgb2hex('rgb(0,0,255)')).toBe("#0000ff");
        });
        it('should return "#0000ff" when input is "rgb(0,0,255)"', () => {
            expect(rgb2hex('rgb(0,0,255)')).toBe("#0000ff");
        });
        it('should return "#0000ff" when input is "rgb(0,0,255)"', () => {
            expect(rgb2hex('rgb(0,0,255)')).toBe("#0000ff");
        });
        it('should return "#0000ff" when input is "rgb(0,0,255)"', () => {
            expect(rgb2hex('rgb(0,0,255)')).toBe("#0000ff");
        });
        it('should return "#0A0F0C" when input is "rgb(10,15,12)"', () => {
            expect(rgb2hex('rgb(10,15,12)')).toBe("#0a0f0c");
        });
    });

    describe('hex2rgb', () => {
        it('should return false when input is "null"', () => {
            expect(hex2rgb(null)).toBe(false);
        });
        it('should return false when input is "undefined"', () => {
            expect(hex2rgb(undefined)).toBe(false);
        });
        it('should return false when input is ""', () => {
            expect(hex2rgb("")).toBe(false);
        });
        it('should return false when input is "123"', () => {
            expect(hex2rgb("123")).toBe(false);
        });
        it('should return "{r:255,g:255,b:255}"" when input is "#ffffff"', () => {
            expect(hex2rgb("#ffffff")).toStrictEqual({r:255,g:255,b:255});
        });
        it('should return "{r:0,g:0,b:0}"" when input is "#000000"', () => {
            expect(hex2rgb("#000000")).toStrictEqual({r:0,g:0,b:0});
        });
        it('should return false when input is "#ff00"', () => {
            expect(hex2rgb("#ff00")).toBe(false);
        });
        it('should return false when input is "#gg0011"', () => {
            expect(hex2rgb("#gg0011")).toBe(false);
        });
        it('should return "{r:255,g:0,b:0}"" when input is "rgb(10,15,12) (already rgb)"', () => {
            expect(hex2rgb("rgb(10,15,12)")).toStrictEqual({r:10,g:15,b:12});
        });
    });

});
