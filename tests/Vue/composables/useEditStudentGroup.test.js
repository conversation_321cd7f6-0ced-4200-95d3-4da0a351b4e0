import { vi } from 'vitest';
import { oneStudentGroup } from "../handlers/mocks/OneStudentGroup.js";

// Mock useToast before importing useEditStudentGroup
vi.mock('../../../resources/js/composables/generic/useToast.js', () => {
    const mockFailToast = vi.fn();
    const mockSuccessToast = vi.fn();
    return {
        default: () => ({
            successToast: mockSuccessToast,
            failToast: mockFailToast
        })
    };
});

// Mock useApi
vi.mock('../../../resources/js/composables/generic/useApi.js', () => {
    const mockApiPost = vi.fn();
    const mockApiGet = vi.fn();
    const mockApiPut = vi.fn();
    const mockApiDel = vi.fn();
    return {
        default: () => ({
            apiGet: mockApiGet,
            apiPost: mockApiPost,
            apiPut: mockApiPut,
            apiDel: mockApiDel
        })
    };
});

// Mock useLang
vi.mock('../../../resources/js/composables/generic/useLang.js', () => {
    return {
        default: () => ({
            ucFirst: vi.fn((str) => str),
            translate: vi.fn((key) => key)
        })
    };
});

// Mock useDateTime
vi.mock('../../../resources/js/composables/generic/useDateTime.js', () => {
    return {
        default: () => ({
            displayDateTime: vi.fn(),
            displayDate: vi.fn()
        })
    };
});

import useEditStudentGroup from "../../../resources/js/composables/useEditStudentGroup";

const flushPromises = require('flush-promises');
const {
    addCourseToStudentGroup,
    addStudentToStudentGroup,
    appointments,
    course,
    getStudentGroup,
    removeCourseFromStudentGroup,
    removeStudentFromStudentGroup,
    saveStudentGroup,
    students,
    studentGroup
} = useEditStudentGroup();

afterEach(() => {
    vi.resetAllMocks();
});

describe('useEditStudentGroup', () => {
    describe('saveStudentGroup', () => {
        it('should call failToast when input is {}', async () => {
            // Get the mocked functions
            const { default: useToast } = await import('../../../resources/js/composables/generic/useToast.js');
            const { default: useApi } = await import('../../../resources/js/composables/generic/useApi.js');
            
            const toastInstance = useToast();
            const apiInstance = useApi();
            
            // Mock the API response to simulate a failed save (no id returned)
            apiInstance.apiPost.mockResolvedValue({
                data: { message: 'Error saving data' } // No id property, which triggers failToast
            });

            await saveStudentGroup({});
            await flushPromises();
            
            // Check that failToast was called
            expect(toastInstance.failToast).toHaveBeenCalled();
        });

        it('should not call failToast when API returns successful response with id', async () => {
            // Get the mocked functions
            const { default: useToast } = await import('../../../resources/js/composables/generic/useToast.js');
            const { default: useApi } = await import('../../../resources/js/composables/generic/useApi.js');
            
            const toastInstance = useToast();
            const apiInstance = useApi();
            
            // Mock the API response to simulate a successful save (with id returned)
            apiInstance.apiPost.mockResolvedValue({
                data: { id: 123 } // Has id property, which should not trigger failToast
            });

            await saveStudentGroup({});
            await flushPromises();
            
            // Check that failToast was NOT called
            expect(toastInstance.failToast).not.toHaveBeenCalled();
        });
    });
    describe('getStudentGroup', () => {
        it ('should fail silently if there is no studentgroup id', async () => {
            studentGroup.value = {};
            await getStudentGroup();
            await flushPromises();
            expect(studentGroup.value).toEqual({});
        });
        it.only('should get a studentgroup and fill properties if studentgroup id > 0', async () => {
            studentGroup.value = {id: 1};
            await getStudentGroup();
            await flushPromises();
            expect(studentGroup.value.id).toEqual(1);
            // expect(studentGroup.value.name).toBe(oneStudentGroup.name);
            // expect(course.value.name).toBe(oneStudentGroup.course.name);
            // expect(appointments.value).toBe(`${oneStudentGroup.future_appointments} / ${oneStudentGroup.appointments}`);
            // expect(students.value.length).toBe(29);
        });
    });
    // describe('addCourseToStudentGroup', () => {
    //     it('should call axios.put with the right parameters', async () => {
    //         studentGroup.value = {id: 1};
    //         await addCourseToStudentGroup(1);
    //         await flushPromises();
    //         expect(axios.put).toHaveBeenCalledWith('/api/studentgroups/1', {courseId: 1});
    //         expect(axios.delete).toHaveBeenCalledWith('/api/removecoursefromstudentgroup/1/1');
    //     });
    // });
    // describe('addStudentToStudentGroup', () => {
    //     it('should call axios.put with the right parameters', async () => {
    //         studentGroup.value = {id: 1};
    //         await addStudentToStudentGroup(1);
    //         await flushPromises();
    //         expect(axios.put).toHaveBeenCalledWith('/api/studentgroups/1', {studentId: 1});
    //     });
    // });
    // describe('removeCourseFromStudengroup', () => {
    //     it('should call axios.delete with the right parameters', async () => {
    //         studentGroup.value = {id: 1};
    //         await removeCourseFromStudentGroup(1);
    //         await flushPromises();
    //         expect(axios.delete).toHaveBeenCalledWith('/api/removecoursefromstudentgroup/1/1');
    //     });
    // });
    // describe('removeStudentFromStudentGroup', () => {
    //     it('should call axios.delete with the right parameters', async () => {
    //         studentGroup.value = {id: 1};
    //         await addStudentToStudentGroup(1);
    //         await flushPromises();
    //         await flushPromises();
    //         expect(axios.delete).toHaveBeenCalledWith('/api/removestudentfromstudentgroup/1/1');
    //     });
    // });
});
