import useDatePicker from "../../../resources/js/composables/generic/useDatePicker.js";

// mock variable lang from useLang to return 'nl'
beforeEach(() => {
    global.window = Object.create(window);
    setLang("nl");
});

const setLang = (lang) => {
    const value = {
        "generic":{
            "language": lang
        }
    };
    Object.defineProperty(window, 'trans', {
        value,
        writable: true,
    });
}

const { dpOptions } = useDatePicker(true);
const { dpOptions: dpOptions2 } = useDatePicker(false);

// Create instances for testing the new functions
const datePickerWholeDay = useDatePicker(true);
const datePickerDateTime = useDatePicker(false);
const datePickerTime = useDatePicker(false, null, true, true);

// fixme: get rid of all those setTimeouts
describe('useDatePicker', () => {
    describe('dpOptions, whole day', () => {
        it('should return nl options when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions.value.locale).toBe('nl');
            }, 1000);
        });
        it('should return "date" label when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions.value.label).toBe('datum');
            }, 1000);
        });
        it('should return en options when lang is "en"', () => {
            setLang("en");
            expect(dpOptions.value.locale).toBe('en-US');
        });
    });
    describe('dpOptions, not whole day', () => {
        it('should return nl options when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions2.value.locale).toBe('nl');
            }, 1000);
        });
        it('should return "date / time" label when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions2.value.label).toBe('datum / tijd');
            }, 1000);
        });
        it('should return en options when lang is "en"', () => {
            setLang("en");
            expect(dpOptions2.value.locale).toBe('en-US');

        });
    });

    describe('dpOptionsTime', () => {
        it('should return time picker options for Dutch', () => {
            setLang("nl");
            expect(datePickerTime.dpOptionsTime.value.locale).toBe('nl-NL');
            expect(datePickerTime.dpOptionsTime.value.clearable).toBe(true);
            expect(datePickerTime.dpOptionsTime.value.textInput).toBe(true);
        });

        it('should return time picker options for English', () => {
            setLang("en");
            // Create new instance after switching language
            const datePickerTimeEn = useDatePicker(false, null, true, true);
            expect(datePickerTimeEn.dpOptionsTime.value.locale).toBe('en-US');
            expect(datePickerTimeEn.dpOptionsTime.value.clearable).toBe(true);
            expect(datePickerTimeEn.dpOptionsTime.value.textInput).toBe(true);
        });
    });
});
