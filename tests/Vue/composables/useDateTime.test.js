import useDateTime from "../../../resources/js/composables/generic/useDateTime.js";
import moment from "moment-timezone";


beforeAll(() => {
    moment.tz.setDefault('Europe/Amsterdam');
});

afterAll(() => {
  moment.tz.setDefault();
});

// mock translation file, located at window.trans (injected from backend)
beforeEach(() => {
    global.window = Object.create(window);
    setLang("nl");
});

const setLang = (lang) => {
    const value = {
        "generic":{
            "language": lang
        }
    };
    Object.defineProperty(window, 'trans', {
        value,
        writable: true,
    });
}

const {
    convertToMysqlDateTime,
    displayDateTime,
    displayDate,
    displayAge,
    displayTime,
    getDateOfWeekByDayName,
    getEndTime,
    getHighestWeekNumber,
    getMonthName,
    getMonthNameOfMondayOfWeekNr,
    getWeekNumber,
    isFutureDate,
    parseDateOnlyToDbFormat,
    dbFormatToDateOnlyValue,
    parseDatePickerValueToDbFormat,
    dbFormatToDatePickerValue
} = useDateTime();

describe('useDateTime', () => {
    describe('displayDateTime', () => {
        const testDate = "2021-01-31 12:34:56";
        const testDate2 = "2021-02-28 12:34:56";
        const testDateNl = "31-01-2021 12:34:56";
        const testDate2GB = "28/02/2021, 12:34:56";

        it('should return "-" when input is null', () => {
            expect(displayDateTime(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(displayDateTime(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(displayDateTime("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(displayDateTime("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(displayDateTime("123456789")).toBe("-");
        });
        it(`should return "${testDateNl.substring(0,16)}" when input is "${testDate}" and language is "nl"`, () => {
            expect(displayDateTime(testDate, false).replaceAll(", ", " ")).toBe(testDateNl.substring(0,16));
        });
        it(`should return "${testDateNl}" when input is "${testDate}" and language is "nl" and showSeconds is "true"`, () => {
            expect(displayDateTime(testDate, true).replaceAll(", ", " ")).toBe(testDateNl);
        });
        // check dislay of dayname
        it(`should return "zo" when input is "${testDate}" and language is "nl" and showDayName is "true"`, () => {
            expect(displayDateTime(testDate, false, true).replaceAll(", ", " ")).toBe("zo 31-01-2021 12:34");
        });
        it(`should return "${testDate2GB.substring(0,16)}" when input is "${testDate2}" and language is "en" and showSeconds is default (false)`, () => {
            // first  change language to en in window trans
            setLang("en");
            expect(displayDateTime(testDate2).replaceAll(", ", " ")).toBe(testDate2GB.substring(0,17).replaceAll(", ", " "));
        });
        it(`should return "${testDate2GB.substring(0,16)}" when input is "${testDate2}" and language is "en" and showSeconds is "false"`, () => {
            setLang("en");
            expect(displayDateTime(testDate2, false).replaceAll(", ", " ")).toBe(testDate2GB.substring(0,17).replaceAll(", ", " "));
        });
        it(`should return "${testDate2GB}" when input is "${testDate2}" and language is "en" and showSeconds is "true"`, () => {
            setLang("en");
            expect(displayDateTime(testDate2, true)).toBe(testDate2GB);
        });
        // check display of dayname
        it(`should return "sun" when input is "${testDate}" and language is "nl" and showDayName is "true"`, () => {
            setLang("en");
            expect(displayDateTime(testDate2, false, true).replaceAll(", ", " ")).toBe("Sun " + testDate2GB.substring(0,17).replaceAll(", ", " "));
        });
    });
    describe('displayDate', () => {
        const testDate = "2021-01-01";
        const testDateShortNl = "01-01-2021";
        it('should return "-" when input is null', () => {
            expect(displayDate(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(displayDate(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(displayDate("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(displayDate("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(displayDate("123456789")).toBe("-");
        });
        it('should return "01-01-2021" when input is "2021-01-01 12:34:56" and language is "nl"', () => {
            expect(displayDate(testDate)).toBe(testDateShortNl);
        });
        it('should return "02-01-2021" when input is "2021-02-01 12:34:56" and language is "en"', () => {
            setLang("en");
            expect(displayDate("2021-02-01 12:34:56")).toBe("02/01/2021");
        });
    });
    describe('displayAge', () => {
        // set test date to be now+ 20 years
        const testDate = moment().subtract(20, 'years').format('YYYY-MM-DD');
        const testDateNL = moment().subtract(20, 'years').format('DD-MM-YYYY');
        const testDateAsObject = new Date(moment().subtract(20, 'years'));


        it('should return "-" when input is null', () => {
            expect(displayAge(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(displayAge(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(displayAge("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(displayAge("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(displayAge("123456789")).toBe("-");
        });
        it(`should return "20" when input is "${ testDate }"`, () => {
            expect(displayAge(testDate)).toBe(20);
        });
        it(`should return "20" when input is "${ testDateNL }"`, () => {
            expect(displayAge(testDateNL)).toBe(20);
        });
        it("should return the correct age when the incoming date is a Date object", () => {
            expect(displayAge(testDateAsObject)).toBe(20);
        });
    });

    describe('isFutureDate', () => {
        // set test date to be now+ 20 years
        const testDate = moment().add(20, 'years').format('YYYY-MM-DD');
        const testDateNL = moment().add(20, 'years').format('DD-MM-YYYY');

        it('should return false when input is null', () => {
            expect(isFutureDate(null)).toBe(false);
        });
        it('should return false when input is undefined', () => {
            expect(isFutureDate(undefined)).toBe(false);
        });
        it('should return false when input is ""', () => {
            expect(isFutureDate("")).toBe(false);
        });
        it('should return false when input is "123"', () => {
            expect(isFutureDate("123")).toBe(false);
        });
        it('should return false when input is "123456789"', () => {
            expect(isFutureDate("123456789")).toBe(false);
        });
        it(`should return true when input is "${testDate}"`, () => {
            expect(isFutureDate(testDate)).toBe(true);
        });
        it(`should return true when input is "${testDateNL}"`, () => {
            expect(isFutureDate(testDateNL)).toBe(true);
        });
    });
    describe('getEndTime', () => {
        it('should return "Invalid date" when input is null', () => {
            expect(getEndTime(null, null)).toBe("Invalid date");
        });
        it('should return "Invalid date" when input is undefined', () => {
            expect(getEndTime(undefined, undefined)).toBe("Invalid date");
        });
        it('should return "Invalid date" when input is ""', () => {
            expect(getEndTime("", "")).toBe("Invalid date");
        });
        it('should return the time when duration part is missing', () => {
            expect(getEndTime("12:00")).toBe("12:00");
        });
        it('should return the time when duration part is not a number', () => {
            expect(getEndTime("12:00", "abc")).toBe("12:00");
        });
        it('should return "13:00" when input is "12:00" and duration is 60', () => {
            expect(getEndTime("12:00", 60)).toBe("13:00");
        });
        it('should return "13:00" when input is "12:00:01" and duration is 60', () => {
            expect(getEndTime("12:00:01", 60)).toBe("13:00");
        });
    });

    describe('convertToMysqlDateTime', () => {
        it('should return null when input is null', () => {
            expect(convertToMysqlDateTime(null)).toBe(null);
        });
        it('should return null when input is undefined', () => {
            expect(convertToMysqlDateTime(undefined)).toBe(null);
        });
        it('should return null when input is ""', () => {
            expect(convertToMysqlDateTime("")).toBe(null);
        });
        it('should return null when input is "123"', () => {
            expect(convertToMysqlDateTime("123")).toBe(null);
        });
        it('should return "2020-12-10" when input is "10-12-2020"', () => {
            expect(convertToMysqlDateTime("10-12-2020")).toBe("2020-12-10");
        });
        it('should return "2020-12-10" when input is "tue 10-12-2020"', () => {
            expect(convertToMysqlDateTime("tue 10-12-2020")).toBe("2020-12-10");
        });
        it('should return "2020-12-10" when input is "Sa 10-12-2020"', () => {
            expect(convertToMysqlDateTime("Sa 10-12-2020")).toBe("2020-12-10");
        });
        it('should return mysql date when input is already in mysql format', () => {
            expect(convertToMysqlDateTime("2020-12-10")).toBe("2020-12-10");
        });
    });

    describe('getWeekNumber', () => {
        it('should return "-" when input is null', () => {
            expect(getWeekNumber(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(getWeekNumber(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(getWeekNumber("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(getWeekNumber("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(getWeekNumber("123456789")).toBe("-");
        });
        it('should return "52" when input is "2020-12-25"', () => {
            expect(getWeekNumber("2020-12-25")).toBe(52);
        });
        it('should return "52" when input is "25-12-2020"', () => {
            expect(getWeekNumber("25-12-2020")).toBe(52);
        });
    });

    describe('getMonthName', () => {
        it('should return "-" when input is null', () => {
            expect(getMonthName(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(getMonthName(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(getMonthName("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(getMonthName("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(getMonthName("123456789")).toBe("-");
        });
        it('should return "Dec" when input is "2020-12-25"', () => {
            expect(getMonthName("2020-12-25")).toBe("Dec");
        });
        it('should return "Dec" when input is "25-12-2020"', () => {
            expect(getMonthName("25-12-2020")).toBe("Dec");
        });
        it('should return "December" when input is "2020-12-25" and short is false', () => {
            expect(getMonthName("2020-12-25", false)).toBe("December");
        });
        it('should return "December" when input is "25-12-2020" and short is false', () => {
            expect(getMonthName("25-12-2020", false)).toBe("December");
        });
        it('should ignore a time part in the string', () => {
            expect(getMonthName("25-12-2020 12:34:56")).toBe("Dec");
        });
    });

    describe('getMonthNameOfMondayOfWeekNr', () => {
        it('should return "-" when input is null', () => {
            expect(getMonthNameOfMondayOfWeekNr(null)).toBe("-");
        });
        it("should return 'Jan' when input is 2021 and weekNr is 2", () => {
            expect(getMonthNameOfMondayOfWeekNr(2021, 2)).toBe("Jan");
        });
        it("should return 'Dec' when input is 2021 although weekNr is 1", () => {
            expect(getMonthNameOfMondayOfWeekNr(2021, 1)).toBe("Dec");
        });
        it("should return 'January' when input is 2021 and weekNr is 2 and short is false", () => {
            expect(getMonthNameOfMondayOfWeekNr(2021, 2, false)).toBe("January");
        });
    });

    describe('getDateOfWeekByDayName', () => {
        it('should return sunday for week 5 of 2021', () => {
            expect(getDateOfWeekByDayName(5, 2021, 'Monday')).toBe("2021-02-01");
        });
        // 2021 has 53 weeks
        it('should return sunday for week 53 of 2020', () => {
            expect(getDateOfWeekByDayName(53, 2020, 'Sunday')).toBe("2021-01-03");
        });
        // the first week of 2021 is week 53 of 2020, so if we ask monday in week 1 of 2021, we should get 2021-01-04
        it('should return monday for week 1 of 2021', () => {
            expect(getDateOfWeekByDayName(1, 2021, 'Monday')).toBe("2021-01-04");
        });
        it("should fail gracefully when weeknumber is too high", () => {
            expect(getDateOfWeekByDayName(54, 2020, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when weeknumber is too low", () => {
            expect(getDateOfWeekByDayName(0, 2020, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when dayname is invalid", () => {
            expect(getDateOfWeekByDayName(1, 2021, 'Funday')).toBe("Invalid date");
        });
        it("should fail gracefully when year is invalid", () => {
            expect(getDateOfWeekByDayName(1, 0, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when year is null", () => {
            expect(getDateOfWeekByDayName(1, null, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when year is not a number", () => {
            expect(getDateOfWeekByDayName(1, 'abc', 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when weekNumber is not a number", () => {
            expect(getDateOfWeekByDayName('abc', 2021, 'Monday')).toBe("Invalid date");
        });
    });

    describe('getHighestWeekNumber', () => {
        it('should return 52 for 2021', () => {
            expect(getHighestWeekNumber(2021)).toBe(52);
        });
        // The last days of december 2020 are in week 53
        it('should return 53 for 2020', () => {
            expect(getHighestWeekNumber(2020)).toBe(53);
        });
        // The last days of december 2024 are in week 1
        it('should return 52 for 2024', () => {
            expect(getHighestWeekNumber(2024)).toBe(52);
        });
    });

    describe('displayTime', () => {
        it("should return empty string when input is null", () => {
            expect(displayTime(null)).toBe("-");
        });
        it("should return empty string when input is unfefined", () =>{
            expect(displayTime(undefined)).toBe("-");
        });
        it("should return time segment of a full date", () =>{
            expect(displayTime("2025-10-10 15:05")).toBe("15:05");
        });
        it("should return time segment of a full date and suppress seconds", () =>{
            expect(displayTime("2025-10-10 15:05:15", true)).toBe("15:05");
        });
        it("should return '-' on not enough characters", () =>{
            expect(displayTime("aap")).toBe("-");
        });
        it("should return time segment of a Date object", () =>{
            expect(displayTime(new Date("Fri Jun 27 2025 16:30:00 GMT+0200")).length).toBe(5);
        });
        it("should return time segment if only the time is in the input and suppress seconds", () =>{
            expect(displayTime("15:05", true)).toBe("15:05");
        });
        it("should return time segment if time without seconds is in the input and do not suppress seconds", () =>{
            expect(displayTime("15:05", false)).toBe("15:05:00");
        });
        it("should return time segment if time with seconds is in the input and do not suppress seconds", () =>{
            expect(displayTime("15:05:00", false)).toBe("15:05:00");
        });
        it("should return time segment if time with seconds is in the input and suppress seconds", () =>{
            expect(displayTime("15:05:00", true)).toBe("15:05");
        });
    });

    // New tests for date format conversion functions
    describe('parseDateOnlyToDbFormat', () => {
        describe('Dutch language (nl)', () => {
            beforeEach(() => {
                setLang("nl");
            });

            it('should convert Dutch date string to DB format', () => {
                expect(parseDateOnlyToDbFormat("17-12-2025")).toBe("2025-12-17");
            });

            it('should convert Dutch date string with single digits', () => {
                expect(parseDateOnlyToDbFormat("01-01-2023")).toBe("2023-01-01");
            });

            it('should handle Date objects', () => {
                const date = new Date(2025, 11, 17); // December 17, 2025
                expect(parseDateOnlyToDbFormat(date)).toBe("2025-12-17");
            });

            it('should handle English format strings (fallback)', () => {
                expect(parseDateOnlyToDbFormat("2025-12-17")).toBe("2025-12-17");
            });

            it('should throw error for invalid Dutch format', () => {
                expect(() => parseDateOnlyToDbFormat("17-12")).toThrow('Invalid Dutch date format: 17-12');
            });

            it('should throw error for invalid date value', () => {
                expect(() => parseDateOnlyToDbFormat("invalid")).toThrow('Invalid date after parsing: invalid');
            });

            it('should throw error for null value', () => {
                expect(() => parseDateOnlyToDbFormat(null)).toThrow('Invalid date value: null');
            });
        });

        describe('English language (en)', () => {
            beforeEach(() => {
                setLang("en");
            });

            it('should convert English date string to DB format', () => {
                expect(parseDateOnlyToDbFormat("2025-12-17")).toBe("2025-12-17");
            });

            it('should convert English date string with single digits', () => {
                expect(parseDateOnlyToDbFormat("2023-01-01")).toBe("2023-01-01");
            });

            it('should handle Date objects', () => {
                const date = new Date(2025, 11, 17); // December 17, 2025
                expect(parseDateOnlyToDbFormat(date)).toBe("2025-12-17");
            });

            it('should throw error for invalid English format', () => {
                expect(() => parseDateOnlyToDbFormat("12-17-2025")).toThrow('Invalid date after parsing: 12-17-2025');
            });
        });
    });

    describe('dbFormatToDateOnlyValue', () => {
        describe('Dutch language (nl)', () => {
            beforeEach(() => {
                setLang("nl");
            });

            it('should convert DB format to Dutch format', () => {
                expect(dbFormatToDateOnlyValue("2025-12-17")).toBe("17-12-2025");
            });

            it('should convert DB format with single digits', () => {
                expect(dbFormatToDateOnlyValue("2023-01-01")).toBe("01-01-2023");
            });

            it("should return empty string for undefined input", () => {
                expect(dbFormatToDateOnlyValue(undefined)).toBe("");
            });

            it('should return empty string for null input', () => {
                expect(dbFormatToDateOnlyValue(null)).toBe("");
            });

            it('should return empty string for empty input', () => {
                expect(dbFormatToDateOnlyValue("")).toBe("");
            });

            it('should handle DB format with time part', () => {
                expect(dbFormatToDateOnlyValue("2025-12-17 14:30:00")).toBe("17-12-2025");
            });
        });

        describe('English language (en)', () => {
            beforeEach(() => {
                setLang("en");
            });

            it('should convert DB format to English format', () => {
                expect(dbFormatToDateOnlyValue("2025-12-17")).toBe("2025-12-17");
            });

            it('should convert DB format with single digits', () => {
                expect(dbFormatToDateOnlyValue("2023-01-01")).toBe("2023-01-01");
            });

            it('should handle DB format with time part', () => {
                expect(dbFormatToDateOnlyValue("2025-12-17 14:30:00")).toBe("2025-12-17");
            });
        });
    });

    describe('parseDatePickerValueToDbFormat', () => {
        describe('Dutch language (nl)', () => {
            beforeEach(() => {
                setLang("nl");
            });

            describe('whole day events', () => {
                it('should convert Dutch date string to DB format with 00:00:00', () => {
                    expect(parseDatePickerValueToDbFormat("17-12-2025", true)).toBe("2025-12-17 00:00:00");
                });

                it('should handle Date objects for whole day', () => {
                    const date = new Date(2025, 11, 17);
                    expect(parseDatePickerValueToDbFormat(date, true)).toBe("2025-12-17 00:00:00");
                });
            });

            describe('datetime events', () => {
                it('should convert Dutch datetime string to DB format', () => {
                    expect(parseDatePickerValueToDbFormat("17-12-2025 14:30", false)).toBe("2025-12-17 14:30:00");
                });

                it('should convert Dutch datetime string without time', () => {
                    expect(parseDatePickerValueToDbFormat("17-12-2025", false)).toBe("2025-12-17 00:00:00");
                });

                it('should handle Date objects for datetime', () => {
                    const date = new Date(2025, 11, 17, 14, 30);
                    expect(parseDatePickerValueToDbFormat(date, false)).toBe("2025-12-17 14:30:00");
                });
            });
        });

        describe('English language (en)', () => {
            beforeEach(() => {
                setLang("en");
            });

            describe('whole day events', () => {
                it('should convert English date string to DB format with 00:00:00', () => {
                    expect(parseDatePickerValueToDbFormat("2025-12-17", true)).toBe("2025-12-17 00:00:00");
                });
            });

            describe('datetime events', () => {
                it('should convert English datetime string to DB format', () => {
                    expect(parseDatePickerValueToDbFormat("2025-12-17 14:30", false)).toBe("2025-12-17 14:30:00");
                });

                it('should convert English datetime string without time', () => {
                    expect(parseDatePickerValueToDbFormat("2025-12-17", false)).toBe("2025-12-17 00:00:00");
                });
            });
        });
    });

    describe('dbFormatToDatePickerValue', () => {
        describe('Dutch language (nl)', () => {
            beforeEach(() => {
                setLang("nl");
            });

            describe('whole day events', () => {
                it("should return a vaild date if the input is a date object and wholeDay = true", () => {
                    expect(dbFormatToDatePickerValue(new Date("Fri Jun 27 2025 16:30:00 GMT+0200"), true)).toBe("27-06-2025");
                });
                it("should return a valid datetime if the input is a date object and wholeDay = false", () => {
                    expect(dbFormatToDatePickerValue(new Date("Fri Jun 27 2025 16:30:00 GMT+0200"), false)).toMatch(/^27-06-2025[, ]+.{5}$/);
                });
                it("should return empty string for empty input", () => {
                    expect(dbFormatToDatePickerValue("", true)).toBe("");
                });
                it('should convert DB format to Dutch date format', () => {
                    expect(dbFormatToDatePickerValue("2025-12-17 00:00:00", true)).toBe("17-12-2025");
                });

                it('should handle DB format without time', () => {
                    expect(dbFormatToDatePickerValue("2025-12-17", true)).toBe("17-12-2025");
                });
            });

            describe('datetime events', () => {
                it('should convert DB format to Dutch datetime format', () => {
                    expect(dbFormatToDatePickerValue("2025-12-17 14:30:00", false)).toBe("17-12-2025 14:30");
                });

                it('should handle DB format without seconds', () => {
                    expect(dbFormatToDatePickerValue("2025-12-17 14:30", false)).toBe("17-12-2025 14:30");
                });
            });
        });

        describe('English language (en)', () => {
            beforeEach(() => {
                setLang("en");
            });

            describe('whole day events', () => {
                it('should convert DB format to English date format', () => {
                    expect(dbFormatToDatePickerValue("2025-12-17 00:00:00", true)).toBe("2025-12-17");
                });
            });

            describe('datetime events', () => {
                it('should convert DB format to English datetime format', () => {
                    expect(dbFormatToDatePickerValue("2025-12-17 14:30:00", false)).toBe("2025-12-17 14:30");
                });
            });
        });
    });

    // Test that existing convertToMysqlDateTime still works (backward compatibility)
    describe('convertToMysqlDateTime (backward compatibility)', () => {
        it('should still work with Dutch format', () => {
            setLang("nl");
            expect(convertToMysqlDateTime("17-12-2025")).toBe("2025-12-17");
        });

        it('should still work with English format', () => {
            setLang("en");
            expect(convertToMysqlDateTime("2025-12-17")).toBe("2025-12-17");
        });

        it('should still handle day names', () => {
            setLang("nl");
            expect(convertToMysqlDateTime("tue 17-12-2025")).toBe("2025-12-17");
        });
    });
});
