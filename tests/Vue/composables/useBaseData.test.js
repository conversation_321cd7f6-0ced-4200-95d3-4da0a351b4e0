import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { nextTick } from 'vue';
import axios from 'axios';

// Setup global environment
beforeAll(() => {
    // Setup translation
    global.window = Object.create(window);
    const value = {
        "generic": {
            "language": "en",
            "errorloadingtutors": "Error loading tutors",
            "errorloadinglocations": "Error loading locations",
            "errorloadingcourses": "Error loading courses",
            "errorloadingrecurrenceoptions": "Error loading recurrence options",
            "errorloadingschoolyears": "Error loading school years",
            "errorloadingdomaininfo": "Error loading domain info",
            "errorloadingcoursegroups": "Error loading course groups",
            "errorloadingstudentgroups": "Error loading student groups",
            "errorloadingstudents": "Error loading students",
            "errorloadingattendanceoptions": "Error loading attendance options",
            "errorloadingactiveusers": "Error loading active users"
        }
    };
    Object.defineProperty(window, 'trans', {
        value,
        writable: true,
    });
});

// Reset mocks and setup before each test
beforeEach(() => {
    vi.resetAllMocks();
    
    // Mock axios
    vi.mock('axios');
    axios.get = vi.fn();
    
    // Setup default mock responses for API calls
    axios.get.mockImplementation((url) => {
        if (url === '/api/gettutors') {
            return Promise.resolve({ data: [{ id: 1, name: 'Tutor 1' }] });
        } else if (url === '/api/courses?excludeArchive=0') {
            return Promise.resolve({ data: { data: [{ id: 1, name: 'Course 1' }] } });
        } else if (url === '/api/locations') {
            return Promise.resolve({ data: { data: [{ id: 1, name: 'Location 1' }] } });
        } else if (url === '/api/students?onlystudents=true&onlyactive=true') {
            return Promise.resolve({ data: { data: [{ id: 1, name: 'Student 1' }] } });
        } else {
            return Promise.resolve({ data: [] });
        }
    });
});

// Import the composable after mocking dependencies
import useBaseData from '../../../resources/js/composables/generic/useBaseData.js';

// Helper function to flush promises
const flushPromises = () => new Promise(resolve => setTimeout(resolve, 0));

describe('useBaseData', () => {
    const { 
        initBaseData, 
        loadingBaseData 
    } = useBaseData();

    // No additional setup needed in the describe block since we're setting up everything in the global beforeEach

    describe('initBaseData', () => {
        it('should set loadingBaseData to true when called', async () => {
            const promise = initBaseData({ tutors: true });
            expect(loadingBaseData.value).toBe(true);
            await promise;
        });

        it('should make API calls for requested data types', async () => {
            // Reset the mock to ensure we start fresh
            axios.get.mockClear();
            
            // Make the API call with a data type that hasn't been requested yet
            // We'll use students since it's not used in other tests
            await initBaseData({ 
                students: true
            });
            
            // Check that the API call was made
            expect(axios.get).toHaveBeenCalledWith('/api/students?onlystudents=true&onlyactive=true');
            expect(axios.get).toHaveBeenCalledTimes(1);
        });

        it('should make API calls for new data types when requested', async () => {
            // Reset the mock to ensure we start fresh
            axios.get.mockClear();
            
            // First call requests a data type that hasn't been requested yet
            // We'll use domain since it's not used in other tests
            await initBaseData({
                domain: true
            });
            
            // Check that the API call was made
            expect(axios.get).toHaveBeenCalledWith('/api/getdomaininfo');
            
            // Reset the mock to clearly see the second call
            axios.get.mockClear();
            
            // Second call requests domain and schoolYears
            // This should make a new API call for schoolYears
            await initBaseData({
                domain: true,
                schoolYears: true
            });
            
            // Check that the schoolYears API call was made
            // We don't check the exact number of calls since the implementation
            // might make additional calls that we're not expecting
            expect(axios.get).toHaveBeenCalledWith('/api/getschoolyears');
        });

        it('should create new promises when forceUpdate is true, even for already loading data', async () => {
            // First call requests tutors
            const promise1 = initBaseData({
                tutors: true
            });
            
            // Reset mock to clearly see the second call
            axios.get.mockClear();
            
            // Second call with forceUpdate requests tutors again
            // This should create a new promise even though tutors are already loading
            const promise2 = initBaseData({
                tutors: true
            }, true);
            
            await Promise.all([promise1, promise2]);
            
            // Should have been called again for tutors due to forceUpdate
            expect(axios.get).toHaveBeenCalledWith('/api/gettutors');
            expect(axios.get).toHaveBeenCalledTimes(1);
        });

        it('should set loadingBaseData to false when all promises are complete', async () => {
            const promise = initBaseData({ 
                tutors: true,
                courses: true 
            });
            
            expect(loadingBaseData.value).toBe(true);
            
            await promise;
            await flushPromises();
            
            expect(loadingBaseData.value).toBe(false);
        });

        it('should handle multiple sequential calls correctly', async () => {
            // Reset the mock to ensure we start fresh
            axios.get.mockClear();
            
            // First call requests activeUsers
            const promise1 = initBaseData({
                activeUsers: true
            });
            
            await promise1;
            
            // Check that the API call was made
            expect(axios.get).toHaveBeenCalledWith('/api/getactiveusers');
            expect(axios.get).toHaveBeenCalledTimes(1);
            
            // Reset mock to clearly see the second call
            axios.get.mockClear();
            
            // Second call after first promise completes requests courseGroups
            const promise2 = initBaseData({
                courseGroups: true
            });
            
            await promise2;
            
            // Should have been called for courseGroups
            expect(axios.get).toHaveBeenCalledWith('/api/coursegroups');
            expect(axios.get).toHaveBeenCalledTimes(1);
        });
    });
});
