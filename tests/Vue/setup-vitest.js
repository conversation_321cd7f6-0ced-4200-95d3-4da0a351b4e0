import { setupServer } from 'msw/node';
import lodash from 'lodash';
import moment from "moment";
import { afterAll, afterEach, beforeAll } from 'vitest';
// Handlers
import { studentGroupsHandlers } from "./handlers/student_groups.js";

// Set up global variables
global._ = lodash;
global.moment = moment;

const server = setupServer(
    ...studentGroupsHandlers
);

// MSW Server Setup
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'warn' });
});

afterEach(() => {
    server.resetHandlers();
});

afterAll(() => {
  server.close();
});
