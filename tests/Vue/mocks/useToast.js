import { vi } from 'vitest';

// Mock implementation of useToast that avoids DOM manipulation and Bootstrap dependency
export default function useToast() {
    // Create spy functions that can be tracked in tests
    const successToast = vi.fn((bodyText, title = '', timeout = 3000) => {
        // Simply record the call, no actual toast creation
        return { bodyText, title, type: 'success', timeout };
    });

    const failToast = vi.fn((bodyText, title = '', timeout = 0) => {
        // Simply record the call, no actual toast creation
        return { bodyText, title, type: 'danger', timeout };
    });

    return {
        successToast,
        failToast
    };
}

