// Mock data for testing useEventWeeks composable

// Mock school year data
export const mockSchoolYear = {
    id: 1,
    start_date: '2024-08-01',
    end_date: '2025-07-31',
    start_year: 2024,
    end_year: 2025,
    label: '2024-2025'
};

// Mock events data - events spanning different weeks and years
export const mockEvents = [
    {
        id: 1,
        start: '2024-09-15T10:00:00', // Week 37 of 2024
        end: '2024-09-15T11:00:00',
        title: 'Event 1'
    },
    {
        id: 2,
        start: '2024-10-20T14:00:00', // Week 42 of 2024
        end: '2024-10-20T15:00:00',
        title: 'Event 2'
    },
    {
        id: 3,
        start: '2024-12-25T09:00:00', // Week 52 of 2024
        end: '2024-12-25T10:00:00',
        title: 'Event 3'
    },
    {
        id: 4,
        start: '2025-01-10T13:00:00', // Week 2 of 2025
        end: '2025-01-10T14:00:00',
        title: 'Event 4'
    },
    {
        id: 5,
        start: '2025-03-15T16:00:00', // Week 11 of 2025
        end: '2025-03-15T17:00:00',
        title: 'Event 5'
    }
];

// Events outside the school year range
export const mockEventsOutsideRange = [
    ...mockEvents,
    {
        id: 6,
        start: '2024-07-15T10:00:00', // Before school year starts
        end: '2024-07-15T11:00:00',
        title: 'Event 6'
    },
    {
        id: 7,
        start: '2025-08-15T14:00:00', // After school year ends
        end: '2025-08-15T15:00:00',
        title: 'Event 7'
    }
];

// Events with gaps in week numbers
export const mockEventsWithGaps = [
    {
        id: 1,
        start: '2024-09-15T10:00:00', // Week 37 of 2024
        end: '2024-09-15T11:00:00',
        title: 'Event 1'
    },
    {
        id: 2,
        start: '2024-11-20T14:00:00', // Week 47 of 2024 (gap between 37 and 47)
        end: '2024-11-20T15:00:00',
        title: 'Event 2'
    },
    {
        id: 3,
        start: '2025-01-10T13:00:00', // Week 2 of 2025 (gap between 47 and 2)
        end: '2025-01-10T14:00:00',
        title: 'Event 3'
    },
    {
        id: 4,
        start: '2025-05-15T16:00:00', // Week 20 of 2025 (gap between 2 and 20)
        end: '2025-05-15T17:00:00',
        title: 'Event 4'
    }
];

// Empty events array
export const mockEmptyEvents = [];
