export const oneStudentGroup = {
  id: 250,
  name: "<PERSON><PERSON><PERSON>",
  students: [
    {
      id: 215,
      domain_id: 1,
      name: "<PERSON><PERSON><PERSON>",
      firstname: "<PERSON><PERSON><PERSON>",
      preposition: "de",
      lastname: "<PERSON><PERSON><PERSON>",
      address: "Rijnstraat 2",
      zipcode: "4024 BW",
      city: "Eck en Wiel",
      date_of_birth: "1963-05-22",
      permission_auto_banktransfer: "<PERSON><PERSON>",
      bankaccount_name: "<PERSON><PERSON> <PERSON>",
      bankaccount_number: "******************",
      mandate_number: "5bae0626c2580",
      remarks: null,
      status: "1",
      accesstoken: "2bf2e0b19c8ae525439137d98aece2bb",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2018-08-31T11:12:23.000000Z",
      updated_at: "2023-08-27T13:04:41.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 215,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 242,
      domain_id: 1,
      name: "<PERSON><PERSON><PERSON>",
      firstname: "Imca",
      preposition: "de",
      lastname: "Haas",
      address: "Frissestein 17",
      zipcode: "4158 GG",
      city: "Deil",
      date_of_birth: "1970-12-18",
      permission_auto_banktransfer: "Nee",
      bankaccount_name: null,
      bankaccount_number: "",
      mandate_number: "5c41057c6d0c5",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "1632634e4dfda43f2308584af4e4c0ca",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2019-01-17T23:45:16.000000Z",
      updated_at: "2023-08-15T14:36:29.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 242,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 240,
      domain_id: 1,
      name: "Claudia Ploegmakers",
      firstname: "Claudia",
      preposition: null,
      lastname: "Ploegmakers",
      address: "Sportveldstraat 84",
      zipcode: "4112 KH",
      city: "Beusichem",
      date_of_birth: "1981-12-05",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "C.J.M. Ploegmakers",
      bankaccount_number: "*****************",
      mandate_number: "5c33a37dc1c81",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "bfaeb4181833d955af530e5bbbcc02ba",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2019-01-07T20:07:41.000000Z",
      updated_at: "2023-08-15T14:37:42.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 240,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 2,
      domain_id: 1,
      name: "Lies van Weverwijk",
      firstname: "Lies",
      preposition: "van",
      lastname: "Weverwijk",
      address: "Diefdijk 44a",
      zipcode: "4143 MG",
      city: "Leerdam",
      date_of_birth: "1945-10-05",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "L. van Weverwijk",
      bankaccount_number: "******************",
      mandate_number: "56d5e50533af6",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "e1f827e85d2818c3adc687e0bde38605",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: null,
      updated_at: "2023-08-15T14:38:59.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 2,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 77,
      domain_id: 1,
      name: "Hetty van de Werken",
      firstname: "Hetty",
      preposition: "van de",
      lastname: "Werken",
      address: "Lage Huis 57",
      zipcode: "4153 CT",
      city: "Beesd",
      date_of_birth: "1967-01-29",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "H.W. van de Werken-van Doesburg",
      bankaccount_number: "******************",
      mandate_number: "588a63a8c3454",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "21170caac963048275b86353b7c7b7c0",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: null,
      updated_at: "2023-08-15T14:38:52.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 77,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 15,
      domain_id: 1,
      name: "Corine Verkou",
      firstname: "Corine",
      preposition: null,
      lastname: "Verkou",
      address: "Vergdstraat 1",
      zipcode: "4158 EE",
      city: "Deil",
      date_of_birth: "1963-04-09",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "A Verkou en/of C.M. Verkou-Keij",
      bankaccount_number: "******************",
      mandate_number: "56e6b39dcd11a",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "642295ad89f5a28c6e1c06bc63aef98a",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: null,
      updated_at: "2023-08-15T14:38:36.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 15,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 14,
      domain_id: 1,
      name: "Arie Verkou",
      firstname: "Arie",
      preposition: null,
      lastname: "Verkou",
      address: "Vergdstraat 1",
      zipcode: "4158 EE",
      city: "Deil",
      date_of_birth: "1959-09-10",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "A Verkou en/of C.M. Verkou-Keij",
      bankaccount_number: "******************",
      mandate_number: "56e6b3267d6cc",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "57162280e1b57e5a6fc668c60d847495",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: null,
      updated_at: "2023-08-15T14:38:39.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 14,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 21,
      domain_id: 1,
      name: "Willeke van Steijn-Bouman",
      firstname: "Willeke",
      preposition: "van",
      lastname: "Steijn-Bouman",
      address: "P. Baltusstraat 7",
      zipcode: "4153 CR",
      city: "Beesd",
      date_of_birth: "1966-09-28",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "W.J.M. van Steijn-Bouman",
      bankaccount_number: "******************",
      mandate_number: "56fe57cd3d3cb",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "ba317c16085f5d320aeefd22a7aa0cfe",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: null,
      updated_at: "2022-08-18T15:22:30.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 21,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 128,
      domain_id: 1,
      name: "Ramon Schreuders",
      firstname: "Ramon",
      preposition: null,
      lastname: "Schreuders",
      address: "Prins Willem Alexandersingel 32",
      zipcode: "4153 BJ",
      city: "Beesd",
      date_of_birth: "1997-05-31",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "A.M.A. Schreuders",
      bankaccount_number: "NL40 RABO 0167 8919 79",
      mandate_number: "59cd1a4fc5697",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "9c8de9a432522d95f86b5d4231ef34ee",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2017-09-28T17:50:39.000000Z",
      updated_at: "2023-11-08T09:53:53.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 128,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 257,
      domain_id: 1,
      name: "Dani\u00ebl Scheele",
      firstname: "Dani\u00ebl",
      preposition: null,
      lastname: "Scheele",
      address: "Perosistraat 20",
      zipcode: "4102 BH",
      city: "Culemborg",
      date_of_birth: "1991-08-10",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "D.E.Scheele",
      bankaccount_number: "NL53 RABO 0106 4798 81",
      mandate_number: "5c4cd7b0ccbd3",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "e6fd95df9e4b3eaea3fb94ddf7186049",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2019-01-26T22:57:04.000000Z",
      updated_at: "2023-11-08T10:27:39.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 257,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 207,
      domain_id: 1,
      name: "Jacqueline Frasa",
      firstname: "Jacqueline",
      preposition: null,
      lastname: "Frasa",
      address: "Bingse Boogaard 18",
      zipcode: "4121ED",
      city: "Everdingen",
      date_of_birth: "1964-11-29",
      permission_auto_banktransfer: "Nee",
      bankaccount_name: null,
      bankaccount_number: "",
      mandate_number: "5b7b178f54e44",
      remarks: null,
      status: "1",
      accesstoken: "f6e2ec68dffef0e70cef9faada78d423",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2018-08-20T21:33:34.000000Z",
      updated_at: "2023-08-15T14:36:22.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 207,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 276,
      domain_id: 1,
      name: "Petra Kusters",
      firstname: "Petra",
      preposition: null,
      lastname: "Kusters",
      address: "Sophiestraat 22",
      zipcode: "4191 GH",
      city: "Geldermalsen",
      date_of_birth: "1975-06-29",
      permission_auto_banktransfer: "Nee",
      bankaccount_name: null,
      bankaccount_number: "",
      mandate_number: "5d28e6e0630ac",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "1a89912e982f4bd1b17582a7ba9b3cfe",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2019-07-12T22:00:32.000000Z",
      updated_at: "2022-08-15T18:48:59.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 276,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 272,
      domain_id: 1,
      name: "Ton den Braven",
      firstname: "Ton",
      preposition: "den",
      lastname: "Braven",
      address: "F.L. Blomlaan 59",
      zipcode: "4143 CW",
      city: "Leerdam",
      date_of_birth: "1959-09-01",
      permission_auto_banktransfer: "Nee",
      bankaccount_name: null,
      bankaccount_number: "",
      mandate_number: "5d6113dde691c",
      remarks: null,
      status: "1",
      accesstoken: "8c33d59526c65aea8b67c9fdcc5559b1",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2019-06-20T13:50:22.000000Z",
      updated_at: "2023-08-15T14:35:35.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 272,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 395,
      domain_id: 1,
      name: "Ellemieke de Vaal",
      firstname: "Ellemieke",
      preposition: "de",
      lastname: "Vaal",
      address: "Voorstraat 9",
      zipcode: "4153 AH",
      city: "Beesd",
      date_of_birth: "1975-05-21",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "EMJ de Vaal van Beurden",
      bankaccount_number: "******************",
      mandate_number: "611fac724f0e8",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "0923d486af5fafbf9493e571fc19971b",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2021-08-20T13:21:54.000000Z",
      updated_at: "2023-08-15T14:38:26.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 395,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 427,
      domain_id: 1,
      name: "Elvira van Vugt",
      firstname: "Elvira",
      preposition: "van",
      lastname: "Vugt",
      address: "Nieuwe Steeg 11",
      zipcode: "4197 RD",
      city: "Buurmalsen",
      date_of_birth: "1969-09-24",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "E. van Vugt",
      bankaccount_number: "NL71 RBRB 0658 6103 25",
      mandate_number: "619270558ff54",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "3ec0accb77be0105571284e8b35f737a",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2021-10-11T12:29:24.000000Z",
      updated_at: "2023-08-27T10:20:22.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 427,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 389,
      domain_id: 1,
      name: "Jan van Vugt",
      firstname: "Jan",
      preposition: "van",
      lastname: "Vugt",
      address: "Korfgraaf 13",
      zipcode: "4174GL",
      city: "Hellouw",
      date_of_birth: "1966-08-27",
      permission_auto_banktransfer: "Nee",
      bankaccount_name: null,
      bankaccount_number: "",
      mandate_number: "60eb4f5e4e059",
      remarks: null,
      status: "1",
      accesstoken: "d0c4c355fa876755dd797964dd0b5f5f",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2021-07-05T12:02:55.000000Z",
      updated_at: "2023-08-15T14:38:46.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 389,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 489,
      domain_id: 1,
      name: "River de Cocq van Delwijnen",
      firstname: "River",
      preposition: "de",
      lastname: "Cocq van Delwijnen",
      address: "Het Aarkeland 24",
      zipcode: "5328 GR",
      city: "Rossum",
      date_of_birth: "1996-10-23",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "S. de Cocq van Delwijnen",
      bankaccount_number: "NL98 INGB 0003 8278 16",
      mandate_number: "62594d4376e35",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "f85313b58e1735073bd7033825add3eb",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2022-04-15T10:47:31.000000Z",
      updated_at: "2023-08-15T14:35:42.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 489,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 470,
      domain_id: 1,
      name: "Genca Pasial",
      firstname: "Genca",
      preposition: null,
      lastname: "Pasial",
      address: "Bolwerk 10",
      zipcode: "4194 CN",
      city: "Meteren",
      date_of_birth: "1994-08-30",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "Mw MMA Pasial",
      bankaccount_number: "NL19 INGB 0102 3404 98",
      mandate_number: "62251745e13b0",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "2a12871ae5b85d33395c579d56db4b38",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2022-03-06T20:19:17.000000Z",
      updated_at: "2023-11-08T09:23:35.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 470,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 408,
      domain_id: 1,
      name: "Natasja Verwolf-Krijgsman",
      firstname: "Natasja",
      preposition: null,
      lastname: "Verwolf-Krijgsman",
      address: "Achterweg 12",
      zipcode: "4156 AC",
      city: "Rumpt",
      date_of_birth: "1975-10-17",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "N.S. Krijgsman",
      bankaccount_number: "NL20 INGB **********",
      mandate_number: "612fe07b8cd8e",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "1b124254a3b271ffb4d058616012c890",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2021-09-01T20:20:11.000000Z",
      updated_at: "2023-08-28T15:13:40.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 408,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 544,
      domain_id: 1,
      name: "Chiel Koudijs",
      firstname: "Chiel",
      preposition: null,
      lastname: "Koudijs",
      address: "Middenstraat 36",
      zipcode: "4153 AD",
      city: "Beesd",
      date_of_birth: "1951-03-30",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "M.Koudijs Chiel Bouw en Advies",
      bankaccount_number: "NL31 RABO 0167 1987 85",
      mandate_number: "63643c9b1dd74",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "66588f35adfaec834ea8c8c80c5a7423",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2022-11-03T22:11:39.000000Z",
      updated_at: "2023-11-08T09:39:21.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 544,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 559,
      domain_id: 1,
      name: "Sija van Zandwijk",
      firstname: "Sija",
      preposition: "van",
      lastname: "Zandwijk",
      address: "Provincialeweg West 3a",
      zipcode: "4155 BJ",
      city: "Gellicum",
      date_of_birth: "1964-11-19",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "S van Zandwijk",
      bankaccount_number: "******************",
      mandate_number: "63c005f0b8f11",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "1296e96cc93f9b222c3d5ec426d518cf",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-01-12T13:06:56.000000Z",
      updated_at: "2023-08-15T14:39:09.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 559,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 466,
      domain_id: 1,
      name: "Lianne den Otter",
      firstname: "Lianne",
      preposition: "den",
      lastname: "Otter",
      address: "Middenstraat 5",
      zipcode: "4156 AG",
      city: "Rumpt",
      date_of_birth: "1977-11-17",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "J.C. Den Otter - Bijleveld",
      bankaccount_number: "NL42 INGB 0754 7423 93",
      mandate_number: "62cf16c968a97",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "3cedb882353e11c3be6038c879bbe751",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2022-02-28T14:01:53.000000Z",
      updated_at: "2023-08-16T15:02:19.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 466,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 560,
      domain_id: 1,
      name: "Diana Wouters",
      firstname: "Diana",
      preposition: null,
      lastname: "Wouters",
      address: "JH Derksenstraat 83",
      zipcode: "4194 WH",
      city: "Meteren",
      date_of_birth: "1958-04-02",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "D Wouters",
      bankaccount_number: "******************",
      mandate_number: "63c00bbf72743",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "4075cc4899d478967521181eefde14d6",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-01-12T13:31:43.000000Z",
      updated_at: "2023-08-15T14:39:06.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 560,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 563,
      domain_id: 1,
      name: "Marceline Bonouvri\u00e9",
      firstname: "Marceline",
      preposition: null,
      lastname: "Bonouvri\u00e9",
      address: "A.H.H. Tolhuisenstraat 11",
      zipcode: "4194 VD",
      city: "Meteren",
      date_of_birth: "1995-07-29",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "M. Bonovrie",
      bankaccount_number: "******************",
      mandate_number: "63c04517dfcef",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "5140187a54e83b976d0d6cb6ff1f20f1",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-01-12T17:36:23.000000Z",
      updated_at: "2023-08-15T14:35:19.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 563,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 628,
      domain_id: 1,
      name: "Claire Brabander-van Driel",
      firstname: "Claire",
      preposition: null,
      lastname: "Brabander-van Driel",
      address: "Voorburcht 5",
      zipcode: "4194 AW",
      city: "Meteren",
      date_of_birth: "1988-03-14",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "C. van Driel",
      bankaccount_number: "NL43 ABNA 0500 9591 96",
      mandate_number: "64b6828b87111",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "bb3c0f8c11406a05e419934b26352ad4",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-07-18T14:16:11.000000Z",
      updated_at: "2023-08-26T15:53:38.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 628,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 627,
      domain_id: 1,
      name: "Hetty Roks-van Zanten",
      firstname: "Hetty",
      preposition: null,
      lastname: "Roks-van Zanten",
      address: "Lage Huis 31",
      zipcode: "4153 CS",
      city: "Beesd",
      date_of_birth: "1958-07-03",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "W.W. Roks en/of H.J.J. Roks-van Zanten",
      bankaccount_number: "NL28 RABO 0304 0065 72",
      mandate_number: "64ae4e6a31a0e",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "6b1e1d27c8e48fba50679bd52e82dea8",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-07-12T08:55:38.000000Z",
      updated_at: "2023-08-21T17:23:07.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 627,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 626,
      domain_id: 1,
      name: "Ilse van Dalen",
      firstname: "Ilse",
      preposition: "van",
      lastname: "Dalen",
      address: "Esdoornstraat 20",
      zipcode: "4191 KR",
      city: "Geldermalsen",
      date_of_birth: "1980-02-02",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "RJ Boesten eo I van Dalen",
      bankaccount_number: "NL29RABO **********",
      mandate_number: "64ae4cdd6b3d2",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "b1800f37e889a64355fae5dab4ed3cd7",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-07-12T08:49:01.000000Z",
      updated_at: "2023-07-12T08:49:01.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 626,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 641,
      domain_id: 1,
      name: "Nelly de Goeij-Van Tilburg",
      firstname: "Nelly",
      preposition: "de",
      lastname: "Goeij-Van Tilburg",
      address: "Schaikseweg 68",
      zipcode: "4143 HH",
      city: "Leerdam",
      date_of_birth: "1965-04-18",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "W. A. A de Goeij eo N.J. de Goeij Van Tilburg",
      bankaccount_number: "NL63 RABO 0336 0348 57",
      mandate_number: "64e757ff749e6",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "073b67a4009468961af264bc30064062",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 1,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-08-24T15:15:43.000000Z",
      updated_at: "2023-08-24T15:33:51.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 641,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
    {
      id: 635,
      domain_id: 1,
      name: "Karla Haas",
      firstname: "Karla",
      preposition: null,
      lastname: "Haas",
      address: "Luchtenburgstraat 5",
      zipcode: "4158 DA",
      city: "Deil",
      date_of_birth: "1980-08-15",
      permission_auto_banktransfer: "Ja",
      bankaccount_name: "K. Haas-Rutten",
      bankaccount_number: "NL40 RABO 0360 2160 13",
      mandate_number: "650065a860cd8",
      remarks: null,
      status: "ACTIVE",
      accesstoken: "1fe9086449995111b19f5ce0d811810a",
      apipin: null,
      has_access: 0,
      agreeSocialShare: 0,
      min_participants: 2,
      max_participants: 99,
      created_at: "2023-08-19T23:58:29.000000Z",
      updated_at: "2023-09-12T15:20:40.000000Z",
      pivot: {
        studentgroup_id: 250,
        student_id: 635,
        as_trial_student: 0,
        start_date: "2016-01-01",
        end_date: null,
      },
    },
  ],
  course: {
    id: 19,
    domain_id: 1,
    variant_code: null,
    coursegroup_id: 2,
    recurrenceoption_id: 2,
    is_trial_course: null,
    name: "Zang (gevorderd) Popkoor",
    price_ex_tax: "27.48",
    price_invoice: "33.50",
    price_ex_tax_sub_adult: "0.00",
    price_is_per: "month",
    tax_rate: 21,
    archive: 0,
    group_size_min: 2,
    group_size_max: 35,
    created_at: null,
    updated_at: "2023-08-08T20:04:05.000000Z",
    pivot: {
      student_id: 250,
      course_id: 19,
      id: 692,
      start_date: "2016-01-01",
      end_date: null,
      signed: null,
      sign_request_send: null,
      checklist_id: null,
      status: 1,
      please_keep_scheduled_time: null,
      created_at: "2019-01-25T19:42:37.000000Z",
      updated_at: "2019-01-25T19:42:37.000000Z",
    },
    recurrenceoption: {
      id: 2,
      domain_id: 1,
      description: "90 minuten per week tot uitschrijven (doorlopend)",
      nr_of_times: 90,
      timeunit: "minutes",
      per_interval: "week",
      ends_after_nr_of_occurrences: null,
      created_at: null,
      updated_at: null,
    },
  },
  future_appointments: 24,
  all_appointments: 200,
  appointments: 40,
};
