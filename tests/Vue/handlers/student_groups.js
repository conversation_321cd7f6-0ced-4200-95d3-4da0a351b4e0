import { http, HttpResponse } from "msw";
import { oneStudentGroup } from "./mocks/OneStudentGroup.js";
import * as studentGroup from "date-fns/locale";

export const studentGroupsHandlers = [
    http.get(/\/api\/studentgroups/, () => {
        return HttpResponse(200, { data: oneStudentGroup });
    }),

    http.get('/posts/:id', ({params}) => {
        console.log("id", params.id);
    }),

    http.get(/\/api\/studentgroups\/:id/, ({request}) => {
        console.log("id:", request);
        return HttpResponse(200, { data: oneStudentGroup });
    })
];
