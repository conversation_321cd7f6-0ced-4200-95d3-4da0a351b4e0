<?php

namespace Tests\Models;

use App\Models\DateException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class DateExceptionTest extends TestCase
{
    use RefreshDatabase;


    /**
     * Tests the isBlocking method in the DateException class
     */
    public function testIsBlocking(): void
    {
        // Create a DateException
        $dateException = DateException::factory()->create();

        // Mock the Auth facade to return a custom object
        Auth::shouldReceive('user')
            ->andReturn((object)[
                'id' => 1,
                'name' => 'Test User',
                'domain' => (object)['id' => 1],
            ]);
            
        // Mock the Log facade
        Log::fake();

        // Use the isBlocking method and check for blocking cases
        $date = '2023-12-10';
        $time = '08:00';
        $locationId = 1;
        $tutorId = 1;

        // Call the method
        $results = DateException::isBlocking($date, $time, $locationId, $tutorId);

        // Check if the returned result is an array
        $this->assertIsArray($results);

        // Check if the blocking DateException is found when a blocking situation takes place
        // More robust checks can be added here based on specific requirements of the isBlocking function
        $this->assertContains($dateException->reason, $results);

        // Check if a log was created
        Log::assertLogged('info', function ($message){
            return str_contains($message, "blocking date exception");
        });
    }
}
