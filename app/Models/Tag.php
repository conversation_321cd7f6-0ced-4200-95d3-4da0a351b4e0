<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Tag extends Model
{
    protected $fillable = ['name', 'domain_id'];

    /**
     * Set standard filtering on queries:
     * makes sure only documents from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    /**
     * Get the documents that have this tag
     */
    public function documents()
    {
        return $this->belongsToMany(Document::class, 'document_tag');
    }

    /**
     * Get the domain this tag belongs to
     */
    public function domain()
    {
        return $this->belongsTo(Domain::class);
    }
}
