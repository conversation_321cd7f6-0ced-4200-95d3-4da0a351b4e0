<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Document extends Model {
    /**
     * Set standard filtering on queries:
     * makes sure only documents from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['documents.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    /**
     * n:m with libraries
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function libraries() {
        return $this->belongsToMany(Library::class,
            "library_document", "document_id", "library_id");
    }

    public function event() {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the tags for this document
     */
    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'document_tag');
    }

    /**
     * Get the domain this document belongs to
     */
    public function domain()
    {
        return $this->belongsTo(Domain::class);
    }
}
