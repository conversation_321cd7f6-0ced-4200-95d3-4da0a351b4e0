<?php

namespace App\Models;

use Carbon\Carbon;
use Carbon\Doctrine\CarbonType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Schoolyear extends Model
{

    use HasFactory;

    /**
     * Set standard filtering on queries:
     * makes sure only schoolyears from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery()
    {
        $query = parent::newQuery();
        if (!Auth::guest()) {
            $query->where([
                ['schoolyears.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function timetables()
    {
        return $this->hasMany(Timetable::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dateExceptions()
    {
        return $this->hasMany(DateException::class);
    }

    /**
     * find the current schoolyear or, if not found the nearest future schoolyear
     * if both options fail, return null
     *
     * @param int $domainid if we have a logged in user, we use that users domain id
     * @param string $fallback ['past' | 'future' | 'fail'] what to do if we can't find a current school year
     * @return \Illuminate\Database\Eloquent\Collection|null|static[]
     * @throws \Exception
     */
    public static function getCurrentOrFuture($domainid = 0, $fallback = 'future')
    {
        $domainIdForLookup = Auth::guest() ? $domainid : Auth::user()->domain_id;

        Log::info("Getting last schoolyear for planning for domain $domainIdForLookup");
        $now = new \DateTime();

        $schoolyear = Schoolyear::where([
            ["start_date", "<=", $now->format("Y-m-d")],
            ["end_date", ">=", $now->format("Y-m-d")],
            ["domain_id", "=", $domainIdForLookup]
        ])->get();

        if ($schoolyear->count() == 0) {
            if ($fallback === 'past') {
                Log::info("no current schoolyear, trying to find past");
                // not current, then try to find next years schoolyear
                // find the schoolear where start_date = this year and end date = next year
                $thisYear = $now->format("Y");
                $lastYear = (string)(intval($thisYear) - 1);
                $schoolyear = Schoolyear::where([
                    ['start_year', '=', $lastYear],
                    ['end_year', '=', $thisYear],
                    ["domain_id", "=", $domainIdForLookup]
                ])->get();
            } else {
                if ($fallback === 'future') {
                    Log::info("no current schoolyear, trying to find future");
                    // not current, then try to find next years schoolyear
                    // find the schoolear where start_date = this year and end date = next year
                    $thisYear = $now->format("Y");
                    $nextYear = (string)(intval($thisYear) + 1);
                    $schoolyear = Schoolyear::where([
                        ['start_year', '=', $thisYear],
                        ['end_year', '=', $nextYear],
                        ["domain_id", "=", $domainIdForLookup]
                    ])->get();
                }
            }
        }

        if ($schoolyear->count() == 1) {
            Log::info("Found schoolyear: " . $schoolyear[0]->label . " (id: " . $schoolyear[0]->id . ")");
            return $schoolyear[0];
        } else {
            Log::warning("Did not find a schoolyear!");
        }
        return null;
    }

    /**
     * Checks if today is between the start and end date of two school years
     * if so, return the end of previous school year +1 day and start of next school year -1 day
     * @param \DateTime $toggleDate
     * @return array [start, end] or null
     */
    public static function getDatesBetweenSchoolYears(\DateTime $toggleDate)
    {
        $toggleDT = new \DateTime($toggleDate);

        // this only works if the toggleDate is between two school years.
        // so check if we can -not- find a school year that starts before toggleDate en ends after toggleDate
        // i.e. the toggleDate is between two school years
        $schoolyearThatAreUnderWay = Schoolyear::query()
            ->where("start_date", "<=", $toggleDT->format("Y-m-d"))
            ->where("end_date", ">=", $toggleDT->format("Y-m-d"))
            ->count();
        if ($schoolyearThatAreUnderWay > 0) {
            return null;
        }
        // get the school year that has end_date closest to the current date
        $schoolyear = Schoolyear::where("end_date", "<=", $toggleDT->format("Y-m-d"))
            ->orderBy("end_date", "desc")
            ->first();
        // get school year that has start_date closest to the current date
        $schoolyear2 = Schoolyear::where("start_date", ">=", $toggleDT->format("Y-m-d"))
            ->orderBy("start_date", "asc")
            ->first();
        // has the second schoolyear not started yet?
        if ($schoolyear2 && $schoolyear2->start_date > $toggleDT->format("Y-m-d")) {
            return [$schoolyear->end_date, $schoolyear2->start_date];
        }
        return null;
    }


    /**
     * get the (one) schoolyear that has this year as start year
     * @param string $startYear
     *
     * @return \Illuminate\Database\Eloquent\Collection|null|static[]
     */
    public static function getByStartyear($startYear)
    {
        $schoolyear = Schoolyear::where('start_year', "=", $startYear)->get();
        // only one may be returned!
        return ((($schoolyear->count() > 0) && ($schoolyear->count() < 2)) ? $schoolyear[0] : null);
    }

    /**
     * Check if we are in de second half of the year.
     * If so, choose the previous schoolyear.
     * If not choose the current schoolyear.
     * The aim is to get the last schoolyear that has a planning
     * This can be used as a template for the new planning
     * also: only look at the last 6 month, as changes in the schedule are
     * often around halfway through the schoolyear
     * @param int $registrationId
     * @param int $domainId
     * @return array
     */
    public static function getLastSchoolyearForPlanning($registrationId = 0, $domainId = 0)
    {
        $domainIdForLookup = Auth::guest() ? $domainId : Auth::user()->domain_id;
        // see if the registration is for a group course instead of individual
        $reg = Registration::findOrFail($registrationId);
        $sgs = $reg->student->studentgroups;
        $courseId = $reg->course->id;
        $coursesInStudentGroup = [];
        foreach ($sgs as $studentgroup) {
            Log::debug("Studentgroup: " . $studentgroup->id);
            $course = $studentgroup->courses;
            if ($course->count() == 0) {
                continue;
            }
            $coursesInStudentGroup[$studentgroup->id] = $course[0]->id;
        }
        // if the value is in the array, return the key, which is the student group id
        $key = array_search($courseId, $coursesInStudentGroup);
        if ($key !== false) {
            Log::info("Registration $registrationId belongs to student group. Looking for key: $key");
            // find the correct student group
            $theStudentgroup = $sgs->filter(function ($row) use ($key) {
                return $row->id === $key;
            })->first();

            if (empty($theStudentgroup)) {
                Log::warning("Studentgroup not found");
            } elseif (empty($theStudentgroup->courses)) {
                Log::warning("Studentgroup has no courses");
            } else {
                // overwrite the registration id to use the groups registration
                $registrationId = $theStudentgroup->courses->first()->pivot->id;
                Log::info("Registration of student group is $registrationId");
            }
        }

        // get last planned in event
        // also number of occurrences filters out an incidental change in the schedule
        $q = "SELECT dayname(e.datetime) as dayname, time(e.datetime) as time, count(e.id) as count " .
            "FROM events e " .
            "LEFT JOIN timetables t ON e.timetable_id = t.id " .
            "LEFT JOIN course_student cs ON t.course_student_id = cs.id " .
            "LEFT JOIN students s ON cs.student_id = s.id " .
            "WHERE t.course_student_id = :registrationId " .
            "AND e.datetime >= (NOW() - INTERVAL 6 MONTH) " .
            "AND s.domain_id = :domainid " .
            "GROUP BY dayname(e.datetime), time(e.datetime) " .
            "ORDER BY count(e.id) DESC LIMIT 1";
        $results = DB::select($q, ['registrationId' => $registrationId, 'domainid' => $domainIdForLookup]);

        // the first row contains the most occurring day / time
        return $results;
    }

    /**
     * Check if the given date range overlaps with existing school years
     *
     * @param string $startDate
     * @param string $endDate
     * @param int $domainId
     * @param int|null $excludeId School year ID to exclude from overlap check (for updates)
     * @return bool
     */
    public static function hasOverlapWithExistingSchoolyears($startDate, $endDate, $domainId, $excludeId = null)
    {
        $query = static::query()
            ->where("domain_id", $domainId);

        if ($excludeId) {
            $query->where("id", "!=", $excludeId);
        }
        DB::enableQueryLog();

        $existingSchoolyears = $query
            ->where(function ($query) use ($startDate, $endDate) {
                $query->where(function ($q) use ($startDate) {
                    $q->where("start_date", "<=", $startDate)
                        ->where("end_date", ">=", $startDate);
                })
                    ->orWhere(function ($q) use ($endDate) {
                        $q->where("start_date", "<=", $endDate)
                            ->where("end_date", ">=", $endDate);
                    });
            })
            ->get();
        $log = DB::getQueryLog();
        DB::disableQueryLog();

        Log::debug(print_r($log, true));

        if ($existingSchoolyears->count() > 0) {
            Log::error("Overlap with existing school years: " . $existingSchoolyears->pluck('label')->implode(', '));
            return true;
        }

        return false;
    }

    /**
     * top 2 schoolyears
     * @return mixed
     */
    public static function getSchoolyearsForPlanningAssistant()

    {
        return Schoolyear::orderBy('end_date', 'desc')
            ->limit(2)
            ->get();
    }

    /**
     * get the events on the planning of the last four weeks that are a series
     * then un-double the appointments to creat a list 'current planning'
     * @param $schoolyear
     * @return array
     */
    public static function getPlanning($schoolyear)
    {
        $scheduleThreshold = Auth::user()->domain->schedule_threshold;
        $q = "select distinct t.course_student_id, ev.tutor_id, ev.location_id, c.recurrenceoption_id, cs.course_id, " .
            "st.date_of_birth, st.firstname, max(ev.datetime) as plandatetime " .
            "from events ev " .
            "left join timetables t on ev.timetable_id = t.id " .
            "left join schoolyears s on t.schoolyear_id = s.id " .
            "left join course_student cs on t.course_student_id = cs.id " .
            "left join courses c on cs.course_id = c.id " .
            "left join students st on cs.student_id = st.id " .
            "left join recurrenceoptions r on c.recurrenceoption_id = r.id " .
            "where t.schoolyear_id = :schoolyear " .
            // omit time component of date by casting to date
            "and (CAST(ev.datetime as DATE) BETWEEN date_sub(s.end_date, INTERVAL :scheduleThreshold MONTH ) AND s.end_date) " .
            "and (c.is_trial_course IS NULL OR c.is_trial_course = 0) " .
            "and (r.ends_after_nr_of_occurrences IS NULL OR r.ends_after_nr_of_occurrences > 1) " .
            "group by t.course_student_id, ev.tutor_id, ev.location_id, c.recurrenceoption_id, st.date_of_birth, st.firstname " .
            "order by plandatetime";
        return DB::select($q, [
            'schoolyear' => $schoolyear,
            'scheduleThreshold' => $scheduleThreshold
        ]);
    }
}
