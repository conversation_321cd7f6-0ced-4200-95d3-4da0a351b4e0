<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TitleFromUrlMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the first segment of the URI path
        $basepart = $request->segment(1);

        // Build the title
        $title = 'CLASS 4';
        if (!empty($basepart)) {
            // replace '-' with spaces to make it more readable
            $basepart = str_replace("-", " ", $basepart);
            $title .= ' - ' . ucfirst($basepart);
        }

        // Share the title with all views
        view()->share('pageTitle', $title);

        return $next($request);
    }
}
