<?php

namespace App\Http\Controllers;

use App\Http\Requests\AnalysePlanDataRequest;
use App\Http\Requests\NewEventsRequest;
use App\Traits\LocationOccupationTrait;
use App\Models\Course;
use App\Models\Event;
use App\Models\Registration;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Studentgroup;
use App\Models\Task;
use App\Models\Timetable;
use App\Models\Tutor;
use App\Models\Location;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\LocaleDate;

class PlanningController extends Controller
{
    use LocationOccupationTrait;

    ////// LAYERED PLANNING //////

    public function lessonplanning()
    {
        return view("planning.lessonplanning");
    }


    /**
     * Return array of an event based on the request data
     * @return array
     */
    private function generateEventArray($dateTime, $request, $tutor, $location)
    {
        return [
            "tutor_id" => $request->tutorId,
            "tutor_name" => $tutor->name,
            "location_id" => $location->id,
            "location_name" => $location->name,
            "starttime" => $dateTime->format('H:i'),
            "daynumber" => $dateTime->format('N'),
            "oddeven" => $dateTime->format('W') % 2 === 0 ? "even" : "odd"
        ];
    }

    /**
     * Determine remarks for the event based on the request data
     * Will set the severity of the remark to one of the following: blocking, warning, info
     * @param $dateTime \DateTime
     * @param $tutor Tutor
     * @param $location Location
     * @param $altLocation Location|null
     * @param $duration int in minutes!
     * @param $isAvailableWorkSchedule bool
     * @param $message string
     * @return array
     */
    private function determineRemarksForConflicts($dateTime, $tutor, $location, $altLocation, $duration, $isAvailableWorkSchedule, $message)
    {
        $remarks = [];

        if (!$isAvailableWorkSchedule) {
            $remarks[] = [
                "severity" => "blocking",
                "description" => ucfirst(trans("generic.tutornotavailableaccordingtoschedule")) . "-1"
            ];
        }

        $tutorOccupation = Tutor::isOccupiedOn(
            $tutor->id,
            $dateTime->format('Y-m-d'),
            $dateTime->format('H:i'),
            $duration
        );
        if (count($tutorOccupation) > 0) {
            $remarks[] = [
                "severity" => "warning",
                "description" => ucfirst(trans("generic.tutornotavailablehasevent"))
            ];
        }

        $locationVerdict = $this->getLocationAvailability(
            $location->getFutureOccupied(),
            $altLocation ? $altLocation->getFutureOccupied() : [],
            $dateTime,
            $duration   // should be in minutes!
        );
        if ($locationVerdict === "bothlocationsnotavailable" || $locationVerdict === "firstlocationnotavailablealternativelocationisavailable") {
            $remarks[] = [
                "severity" => "warning",
                "description" => ucfirst(trans("generic.$locationVerdict"))
            ];
        }

        if ($message) {
            $remarks[] = [
                "severity" => "info",
                "description" => $message
            ];
        }
        return $remarks;
    }

    // Extracted method to calculate the next date based on the recurrence option
    private function calculateNextDate($currentDate, $timeIntervalArray)
    {
        return $currentDate->add(
            \DateInterval::createFromDateString($timeIntervalArray[0] . " " . $timeIntervalArray[1])
        );
    }

    /**
     * Analyse requested planning for the layered planning.
     * Checks availability of tutor and location during event and recurring events
     * @param AnalysePlanDataRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function analysePlanRequest(AnalysePlanDataRequest $request)
    {
        Log::info(
            "analysePlanRequest for courseRegistrationId: " .
            $request->courseRegistrationId . " on " . $request->date . " at " .
            $request->time . " and repeats: " . $request->repeats
        );

        $timeintervals = [
            "day" => [1, 'day'],
            "week" => [1, 'week'],
            "two weeks" => [2, 'weeks'],
            "month" => [1, 'month'],
            "notapply" => [1, 'day']
        ];

        $schoolyear = Schoolyear::getCurrentOrFuture();
        if (empty($schoolyear)) {
            Log::warning("no schoolyear found, so we cannot plan");
            return response()->json(['error' => 'No school year found'], 500);
        }
        // repeats: 0 = all (52), otherwise the requested number of repeats
        // during planning we check if go beyond end of school year
        $repeats = intval($request->repeats) === 0 ? 52 : min(max(intval($request->repeats), 1), 52);

        Log::debug("trying $repeats repeats");
        $courseRegistration = Registration::findOrFail($request->courseRegistrationId);
        $course = Course::findOrFail($courseRegistration->course_id);
        $recurrenceOption = $course->recurrenceoption;
        $timeIntervalArray = $timeintervals[$recurrenceOption->per_interval];

        $tutor = Tutor::findOrFail($request->tutorId);
        $amount = $recurrenceOption->nr_of_times;
        $timeUnit = $recurrenceOption->timeunit;
        $duration = ($timeUnit === "hour" ? 60 : 1) * $amount;
        // duration can not be a broken number, so we round up
        $duration = ceil($duration);
        $message = "";
        // determine this once for the first event, it will be the same for all events
        $isAvailableWorkSchedule = $tutor->isAvailableAccodingToTutorSchedule(
            $request->date,
            $request->time,
            $duration
        );
        Log::info("tutor is available according to work schedule: " . $isAvailableWorkSchedule);

        $theLocation = Location::findOrFail($request->locationId);
        $altLocation = !empty($request->locationIdAlt) ? Location::findOrFail($request->locationIdAlt) : null;

        if (!empty($recurrenceOption->ends_after_nr_of_occurrences) && ($repeats > $recurrenceOption->ends_after_nr_of_occurrences)) {
            $message = trans("generic.limitingnumberofrepeatstocoursedefinition", ["count" => $recurrenceOption->ends_after_nr_of_occurrences]);
            Log::warning(
                "number of repeats > number of repeats according to courses recurrence option, limiting to " .
                $recurrenceOption->ends_after_nr_of_occurrences . " repeats instead of $repeats"
            );
            $repeats = intval($recurrenceOption->ends_after_nr_of_occurrences);
        }

        $startDateTime = new \DateTime($request->date . ' ' . $request->time);
        $planDates = [];

        while (count($planDates) < $repeats) {
            $newDateStart = count($planDates) === 0 ? $startDateTime : $this->calculateNextDate(
                new \DateTime(array_key_last($planDates)),
                $timeIntervalArray
            );

            if ($newDateStart->format('Y-m-d') > $schoolyear->end_date) {
                Log::info("planning beyond end of schoolyear, breaking loop.");
                break;
            }

            $planDates[$newDateStart->format("Y-m-d H:i")] = [
                "event" => $this->generateEventArray($newDateStart, $request, $tutor, $theLocation),
                "remarks" => $this->determineRemarksForConflicts(
                    $newDateStart,
                    $tutor,
                    $theLocation,
                    $altLocation,
                    $duration,  // should be in minutes!
                    $isAvailableWorkSchedule,
                    $message
                )
            ];
        }
        return response()->json($planDates);
    }

    /**
     * Save new events to the database
     * todo: check if an event is already present before creating a new one
     * @param NewEventsRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveNewEvents(NewEventsRequest $request)
    {
        Log::info("saveNewEvents for courseRegistrationId: " . $request->courseRegistrationId);
        $userDomain = Auth::user()->domain_id;
        // give all events in this set the same calUniqueId
        $calUniqueId = uniqid(null, true);
        // get the timetable for this course registration in this school year
        $schoolYearId = $request->schoolYearId;
        $courseRegistrationId = $request->courseRegistrationId;
        $registration = Registration::findOrFail($courseRegistrationId);
        Log::info("checking if all schoolyears have a timetable for this registration");
        // if any school year doesn't have a timetable,
        // create it for every school year that doesn't have one
        $registration->checkTimetables();
        // now fetch the timetable, we know for sure it exists
        $timetable = Timetable::getTimetableFromRegAndSchoolYear($courseRegistrationId, $schoolYearId);
        if (empty($timetable)) {
            Log::error(
                "no timetable found for courseRegistrationId: $courseRegistrationId and schoolYearId: $schoolYearId"
            );
            return response()->json(['error' => 'No timetable found'], 500);
        }
        // get the timespan from registration->course->recurrence->timespan
        $course = Course::findOrFail($registration->course_id);
        // is this a student or a studentgroup?
        $student = Student::find($registration->student_id);
        if (empty($student)) {
            $student = Studentgroup::find($registration->student_id);
        }
        if (empty($student)) {
            Log::error("no student or studentgroup found for registration id: $courseRegistrationId");
            return response()->json(['error' => 'No student or studentgroup found'], 500);
        }
        $recurrenceOption = $course->recurrenceoption;
        // if time_unit is hour, we need to convert to minutes
        $duration = $recurrenceOption->nr_of_times;
        $timeUnit = $recurrenceOption->timeunit;
        if ($timeUnit === "hour") {
            $duration = $duration * 60;
        }
        // domain check on course and student (or any other, this will do)
        if ($course->domain_id !== $userDomain || $student->domain_id !== $userDomain) {
            Log::error("course domain_id does not match user domain_id");
            return response()->json(['error' => 'you are not allowed to enter a planning for this user and course'],
                500);
        }

        $eventsToPersist = $request->eventsToPersist;
        $messageArr = [];
        foreach ($eventsToPersist as $datetime => $newEvent) {
            // todo: at this point: check if the event already exists, if so, skip
            $eventToSave = $newEvent['event'];
            $saveEvent = new Event();
            $saveEvent->caluniqueid = $calUniqueId;
            $saveEvent->timetable_id = $timetable->id;
            $saveEvent->location_id = $eventToSave['location_id'];
            $saveEvent->tutor_id = $eventToSave['tutor_id'];
            $saveEvent->timespan = $duration . " minutes"; // is always in minutes
            $saveEvent->datetime = $datetime;
            $saveEvent->original_datetime = $datetime;
            $saveEvent->sequence = 0; // initial value
            $saveEvent->save();

            // check if we need to create a task for action after trial lesson
            if ($course->is_trial_course) {
                Log::info('This is a trial course event');
                if (!empty($request->createActionAfterTrialLessonTask)) {
                    Log::info('Creating action after trial lesson task');
                    $task = new Task();
                    $task->domain_id = $userDomain;
                    $task->tasktype_id = config('app.TASKTYPE_ACTION_AFTER_TRIAL_LESSON');
                    $task->date_opened = $datetime;
                    $task->course_id = $course->id;
                    $task->student_id = $student->id;
                    $task->tutor_id = $eventToSave['tutor_id'];
                    $task->registration_id = $courseRegistrationId;
                    $task->event_id = $saveEvent->id;
                    $task->remarks = LocaleDate::getLocaleSpecificDate(
                            date('Y-m-d'),
                            App::getLocale()
                        ) . ": " . ucfirst(
                            trans('generic.addedtaskfor') . " " . trans('generic.actionnaftertriallesson')
                        );
                    $task->save();
                    $messageArr[] = ucfirst(
                        trans('generic.addedtaskfor') . " " . trans('generic.actionnaftertriallesson')
                    );
                }
            } else {
                if ($course->recurrenceoption->ends_after_nr_of_occurrences === 1) {
                    Log::info('This is a single lesson event, creating action after single lesson task');
                    $task = new Task();
                    $task->domain_id = Auth::user()->domain->id;
                    $task->tasktype_id = config('app.TASKTYPE_ACTION_AFTER_SINGLE_LESSON');
                    $task->date_opened = $datetime;
                    $task->course_id = $course->id;
                    $task->student_id = $student->id;
                    $task->tutor_id = $eventToSave['tutor_id'];
                    $task->registration_id = $courseRegistrationId;
                    $task->event_id = $saveEvent->id;
                    $task->remarks = LocaleDate::getLocaleSpecificDate(
                            date('Y-m-d'),
                            App::getLocale()
                        ) . ": " . ucfirst(
                            trans('generic.addedtaskfor') . " " . trans('generic.actionaftersinglelesson')
                        );
                    $task->save();
                    $messageArr[] = ucfirst(
                        trans('generic.addedtaskfor') . " " . trans('generic.actionaftersinglelesson')
                    );
                }
            }

            // set end datetime of course subscription if it's a trial lesson or single lesson (recurrence = 1)
            // only if the user didn't already set an end date
            // plus: only if the event is in the future
            // plus only if the number of events for this registration is 1 ($eventsToPersist)
            if (
                empty($registration->end_date) &&
                $datetime > date('Y-m-d H:i') &&
                $course->recurrenceoption->ends_after_nr_of_occurrences === 1)
            {
                // get date from datetime
                $date = explode(' ', $datetime)[0];
                $registration->end_date = $date . " 23:59:59";
                $registration->save();
                Log::info("setting end date for registration $request->registration_id to " . $registration->end_date);
                $messageArr[] = ucfirst(trans('generic.enddateforcoursesubscriptionset'));
            }
        }
        $messageArr[] = count($eventsToPersist) . " " . trans_choice('generic.eventsadded', count($eventsToPersist));
        // swap the first and the last message, the more important message should be first
        $messageArr = array_reverse($messageArr);
        // turn into <br> separated string
        $messageStr = implode("<br>", $messageArr);
        return response()->json(['message' => $messageStr]);
    }
}
