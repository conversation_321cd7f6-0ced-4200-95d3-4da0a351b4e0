<?php

namespace App\Http\Controllers;

use App\Exports\StudentListExport;
use App\Exports\StudentMaillistExport;
use App\Http\Requests\NewStudentFromCopyRequest;
use App\Http\Resources\BankDataResource;
use App\Http\Resources\StudentContactResource;
use App\Http\Resources\StudentCourseRegistrationResource;
use App\Http\Resources\StudentLogentryResource;
use App\Models\Attendancenote;
use App\Models\Checklist;
use App\Models\Classyuser;
use App\Models\Course;
use App\Models\DefaultChecklist;
use App\Models\Emaillogentry;
use App\Models\Event;
use App\Models\Logentry;
use App\Models\Planningentries;
use App\Models\Registration;
use App\Models\Student;
use App\Models\Studentcontact;
use App\Models\Studentgroup;
use App\Models\Studentlist;
use App\Models\Task;
use App\Models\Timetable;
use App\Models\TrialcourseRelation;
use App\Models\Trialstudent;
use App\Models\Tutor;
use App\Http\Requests\SavePinRequest;
use App\Http\Requests\SignRequestRequest;
use App\Http\Requests\StudentFormRequest;
use App\Http\Resources\StudentResource;
use App\Mail\RequestSignature;
use App\Traits\RegistrationsTrait;
use http\Exception\InvalidArgumentException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Scolavisa\scolib\Mydat2Ndat;
use Scolavisa\scolib\Ndat2Mydat;
use Scolavisa\scolib\StringManipulator;

class StudentsController extends Controller
{
    Use RegistrationsTrait;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        return view('students.list');
    }

    /**
     * API looking for students
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiindex(Request $request)
    {
        $students = [];
        $searchkey = $request->searchkey ?: '';
        if ($searchkey != '') {
            $students = Student::where('name', 'like', '%' . $searchkey . '%')->get();
        }
        return response()->json($students);
    }

    /**
     * API get all students and studentgroups
     * query parameters:
     * - onlystudents=true: only return individual students
     * - onlyactive=true: only return students with active courses
     * please note: studentgroups are not filtered by active courses
     * @param Request $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getAll(Request $request)
    {
        $urlWithQueryString = $request->fullUrl();
        $parsed = parse_url($urlWithQueryString);
        $q = $parsed["query"] ?? "";
        $onlyStudents = strpos($q, "onlystudents=true") !== false;
        $onlyActive = strpos($q, "onlyactive=true") !== false;
        Log::info("Requested: $urlWithQueryString, query: $q.");
        Log::info($onlyStudents
            ? "==> I will only return individual students"
            : "==> I will return students and studentgroups"
        );
        Log::info($onlyActive
            ? "==> I will only return active students"
            : "==> I will return all students"
        );
        // if the user is a teacher, we only return their own students
        if(!Auth::user()->userIsA('admin')) {
            $tutor = Tutor::findOrFail(Auth::id());
            $s = $tutor->myStudents;
        } else {
            $st = Student::with(['courses', 'contacts'])->orderBy('lastname')->get();
            if (!$onlyStudents) {
                $sg = Studentgroup::with('courses')->orderBy('lastname')->get();
                $s = $st->merge($sg)->sortBy('lastname');
            } else {
                $s = $st;
            }
            // if only active students are requested, filter out inactive students
            if ($onlyActive) {
                $s = $s->filter(function ($student) {
                    // Filter de cursussen eerst om alleen actieve cursussen over te houden
                    $activeCourses = $student->courses->filter(function ($course) {
                        return empty($course->pivot->end_date) || 
                               $course->pivot->end_date > now()->format('Y-m-d');
                    });
                    
                    // Return true alleen als er actieve cursussen zijn
                    return $activeCourses->isNotEmpty();
                });
            }
            // get student data, sort resulted collection, add learner type, add participants if its a group
            foreach ($s as $index => $student) {
                if ($student->firstname === '-' && $student->date_of_birth === '1800-01-01') {
                    $s[$index]["type"] = "studentgroup";
                    // get id's of participating students
                    $studentsInGroup = $student->students;
                    $studentsInGroupIds = [];
                    foreach ($studentsInGroup as $studentObj) {
                        $studentsInGroupIds[] = $studentObj->id;
                    }
                    $s[$index]["participants"] = $studentsInGroupIds;
                } else {
                    $s[$index]["type"] = "individual";
                    $s[$index]["participants"] = [];
                }
            }
        }
        return StudentResource::collection($s);
    }

    /**
     * Show the form for creating a new resource.
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function create()
    {
        if (Auth::user()->domain->trialLimitReachedStudent) {
            Log::warning("domain " .
                Auth::user()->domain->name . " has reached the account limit for students in a trial account.");
            Log::warning("not showing entryform for new student");
            return redirect()->route('home')
                ->with('message', "error: " . ucfirst(trans("generic.limitoftrialaccountreachedstudent")));
        }

        $allCourses = Course::get();
        $courses = [];
        return view('students.create', compact('allCourses', 'courses'));
    }

    public function newFromOtherStudent(NewStudentFromCopyRequest $request)
    {
        $studentTarget = new Student();
        // all or none transaction
        DB::transaction(function() use($request, $studentTarget) {
            // check student source data
            $studentSource = Student::find($request->studentId);
            if ($studentSource->domain_id !== Auth::user()->domain_id) {
                Log::error("Student $request->studentId has wrong domain_id (should be: " . Auth::user()->domain_id . ", but was: $studentSource->domain_id)");
                throw new InvalidArgumentException("Student source unknown in your organisation");
            }
            $name = "$request->newFirstName $request->newPreposition $request->newLastName";
            $name = str_replace("  ", " ", $name);
            $studentTarget->domain_id = Auth::user()->domain_id;
            $studentTarget->name = $name;
            $studentTarget->firstname = $request->newFirstName ?: '';
            $studentTarget->preposition = $request->newPreposition ?: '';
            $studentTarget->lastname = $request->newLastName;  // mandatory
            $studentTarget->date_of_birth = Ndat2Mydat::getMydat($request->newDateOfBirth); // mandatory
            $studentTarget->status = "ACTIVE"; // @deprecated but mandatory
            $studentTarget->has_access = 0;
            // need the new students ID to generate the accesstoken
            $studentTarget->save();
            $studentTarget->accesstoken = md5($studentTarget->id . $name . $studentTarget->id);
            $studentTarget->save();

            Log::info("Saving basic data for new student with ID: $studentTarget->id");
            // Toggles for data copy
            if ((bool)$request->copyToggleAddress) {
                Log::info("Saving ADDRESS data for student with ID: $studentTarget->id, based on student with id: $request->studentId");
                $studentTarget->address = $studentSource->address;
                $studentTarget->zipcode = $studentSource->zipcode;
                $studentTarget->city = $studentSource->city;
                $studentTarget->save();
            }
            if ((bool)$request->copyToggleBank) {
                Log::info("Saving BANK data for student with ID: $studentTarget->id, based on student with id: $request->studentId");
                $studentTarget->permission_auto_banktransfer = $studentSource->permission_auto_banktransfer;
                $studentTarget->bankaccount_name = $studentSource->bankaccount_name;
                $studentTarget->bankaccount_number = $studentSource->bankaccount_number;
                $studentTarget->mandate_number = uniqid();
                $studentTarget->save();
            }
            if ((bool)$request->copyToggleContact) {
                Log::info("Saving CONTACT data for student with ID: $studentTarget->id, based on student with id: $request->studentId");
                $contacts = $studentSource->contacts;
                // copy contacts, couple on target
                foreach ($contacts as $contact) {
                    $c = new Studentcontact();
                    $c->student_id              = $studentTarget->id;
                    $c->contacttype             = $contact->contacttype;
                    $c->label                   = $contact->label;
                    $c->value                   = $contact->value;
                    $c->apply_for_planning      = $contact->apply_for_planning;
                    $c->apply_for_finance       = $contact->apply_for_finance;
                    $c->apply_for_promotions    = $contact->apply_for_promotions;
                    $c->use_salutation          = $contact->use_salutation;
                    $c->save();
                }
            }
            if ((bool)$request->copyToggleStudentlists) {
                Log::info("Saving STUDENTLISTS data for student with ID: $studentTarget->id, based on student with id: $request->studentId");
                $lists = $studentSource->studentlists;
                foreach ($lists as $list) {
                    $l = Studentlist::findOrFail($list->id);
                    $studentTarget->studentlists()->attach($l);
                }
            }
            Log::info("Successfully created new student based on copied information. Returning new student ID: $studentTarget->id");
        });

        return (isset($studentTarget) && isset($studentTarget->id))
            ? response()->json(['studentId' => $studentTarget->id])
            : response()->json(['error' => 'Create student record failed']);
    }

    /**
     * Save action for NEW:
     * Store a newly created resource in storage.
     * First check trial account status
     * replaces the store_C2 method
     * @param StudentFormRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StudentFormRequest $request)
    {
        Log::info("saving new student");

        if (Auth::user()->domain->trialLimitReachedStudent) {
            Log::warning("domain " . Auth::user()->domain->name . " has reached the account limit for students in a trial account.");
            Log::warning("not saving new student");
            return redirect()->route('home')->with('message', ucfirst(trans("generic.limitoftrialaccountreachedstudent")));
        }

        $student = new Student();
        $student->domain_id = Auth::user()->domain->id;
        $name = $request->firstname;
        if (!empty($request->preposition)) {
            $name .= ' ' . $request->preposition;
        }
        $name .= ' ' . $request->lastname;

        $student->name = $name;
        $student->firstname = $request->firstname;
        $student->preposition = $request->preposition;
        $student->lastname = $request->lastname;
        $student->address = $request->address;
        $student->zipcode = $request->zipcode;
        $student->city = $request->city;
        $student->date_of_birth = Ndat2Mydat::getMydat($request->date_of_birth);
        $student->status = "ACTIVE"; // @deprecated but mandatory
        $student->mandate_number = uniqid();
        $student->permission_auto_banktransfer = $request->permission_auto_banktransfer;
        $student->has_access = 0;
        $student->save();
        // now that i have an ID i can create an accesstoken
        $student->accesstoken = md5($student->id . $name . $student->id);
        $student->save();

        // now go to the edit screen
        return redirect()->route('students.edit', $student->id)
            ->with('message', ucfirst(trans("generic.dataSaved")));
    }

    /**
     * get student info for API calls
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function get($id)
    {
        $student = Student::with(['contacts', 'courses', 'courses.recurrenceoption'])->find($id);
        if (empty($student)) {
            // try if this is a studentgroup
            $student = Studentgroup::with(['courses', 'courses.recurrenceoption'])->find($id);
            if (empty($student)) {
                return response()->json(["error" => "Student not found"], 404);
            } else {
                $student->type = "studentgroup";
            }
        } else {
            $student->type = "individual";
        }

        if ($student->type === 'individual') {
            // check if a course is being tutored in a group
            $coursesInStudentGroup = [];
            $studentGroupLinksForTrialCourses = [];
            foreach ($student->studentgroups as $studentGroup) {
                $course = $studentGroup->courses[0]; // only one course in a studentgroup
                $coursesInStudentGroup[$course->id] = [
                    "stgId" => $studentGroup->id,
                    "stgName" => $studentGroup->lastname
                ];
                $trialCoursesForThisCourse = TrialcourseRelation::trialcoursesForCourse($course->id);
                // keep track of which trial courses are linked to this student group
                $studentGroupLinksForTrialCourses[$studentGroup->id] = $trialCoursesForThisCourse;
            }
            // remove doubles
            // $coursesInStudentGroup = array_unique($coursesInStudentGroup);
            $groupInfo = [
                "coursesInStudentGroup" => $coursesInStudentGroup,
                "studentGroupLinksForTrialCourses" => $studentGroupLinksForTrialCourses
            ];
            $student->groupInfo = $groupInfo;
        }
        return response()->json($student);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View
     * @throws \Exception
     */
    public function edit($id)
    {
        Log::info("Fetching student: $id for editing.");

        $isNoAdmin = !Auth::user()->userIsA("admin");
        if ($isNoAdmin) {
            $theTutor = Tutor::findOrFail(Auth::user()->id);
            $validStudents = $theTutor->myStudents();
            Log::info("logged in user is a tutor, there are " . count($validStudents) . " students this tutor can see in the interface.");
            if (array_search($id, $validStudents) === false) {
                Log::error("tutor access to student $id is not allowed");
                return view('errors.404', [
                    "message" => __('generic.accessnotallowed')
                ]);
            }
        }

        Log::info("is admin: Fetching all studentdata.");
        $student = Student::findOrFail($id);
        Log::info("student found: " . $student->name);
        // please note: weird code ahead.
        // turns out it's very hard to convince laravel to add this property
        $isAdult = $student->isAdult;
        $student->isAdult = $isAdult;
        $courses = $student->courses;

        $currentCourses = [];
        $previousCourses = [];

        // assemble current courses
        foreach ($courses as $course) {
            $endDate = new \DateTime($course->pivot->end_date);
            $currentDate = new \DateTime();
            if ((empty($course->pivot->end_date)) || ($endDate > $currentDate)) {
                $currentCourses[] = $course;
            } else {
                $previousCourses[] = $course;
            }
        }

        // get all students for the quickjump
        $allStudents = Student::all();

        // get all courses
        $allCourses = Course::orderBy("name")->get();

        // does the student follow a course in a course group?
        Log::info("examining studentgroups for this student");
        $stgs = [];
        $studentgroups = DB::table('student_studentgroup as ssg')
            ->select('studentgroup_id')
            ->where('ssg.student_id', '=', $id)
            ->get();
        foreach ($studentgroups as $studentgroup):
            try {
                $stg = Studentgroup::findOrFail($studentgroup->studentgroup_id);
            } catch (\Exception $e) {
                Log::error("Studentgroup $studentgroup->studentgroup_id not found");
                continue;
            }
            $regid = (isset($stg->courses) && count($stg->courses) > 0) ? $stg->courses[0]->pivot->id : 0;
            if ($regid > 0) {
                $reg = Registration::findOrFail($regid);
                $stgs[$stg->courses[0]->id] = [
                    'studentgroupid' => $studentgroup->studentgroup_id,
                    'studentgroupname' => $stg->lastname,
                    //'nrofappointments' => $reg->getNrOfAppointments(),
                    //'nrofappointmentsfuture' => $reg->getNrOfAppointments(true),
                    'regid' => $regid
                ];
            }
        endforeach;

        // Check Email
        $email = $student->email;
        $contactTypes = ['telephone' => __('generic.telephone'), 'email' => __('generic.email')];
        // courseTaxRate
        $courseTaxRate = Auth::user()->domain->course_tax_rate;
        // individual student view
        return view('students.edit', compact('student', 'courses', 'allCourses', 'allStudents', 'contactTypes', 'currentCourses', 'previousCourses', 'stgs', 'email', 'courseTaxRate'));
    }


    /**
     * API get 1 student
     * @param Request $request
     * @param $studentId
     * @return StudentResource
     */
    public function getOne(Request $request, $studentId)
    {
        $s = Student::where("id", $studentId)
            ->with('courses')
            ->get();
        return new StudentResource($s);
    }

    /**
     * @param Request $request
     * @param $studentId
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getAllContacts(Request $request, $studentId)
    {
        Log::info("getting all contact info for student $studentId");
        $s = Student::where([['id', '=', $studentId], ['domain_id', '=', Auth::user()->domain->id]])->first();
        $contacts = $s->contacts;
        return StudentContactResource::collection($contacts);
    }

    /**
     * Delete 1 student contact
     * @param Request $request
     * @param $contactId
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function deleteStudentContact(Request $request, $contactId)
    {
        $c = Studentcontact::findOrFail($contactId);
        $c->delete();
        return response()->json(["result" => "success"]);
    }

    public function updateContactApplyFor(Request $request, $contactId)
    {
        $c = Studentcontact::findOrFail($contactId);
        switch ($request->field) {
            case "apply_for_finance":
                $c->apply_for_finance = $request->newValue;
                break;
            case "apply_for_planning":
                $c->apply_for_planning = $request->newValue;
                break;
            case "apply_for_promotions":
                $c->apply_for_promotions = $request->newValue;
                break;
        }
        $c->save();
        return response()->json(["result" => "success"]);
    }

    /**
     * Get all logentries for a student or a student group (the logbook of 1 student)
     * @param Request $request
     * @param $studentId
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getAllLogentries(Request $request, $studentId)
    {
        Log::info("getting all log entries for student $studentId");

        $s = Student::where([
            ['id', '=', $studentId],
            ['domain_id', '=', Auth::user()->domain_id]
        ])->first();

        if (empty($s)) {
            Log::info("trying again for studentgroup $studentId");
            // well, maybe it's a student group
            $s = Studentgroup::where([
                ['id', '=', $studentId],
                ['domain_id', '=', Auth::user()->domain_id]
            ])->first();
        }
        $logentries = $s->logentries;
        return StudentLogentryResource::collection($logentries);
    }

    /**
     * update excising or create new logentry through API
     * @param Request $request
     * @param int $logentryId
     * @return StudentLogentryResource
     */
    public function saveLogentry(Request $request, $logentryId = 0) {
        if (($logentryId === 0 || $logentryId === '0')) {
            Log::info("New logentry for student $request->student_id");
            // new record
            $logentry = new Logentry();
            $logentry->student_id = $request->student_id;
        } else {
            Log::info("Update logentry $logentryId");
            // update: can't change student_id
            $logentry = Logentry::findOrFail($logentryId);
        }
        $logentry->entry = $request->entry;
        $logentry->save();
        return new StudentLogentryResource($logentry);

    }

    public function deleteLogentry(Request $request, $logentryId)
    {
        Logentry::findOrFail($logentryId)->delete();
        return response()->json(["result" => "success"]);
    }

    /**
     * @param Request $request
     * @param $studentId
     * @return BankDataResource
     */
    public function getBankData(Request $request, $studentId)
    {
        Log::info("getting bank information for student $studentId");
        $s = Student::select (
            'permission_auto_banktransfer',
            'bankaccount_name',
            'bankaccount_number',
            'mandate_number'
        )
            ->where('id', $studentId)
            ->first();
        return new BankDataResource($s);
    }

    public function saveBankData(Request $request, $studentId)
    {
        Log::info("saving bank information for student $studentId");
        // if mandate number is still empty, create a unique id for it
        $mandateNumber = $request->mandate_number;
        if(strlen($mandateNumber) === 0) {
            $mandateNumber = uniqid();
        }
        $s = Student::where('id', $studentId)
            ->first();
        $s->permission_auto_banktransfer    = $request->permission_auto_banktransfer;
        $s->bankaccount_name                = $request->bankaccount_name;
        $s->bankaccount_number              = strtoupper($request->bankaccount_number);
        $s->mandate_number                  = $mandateNumber;
        $s->save();
        return new BankDataResource($s);
    }

    /**
     * @param Request $request
     * @param $registrationId
     * @return StudentCourseRegistrationResource
     */
    public function getStudentCourseRegistration(Request $request, $registrationId)
    {
        $reg = Registration::where('id', $registrationId)->with('checklists')->first();
        return new StudentCourseRegistrationResource($reg);
    }
    /**
     * Delete course registration
     * If it's the registration : also delete from studentgroup
     * @param Request $request
     * @param $registrationId
     */
    public function deleteStudentCourseRegistration(Request $request, $registrationId)
    {
        if (Auth::user()->userIsA('admin')) {
            // not atomic: use transaction
            DB::transaction(function () use ($registrationId) {
                $r = Registration::findOrFail($registrationId);
                // first remove checklists
                $r->checklists->each(function($checklist) use ($registrationId) {
                    Log::info("deleting checklist $checklist->id because of deleting reg: $registrationId");
                    $checklist->delete();
                });
                // remove tasks
                $r->tasks->each(function($task) use ($registrationId) {
                    Log::info("deleting task $task->id because of deleting reg: $registrationId");
                    $task->delete();
                });
                // remove events
                $r->events->each(function($event) use ($registrationId) {
                    // remove attendance notes
                    $event->attendancenotes->each(function($attendancenote) use ($registrationId) {
                        Log::info("deleting attendancenote $attendancenote->id because of deleting reg: $registrationId");
                        $attendancenote->delete();
                    });
                    // remove tasks
                    $event->tasks->each(function($task) use ($registrationId) {
                        Log::info("deleting task $task->id because of deleting reg: $registrationId");
                        $task->delete();
                    });
                    Log::info("deleting event $event->id because of deleting reg: $registrationId");
                    $event->delete();
                });
                // remove timetable
                $r->timetables->each(function($timetable) use ($registrationId) {
                    Log::info("deleting timetable $timetable->id because of deleting reg: $registrationId");
                    $timetable->delete();
                });
                // now delete the registration
                Log::info("deleting registration $registrationId");
                $r->delete();
            });
            return response()->json(['result' => 'Delete successful'], 200);
        }
        // insufficient rights
        Log::error("Error deleting course registration $registrationId");
        return response()->json(['result' => 'Error deleting course registration'], 403);
    }

    /**
     * Create a new mandate number if the admin requests this.
     * This is hardly ever needed, only if admin wants to keep the current student,
     * but they are using a different bankaccount (no longer dad pays for the lessons)
     * @param Request $request
     * @param $studentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function createNewMandateNumber(Request $request, $studentId)
    {
        $s = Student::findOrFail($studentId);
        $s->mandate_number = uniqid();
        $s->save();
        return response()->json(["mandate_number"=>$s->mandate_number]);
    }

    /**
     * @param StudentFormRequest $request
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(StudentFormRequest $request, $id)
    {
        Log::info("Saving student data");

        $student = Student::findOrFail($id);

        $name = $request->firstname;
        if (!empty($request->preposition)) {
            $name .= ' ' . $request->preposition;
        }
        $name .= ' ' . $request->lastname;

        $student->name = $name;
        $student->firstname = $request->firstname;
        $student->preposition = $request->preposition;
        $student->lastname = $request->lastname;
        $student->address = $request->address;
        $student->zipcode = $request->zipcode;
        $student->city = $request->city;
        $student->date_of_birth = Ndat2Mydat::getMydat($request->date_of_birth);
        $student->has_access = $request->has_access === 'on' ? 1 : 0;
        // contact-data
        $fields = $request->input(); // gets all request fields
        foreach ($fields as $fieldName =>  $fieldValue) {
            if(strpos($fieldName, "contacttype") !== false) {
                // get al related contact fields [contacttype_3375]
                $contactid = substr($fieldName, 12);
                // value is mandatory
                if (!empty($request->get("contactvalue_$contactid"))) {
                    if (strpos($contactid, "new_") !== false) {
                        Log::info('found new contact');
                        // create a new contact record
                        $contact = new Studentcontact();
                        $contact->student_id = $id;
                    } else {
                        Log::info('found possible update for contact');
                        // update existing record
                        $contact = Studentcontact::findOrFail($contactid);
                    }
                    $contact->contacttype = $request->get("contacttype_$contactid");
                    $contact->label = $request->get("contactlabel_$contactid");
                    $contact->value = $request->get("contactvalue_$contactid");
                    $contact->use_salutation = $request->get("contactsalutation_$contactid");
                    $contact->save();
                } else {
                    Log::debug("contact $contactid seems to be empty - skipping.");
                }
            }
        }

        // bank-data
        Log::info("permission bank: $request->permission_auto_banktransfer");
        $permission = isset($request->permission_auto_banktransfer) && $request->permission_auto_banktransfer === 'on';
        $student->permission_auto_banktransfer = $permission
            ? 'Ja'
            : 'Nee';
        Log::info("permission to save: $student->permission_auto_banktransfer");
        $student->bankaccount_name = $request->bankaccount_name;
        $student->bankaccount_number = strtoupper($request->bankaccount_number);
        $student->save();
        Log::info("Student data saved");

        // course registrations, if any - start / enddate
        if (!empty($request->startdate)) {
            foreach ($request->startdate as $regid => $startdate) {
                Log::info("Updateting registration $regid - start/enddate");
                $enddate = $request->enddate[$regid];
                $r = Registration::findOrFail($regid);
                $sd = Ndat2Mydat::getMydat($startdate);
                $ed = Ndat2Mydat::getMydat($enddate);
                Log::debug("sd: $sd, ed: $ed");
                $r->start_date = $sd;
                $r->end_date = $ed; // may be null.
                $r->save();
                // if this is an unsubscribe action, then all events after this end_date for this registration should be removed
                if (!empty($ed)) {
                    $eventsToDelete = Event::select('events.id as id')
                        ->leftJoin('timetables', 'events.timetable_id', '=', 'timetables.id')
                        ->where('timetables.course_student_id', '=', $r->id)
                        ->where('events.datetime', '>', $ed . " 23:59:59")
                        ->get();
                    foreach ($eventsToDelete as $eventToDelete) {
                        // if an event has a task, delete that first
                        Log::info("deleting tasks for event $eventToDelete->id");
                        Task::where("event_id", "=", $eventToDelete->id)->delete();
                        // if event has attendancenotes, delete those first
                        Log::info("deleting attendancenotes for event $eventToDelete->id");
                        Attendancenote::where("event_id", "=", $eventToDelete->id)->delete();
                        // finally, delete the event
                        Log::info("deleting event: $eventToDelete->id");
                        $eventToDelete->delete();
                    }
                }
            }
        }

        // back to the edit screen
        return redirect()->route('students.edit', $id)->with('message', ucfirst(trans("generic.dataSaved")));
    }


    /** ********************************************* */
    /** CLASS 2 - not necessarily deprecated though!  */
    /** ********************************************* */

    /**
     * Update the specified resource in storage.
     *
     * @param StudentFormRequest|Request $request
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function update_c2(StudentFormRequest $request, $id)
    {
        Log::info("Saving Student data");
        $mandateNumber = empty($request->mandate_number) ? uniqid() : $request->mandate_number;

        $student = Student::findOrFail($id);

        $name = $request->firstname;
        if (!empty($request->preposition)) {
            $name .= ' ' . $request->preposition;
        }
        $name .= ' ' . $request->lastname;

        $accesstoken = empty($request->accesstoken) ? md5($id . $student->name . $id) : $request->accesstoken;

        $student->name = $name;
        $student->firstname = $request->firstname;
        $student->preposition = $request->preposition;
        $student->lastname = $request->lastname;
        $student->address = $request->address;
        $student->zipcode = $request->zipcode;
        $student->city = $request->city;
        $student->date_of_birth = Ndat2Mydat::getMydat($request->date_of_birth);
        $student->accesstoken = $accesstoken;
        $student->has_access = $request->has_access;
        $student->bankaccount_name = $request->bankaccount_name;
        $student->bankaccount_number = $request->bankaccount_number;
        $student->mandate_number = $mandateNumber;
        $student->permission_auto_banktransfer = $request->permission_auto_banktransfer;
        $student->remarks = $request->remarks;
        $student->save();
        Log::info("base data saved");
        // course update (n:m)
        $syncArray = [];
        if (count($request->get('courses')) > 0) {
            $theCourses = $request->get('courses');
            $theStartdates = $request->get('startdates');
            $theEnddates = $request->get('enddates');
            $theRegIds = $request->get('regid');
            $theSigned = $request->get('signed');
            // loop through courses
            foreach ($theCourses as $idx => $theCourse) {
                // the empty course placeholder will also be in the request.
                // recognise that it is empty
                if (!empty($theCourse)) {
                    if (!empty($theRegIds[$idx])) {
                        $tempRegArray = Registration::findOrFail($theRegIds[$idx])->toArray();
                    } else {
                        $tempRegArray = ["id" => null];
                    }
                    // mutable - these field may be mutated by the interface
                    $tempRegArray["start_date"] = $theStartdates[$idx] == '' ? Date("Y-m-d") : Ndat2Mydat::getMydat($theStartdates[$idx]); // if empty: today.
                    $tempRegArray["end_date"] = empty($theEnddates[$idx]) ? null : Ndat2Mydat::getMydat($theEnddates[$idx]); // if empty: null
                    $tempRegArray["course_id"] = $theCourse;
                    $tempRegArray["student_id"] = $student->id;
                    $tempRegArray["signed"] = empty($theSigned[$idx]) ? null : $theSigned[$idx];
                    //  - these fields don't change, once they have a value
                    //  - the value will never be initiated by a request
                    $tempRegArray["sign_code"] = empty($tempRegArray["sign_code"]) ? uniqid("", true) : $tempRegArray["sign_code"];
                    $syncArray[] = $tempRegArray;
                }

                // if the enddate is filled: delete any appointments after that date
                if (!empty($theEnddates[$idx])) {
                    Log::info("enddate ($idx): " . $theEnddates[$idx] . " reg: " . $theRegIds[$idx]);
                    // look for future appointments for this registration
                    $nrOfEventsDeleted = Event::deleteEventsAfter($theEnddates[$idx], $theRegIds[$idx]);
                    Log::info("Found $nrOfEventsDeleted events after the registration enddate");
                    // todo: add to save message

                }
            }
            Log::info("Found " . count($syncArray) . " courses to save");


        } else {
            Log::info("No courses found");
        }

        // save the new course registrations (delete is handled by Vue code)
        // this being too complex to use the standard sync function,
        // We'll write our own save / sync function
        // We'll write our own save / sync function
        // but make sure the keep the current PK!
        foreach ($syncArray as $registration) {
            if (!empty($registration["id"])) {
                Log::info('registration updated');
                // original PK is saved
                $r = with(new Registration)->newInstance($registration, true);
                $r->save();
            } else {
                // new id (pk) is created
                $r = new Registration();
                $r->fill($registration);
                $r->save();
                Log::info('new registration created');
                // see if there are checklists to link
                $this->initiateChecklists($r->id);
                // this maybe a registration for a triallesson request:
                // --> is student id present in trialstudents?
                // --> has this trialstudent no registration yet?
                // --> is the course_id empty? ('other course' choosen)
                // then: register this registration with this trialstudent,
                // to make sure we know we can now schedule this registration
                $ts = Trialstudent::where([
                    "generated_student_id" => $student->id,
                ])
                    ->whereNull("generated_registration_id")
                    ->whereNull("course_id")
                    ->first(); // use first(). get() will return an array
                if (!empty($ts)) {
                    Log::info("add registration for trial lesson student $r->id");
                    $ts->generated_registration_id = $r->id;
                    $ts->save();
                } else {
                    Log::info("no trial lesson student");
                }// else: dont touch it
            }
        }

        // contactinfo update (1:n)
        $student->contacts()->delete();
        $values = $request->get('value');
        if (isset($values)) {
            if (count($request->get('value')) > 0) {
                $theLabels = $request->get('label');
                $theTypes = $request->get('contacttype');
                $theForFinances = $request->get('apply_for_finance');
                $theForPlanning = $request->get('apply_for_planning');
                $theForPromotions = $request->get('apply_for_promotions');
                $theUseSalutations = $request->get('use_salutation');
                foreach ($request->get('value') as $index => $value) {
                    // ignore the template row
                    if (($value !== '999') && ($value !== '') && ($value !== null)) {
                        Log::info("saving contact " . $theTypes[$index] . " for student $student->id");
                        if ($theTypes[$index] === 'email') {
                            // this ensures there's no unicode or other invalid characters in the string
                            // which would yield an exception in the mailer
                            $clean = StringManipulator::emailExtractor($value);
                            $value = $clean["email"] ?? '';
                        }
                        // create new contact item
                        $student->contacts()->create([
                            "contacttype" => $theTypes[$index],
                            "label" => $theLabels[$index],
                            "value" => $value,
                            "apply_for_finance" => (isset($theForFinances[$index]) ? 1 : null),
                            "apply_for_planning" => (isset($theForPlanning[$index]) ? 1 : null),
                            "apply_for_promotions" => (isset($theForPromotions[$index]) ? 1 : null),
                            "use_salutation" => $theUseSalutations[$index]
                        ]);
                    }
                }
            }
        }

        // back to the edit screen
        return redirect()->route('students.edit', $id)->with('message', ucfirst(trans("generic.datasaved")));
    }

    /**
     * if no checklist id was added through the request, we try if a default checklist should be added
     * i.e. one or more that are marked as 'auto-add'
     * if not we return null.
     *
     * @param int $registrationId course_student coupling table
     * @param int $checklistFromRequest
     *
     * @return array
     */
    private function initiateChecklists($registrationId, $checklistFromRequest = 0)
    {
        $retChecklistIds = [];
        if (empty($checklistFromRequest)) {
            // this may be 0, 1 or more than 1 checklist
            $defaultChecklists = $this->getAutoAddChecklists();
            // see if any checklists should be added
            if (count($defaultChecklists) > 0) {
                // this will be multiple checklists on a registration in a future version
                // for now: only one checklist, because the course_student table can currently only hold one checklist
                foreach ($defaultChecklists as $autoAddChecklistId) {
                    // create a copy of the template checklist
                    //$retChecklistId = Checklist::createNewChecklist( $autoAddChecklistId->id );
                    $retChecklistIds[] = Checklist::createNewChecklist($autoAddChecklistId->id, $registrationId);
                }
            }
        }

        return (count($retChecklistIds) == 0 ? null : $retChecklistIds);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws \Exception
     */
    public function destroy($id)
    {
        // first remove registrations: delete from course_student where student_id = $id
        Registration::destroyForStudent($id);
        // then remove the student: delete from student where id = $id
        Student::where("id", $id)->delete();
    }

    /**
     * Return the checklist(s) that should automatically be added to a registration, if any
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    private function getAutoAddChecklists()
    {
        $autoAddChecklistIds = DefaultChecklist::select('id')->where('auto_add', '1')->get();

        return $autoAddChecklistIds;
    }

    /**
     * Send sign request email for this registration
     *
     * @param SignRequestRequest $request
     *
     * @return string
     */
    public function sendSignRequest(SignRequestRequest $request)
    {
        $registration = Registration::findOrFail($request->regid);
        $replacementtext = $request->replacementtext ?: '';

        // check if this is an old link (already signed)
        if (isset($registration)) {
            if (!empty($registration->signed)) {
                Log::error("Registration id entered that has already been signed: " . $request->regid);

                return view('errors.404', [
                    "message" => __('generic.alreadysigned')
                ]);
            }
        } else {
            Log::error('Registration entered that is not valid: ' . $request->regid);

            return view('errors.404', [
                "message" => __("generic.regidnotvalid")
            ]);
        }

        // send the email to the target email. first remove any spaces
        // not trim(), there maybe be more addresses, comma separated
        $email = str_replace(" ", "", $request->targetemail);

        // multiple addresses as array (not in the docs!)
        $addresses = explode(',', $email);
        foreach ($addresses as $address) {
            Log::info("Sending email to $address to request signing registration " . $request->regid);
            Mail::to($address)
                ->send(new RequestSignature($registration, $replacementtext));
        }
        return response()->json(['success' => 'message send']);
    }

    /**
     * Create a new student, based on the info in a trialrequest
     *
     * @param int $requestid
     * @return \Illuminate\Http\JsonResponse
     */
    public function createFromTrialRequest(Request $request)
    {
        $logNotices = '';
        // first check if this request already has a couplet student account
        $trialrequest = Trialstudent::findOrFail($request->input("trialStudentId"));
        if (empty($trialrequest->generated_student_id)) {
            Log::info("creating student based on trialrequest $trialrequest->id.");
            $name = $trialrequest->firstname;
            if (!empty($trialrequest->preposition)) {
                $name .= ' ' . $trialrequest->preposition;
            }
            $name .= ' ' . $trialrequest->lastname;

            // create a student
            $student = new Student();
            $student->domain_id = Auth::user()->domain_id;
            $student->firstname = $trialrequest->firstname;
            $student->preposition = $trialrequest->preposition;
            $student->lastname = $trialrequest->lastname;
            $student->name = $name;
            $student->status = "ACTIVE"; // @deprecated but mandatory
            $student->date_of_birth = isset($trialrequest->date_of_birth) ? Ndat2Mydat::getMydat($trialrequest->date_of_birth) : "1800-01-01";
            $student->has_access = 0;

            $logNotices = "<p>" . ucfirst(trans('generic.thisisatrialrequeststudent')) . "</p>";
            $logNotices = $logNotices . "<p>" . $trialrequest->remarks . "</p>";
            if (substr($trialrequest->requested_startdate, 0, 6) !== "Anders") {
                $logNotices = $logNotices . "<p>" . trans('generic.requestedstartdate') . " " . $trialrequest->requested_startdate . "</p>";
            } // else it's already there
            $student->save();
            // now that we have an ID we can generate an accesstoken
            $student->accesstoken = md5($student->id . $student->name . $student->id);
            $student->save();

            // make a note if date_of_birth is missing
            if (!isset($trialrequest->date_of_birth)) {
                Log::warning("Trial request does not have a date_of_birth!");
                $logNotices = $logNotices . "<p style='color:red;'>" . trans('generic.dateofbirthmissing') . "</p>";
            }

            // register student record with this trial student
            $trialrequest->generated_student_id = $student->id;
            $trialrequest->save();

            // $course_id if available in course_student table
            if (!empty($trialrequest->course_id)) {
                Log::info("coupling course $trialrequest->course_id to new student $student->id");
                // create registration
                $registration = new Registration();
                $registration->course_id = $trialrequest->course_id;
                $registration->student_id = $student->id;
                $registration->start_date = date('Y-m-d'); // today will do, can be fixed if needed
                $registration->sign_code = uniqid("", true);
                $registration->save();

                // register this id subscription with the trial lesson request
                $trialrequest->generated_registration_id = $registration->id;
                $trialrequest->save();

                // check if a checklist should be coupled automatically.
                // I won't couple it, because that's probably not wanted for a trial lesson
                // but i will warn in the remarks section
                $defChecklist = $this->getAutoAddChecklists();
                if (count($defChecklist) > 0) {
                    $logNotices = $logNotices . "<p>" . trans('generic.nochecklistwasadded') . "</p>";
                }
            } else {
                Log::info("No course to be coupled. Place warning in student remarks");
                $logNotices = $logNotices . "<p>" . trans('generic.couldnotcreatecourseregistration') . "</p>";
            }
            // insert telephone and email in the contact table
            if (isset($trialrequest->email)) {
                Log::info("adding email to new student $student->id");
                $emailAnalysis = StringManipulator::emailExtractor($trialrequest->email);
                $label = isset($emailAnalysis['extra']) ? $emailAnalysis['extra'] : '-';
                $email = isset($emailAnalysis['email']) ? $emailAnalysis['email'] : '-';
                $student->contacts()->create([
                    "contacttype" => 'email',
                    "label" => $label,
                    "value" => $email
                ]);
            }
            if (isset($trialrequest->telephone)) {
                Log::info("adding telephone to new student $student->id");
                $telephoneAnalysis = StringManipulator::telephoneExtractor($trialrequest->telephone);
                $label = isset($telephoneAnalysis['extra']) ? $telephoneAnalysis['extra'] : '-';
                $telephone = isset($telephoneAnalysis['tel']) ? $telephoneAnalysis['tel'] : '-';
                $student->contacts()->create([
                    "contacttype" => 'telephone',
                    "label" => $label,
                    "value" => $telephone
                ]);
            }
        } else {
            return response()->json([
                "message"=>"trial request already has a coupled student",
                "generated_student_id" => $trialrequest->generated_student_id
            ], 400);
        }
        Log::info("student $student->id created succesfully");
        // after a student create we need to update the status as well to status 2
        // fixme: this is very specifically the case for MFB,
        // fixme: it should be a choice of the customer to create a workflow like this
        $trialrequest->trialrequeststatus_id = 2;
        $trialrequest->save();
        Log::info("update trial request, setting status to: 2 for trial request $trialrequest->id");

        // save log notices
        if ($logNotices !== '') {
            $logEntry = new Logentry(["entry" => $logNotices]);
            $student->logentries()->save($logEntry);
        }
        return response()->json([
            "message"=>"student $student->id created succesfully",
            "generated_student_id" => $student->id
        ]);
    }

    public function delFull($studentId)
    {
        if (Auth::user()->userIsA('admin')) {
            DB::transaction(function () use ($studentId) {
                Log::info("***>> Start delete all info of student with ID $studentId");
                // determine all student related tables, cleaning up

                // ---- analyses
                // student has trialstudentaccount?
                $trialStudents = Trialstudent::where("generated_student_id", "=", $studentId)->get(['id'])->toArray();
                $trialStudentIdsToDelete = array_map(function ($ts) {
                    return $ts["id"];
                }, $trialStudents);
                Log::info("deleting trialstudents: " . join(", ", $trialStudentIdsToDelete));
                Trialstudent::destroy($trialStudentIdsToDelete);

                // has logentries?
                Log::info("deleting logentries: " . join(", ", $trialStudentIdsToDelete));
                $logentries = Logentry::where("student_id", "=", "$studentId")->get(['id'])->toArray();
                Logentry::destroy($logentries);

                // has contacts
                $studentContacts = Studentcontact::where("student_id", "=", $studentId)->get(['id'])->toArray();
                $studentContactsIdsToDelete = array_map(function ($elm) {
                    return $elm["id"];
                }, $studentContacts);

                Log::info("deleting studentcontacts: " . join(",", $studentContactsIdsToDelete));
                Studentcontact::destroy($studentContactsIdsToDelete);

                // has tasks
                $tasks = Task::where("student_id", "=", $studentId)->get(['id'])->toArray();
                $taskIdsToDelete = array_map(function ($elm) {
                    return $elm["id"];
                }, $tasks);
                Log::info("deleting tasks: " . join(", ", $taskIdsToDelete));
                Task::destroy($taskIdsToDelete);

                // has registrations?
                $registrations = Registration::where("student_id", "=", $studentId)->get(['id'])->toArray();
                foreach ($registrations as $registration) {
                    // registration has checklists
                    $checklists = Checklist::where("registration_id", "=", $registration["id"])->get(['id'])->toArray();
                    $checklistIdsToDelete = array_map(function ($elm) {
                        return $elm["id"];
                    }, $checklists);
                    Log::info("deleting checklists (for registration " . $registration["id"] . "): " . join(", ", $checklistIdsToDelete));
                    Checklist::destroy($checklistIdsToDelete);

                    // registration has timetables?
                    $timetables = Timetable::where("course_student_id", "=", $registration["id"])->get(['id'])->toArray();

                    foreach ($timetables as $timetable) {
                        // timetable has events?
                        $events = Event::where("timetable_id", "=", $timetable["id"])->get(['id'])->toArray();
                        foreach ($events as $event) {
                            // event has attendancenotes?
                            $attendancenotes = Attendancenote::where("event_id", "=", $event["id"])->get(['id'])->toArray();
                            $attendancenoteIdsToDelete = array_map(function ($elm) {
                                return $elm["id"];
                            }, $attendancenotes);
                            Log::info("deleting attendancenotes (for event " . $event["id"] . "): " . join(", ", $attendancenoteIdsToDelete));
                            Attendancenote::destroy($attendancenoteIdsToDelete);

                            // event has tasks?
                            $tasks = Task::where("event_id", "=", $event["id"])->get(['id'])->toArray();
                            $taskIdsToDelete = array_map(function ($elm) {
                                return $elm["id"];
                            }, $tasks);
                            Log::info("deleting tasks (for event " . $event["id"] . "): " . join(", ", $taskIdsToDelete));
                            Task::destroy($taskIdsToDelete);

                            // finally, delete the event
                            Log::info("deleting event: " . $event["id"]);
                            Event::destroy($event["id"]);
                        }
                    }

                    $timetableIdsToDelete = array_map(function ($elm) {
                        return $elm["id"];
                    }, $timetables);
                    Log::info("deleting timetables (for registration " . $registration["id"] . "): " . join(", ", $timetableIdsToDelete));
                    Timetable::destroy($timetableIdsToDelete);
                }

                $registrationIdsToDelete = array_map(function ($elm) {
                    return $elm["id"];
                }, $registrations);

                $planningEntries = Planningentries::whereIn("registration_id", $registrationIdsToDelete)->get()->toArray();
                $planningEntryIdsToDelete = array_map(function ($elm) {
                    return $elm["id"];
                }, $planningEntries);
                Log::info("deleting planning entries for: " . join(", ", $registrationIdsToDelete));
                Planningentries::destroy($planningEntryIdsToDelete);

                Log::info("deleting registrations: " . join(", ", $registrationIdsToDelete));
                Registration::destroy($registrations);

                // Is this student part of a student group?
                Log::info("deleting student: " . $studentId . " from studentgroups");
                $q = "DELETE FROM student_studentgroup where student_id = ?";
                DB::delete($q, [$studentId]);

                Log::info("deleting student: " . $studentId . " \o/");
                Student::destroy($studentId);
                Log::info("***>> End delete all info of student with ID $studentId, deleted by admin account: " . Auth::user()->id);
            });
            return response()->json([
                "message" => "Student deleted successfully"
            ]);
        } else {
            return response()->json([
                "message" => "You are not authorized to delete this student"
            ], 403);
        }
    }

    /**
     * @return mixed
     */
    public function accesscodes()
    {
        return view('students.admin_access_tokens');
    }

    /**
     * @param string $year
     * @return mixed
     */
    public function apiStudentsForPlanning()
    {
        $students = Student::getCurrentlyActiveStudents();
        // check email entries
        $logentries = Emaillogentry::orderBy('updated_at')->get();
        $emailArchive = [];
        $skippedEntries = [];
        foreach ($logentries as $logentry) {
            // find the link
            $p1 = strpos($logentry->body, "schedulepreference/");
            if ($p1 !== false) {
                $p2 = strpos($logentry->body, "'>", $p1);
                if ($p2 !== false) {
                    $accessCode = substr($logentry->body, $p1 + 19, $p2 - ($p1 + 19));
                    if (empty($accessCode)) {
                        // usually a link but not an access code after schedulepreference/
                        $skippedEntries[] = $logentry->id;
                    }
                    $date = Mydat2Ndat::getNdat($logentry->updated_at);
                    // because of the order in the query, the last entry survives
                    $emailArchive[$accessCode] = $date;
                } else {
                    $skippedEntries[] = $logentry->id;
                }
            } else {
                $skippedEntries[] = $logentry->id;
            }
        }
        Log::info(
            "Found " . count($emailArchive) . " access codes in email log. " .
            "Skipped " . count($skippedEntries) . " logentries because we didn't find an access code in the message body"
        );
        $response = [
            "students" => $students,
            "emailArchive" => $emailArchive,
            "domain_schedule_threshold" => Auth::user()->domain->schedule_threshold
        ];
        return response()->json($response);
    }

    /**
     * delete accesscodes for multiple students
     * if filter == 'all': do just that
     * if filter == 'filter': only select students that have filled out there
     *                        preference table less than 60 days ago (configurable)
     * @param string $filter
     * @return \Illuminate\Http\JsonResponse resonse
     */
    public function revokeAccessFromAll($filter = '')
    {
        $daylimit = Auth::user()->domain->schedule_threshold;
        switch ($filter) {
            case "all":
                Log::info('revoking access from ALL students');
                $affected = DB::table('students')->update(array('has_access' => "0"));
                break;
            case "filter":
                Log::info("revoking access from all students that have updated their prefs in the last $daylimit days");
                $affected = DB::table('students as s')
                    ->select('s.id')
                    ->leftjoin('schedule_prefs as sp', 's.id', '=', 'sp.student_id')
                    ->whereRaw("DATEDIFF(now(),sp.updated_at) <= $daylimit")
                    ->groupBy('s.id')// distinct
                    ->update(array('has_access' => "0"));
                break;
            default:
                Log::error("No known filter specified ($filter) for deleting tokens");
                return response()->json(['result' => 'error', 'message' => 'no filter specified'], 400);
        }
        Log::info("Revoking access successful, $affected affected rows");
        return response()->json(['result' => 'Revoking access successful', 'affected' => $affected]);
    }

    /**
     * add accesscodes for multiple students
     * if a student already has an accesstoken: don't touch it
     * if filter == 'all': do just that
     * if filter == 'filter': only select students that have NOT filled out there
     *                        preference table less than 60 days ago (configurable)
     * @param string $filter
     * @return \Illuminate\Http\JsonResponse resonse
     */
    public function grantAccessToAllActive($filter = '')
    {
        $daylimit = Auth::user()->domain->schedule_threshold;
        switch ($filter) {
            case "all":
                Log::info('granting access for ALL students');
                $affected = DB::table('students AS s')
                    ->select('s.*')
                    ->distinct()
                    ->leftJoin('course_student AS cs', 's.id', '=', 'cs.student_id')
                    ->where('cs.start_date', '<=', DB::raw('NOW()'))
                    ->where(function ($query) {
                        $query->whereNull('end_date')
                            ->orWhere("end_date", ">", DB::raw("NOW()"));
                    })->update(array('has_access' => "1"));
                break;
            case "filter":
                Log::info("grant access for all students that have NOT updated their prefs in the last $daylimit days");
                $affected = DB::table('students as s')
                    ->select('s.id')
                    ->leftjoin('schedule_prefs as sp', 's.id', '=', 'sp.student_id')
                    ->leftjoin('course_student as cs', 's.id', '=', 'cs.student_id')
                    ->where('cs.start_date', '<=', DB::raw('NOW()'))
                    // (
                    ->where(function ($query) {
                        $query->whereNull('cs.end_date')
                            ->orWhere('cs.end_date', '>', DB::raw('DATE(NOW())'));
                    })
                    // )
                    // (
                    ->where(function ($query) use ($daylimit) {
                        $query->whereNull('sp.id')
                            ->orWhereRaw("DATEDIFF(now(),sp.updated_at) > $daylimit");
                    })
                    ->groupBy('s.id')// distinct
                    // )
                    ->update(array('has_access' => "1"));
                break;
            default:
                Log::error("No known filter specified ($filter) for adding tokens");
                return response()->json(['result' => 'error', 'message' => 'no filter specified'], 400);
        }

        Log::info("adding access successful, $affected affected rows");
        return response()->json(['message' => 'Granting access successful', 'affected' => $affected]);
    }


    /**
     * revoke access for a single student
     * @param int $studentId
     */
    public function removeAccess($studentId = 0)
    {
        Log::info("removing webaccess from student: $studentId");
        if ($studentId > 0) {
            Student::where("id", "=", $studentId)->update(array('has_access' => '0'));
            return response()->json(["message" => "web access removed successfully"]);
        }
        return response()->json(["message" => "error granting access"], 400);
    }

    /**
     * grant access to a single student
     * @param int $studentId
     */
    public function addAccess($studentId = 0)
    {
        Log::info("granting webaccess to student: $studentId");
        if ($studentId > 0) {
            Student::where("id", "=", $studentId)->update(array('has_access' => '1'));
            return response()->json(["message" => "web access granted successfully"]);
        }
        return response()->json(["message" => "error granting access"], 400);
    }

    /**
     * Show admin page student preferences
     * This is the admin version of the student page that needs an access token for admission
     * The student uses the route: SchedulepreferenceController -> edit
     *
     * @param $accessToken
     * @return mixed
     * @throws \Exception
     */
    public function preferencesPage($studentId)
    {
        return view('students.schedulepreferrences');
    }


    /**
     * Get and return a student group for API requests
     * @param Request $request
     * @param $stgid
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDetailsOfStudentgroup(Request $request, $stgid)
    {
        Log::info("request studentgroup with id: $stgid");
        $stgroup =- Studentgroup::with(['courses', 'students'])
            ->where('id', '=', $stgid)
            ->first();
        return response()->json($stgroup);
    }

    /**
     * Remove student from studentgroup
     * @param $stgid
     * @param $stid
     */
    public function delStudentFromStudentgroup(Request $request)
    {
        $stid = $request->stid;
        $stgid = $request->stgid;
        Log::info("delete student $stid from studentgroup with id: $stgid");
        $stgroup = Studentgroup::findOrFail($stgid);
        // remove student from relation
        $stgroup->students()->detach($stid);
    }

    /**
     * adds a student id to a student group
     * if the association is based on a trial course: add the id of the trial course
     * @param Request $request
     * @return void
     */
    public function addStudentToStudentgroup(Request $request)
    {
        $stid = $request->input("stid");
        $stgid = $request->input("stgid");
        Log::info("add student $stid to studentgroup with id: $stgid");
        $assocCourse = $request->associatedCourse;
        $course = Course::findOrFail($assocCourse);
        $as_trial_course = $course->is_trial_course ? $assocCourse : 0;
        $stgroup = Studentgroup::findOrFail($stgid);
        $stgroup->students()->attach($stid, ["as_trial_student" => $as_trial_course]);
    }

    /**
     * because the studentgroup is itself a student, we need to
     * exclude that type of student from te response
     *
     * @param $courseid
     * @param string $notingroup
     * @return \Illuminate\Http\JsonResponse
     */
    public function studentsWithCourse($courseid, $notingroup = 'false')
    {
        $excludeStudents = [];
        $toExclude = [];
        // which students to exclude
        if ($notingroup === 'true') {
            Log::info("excluding students already in group for course $courseid");
            // look for groups to exclude
            $groupIdToFilter = Studentgroup::select('students.id')
                ->leftJoin('course_student', 'course_student.student_id', '=', 'students.id')
                ->where('course_student.course_id', '=', $courseid)
                ->get()
                ->toArray();

            // which students NOT to return (because they are in a group for this course)
            $excludeStudents = DB::table('student_studentgroup')
                ->select("student_id")
                ->whereIn('studentgroup_id', $groupIdToFilter)
                ->get()
                ->toArray();

            // cast class to array
            foreach ($excludeStudents as $excludeStudent) {
                $toExclude[] = $excludeStudent->student_id;
            }
        }

        Log::info("select students for course $courseid, excluding " . count($excludeStudents) . " students.");
        $students = DB::table('students AS s')
            ->select('s.*')
            ->distinct()
            ->leftJoin('course_student AS cs', 's.id', '=', 'cs.student_id')
            ->where([
                ['cs.start_date', '<=', DB::raw('NOW()')],
                ['cs.course_id', '=', $courseid],
                ['s.firstname', '<>', '-'],
                ['s.date_of_birth', '<>', '1800-01-01']
            ])
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere("end_date", ">", DB::raw("NOW()"));
            })
            ->whereNotIn('s.id', $toExclude)
            ->orderBy('lastname')
            ->get();

        // add students in a related trial course


        return response()->json($students);
    }

    public function delCourseFromGroup(Request $request)
    {
        $stgid = $request->stgid;
        Log::info("removing course from studentgroup with id: $stgid");
        $stgroup = Studentgroup::findOrFail($stgid);
        $courses = $stgroup->courses;
        foreach ($courses as $course) {
            $r = Registration::findOrFail($course->pivot->id);
            $r->checklists()->delete();
            $r->delete();
        }
    }

    public function coursesNotTrial()
    {
        $courses = Course::where(function ($query) {
            $query->whereNull('is_trial_course')
                ->orWhere('is_trial_course', '=', '0');
        })
            ->with('recurrenceoption')
            ->orderBy("name")
            ->get();
        return response()->json($courses);
    }

    public function addCourseToGroup(Request $request)
    {
        $cid = $request->cid;
        $stgid = $request->stgid;
        Log::info("attaching course $cid to coursegroup: $stgid");
        $stgroup = Studentgroup::findOrFail($stgid);
        // attach registration
        $stgroup->courses()->attach($cid);
        $regid = $stgroup->getActiveRegistrations()[0]->id;
        Log::info("results in registration: $regid");
        return response()->json(['regid' => $regid]);
    }

    /**
     * Add pin to the student record, crypts it and
     * adds it to the corresponding classy users record
     * @param SavePinRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function savepin_c2(SavePinRequest $request)
    {
        $s = Student::findOrFail($request->studentid);
        $username = str_replace('@', '_at_', $s->planning_email);

        // do we have a classy_user? if not we need to create one now.
        $clu = Classyuser::where("username", "=", $username)->first();

        if (empty($clu)) {
            // create classy_user
            $clu = new Classyuser();
            $clu->domain_id = Auth::user()->domain->id;
            $clu->username = $username;
            $clu->student_id = $request->studentid;
        }

        $clu->password      = null;
        $clu->initial_pin   = $request->apipin;
        $clu->save();

        return response()->json(["result" => "pin added or updated"]);
    }

    /**
     * Add pin to the student record
     * PIN remains readable, the admin needs to be able to add give it to the student
     * This is visible in the students list
     * @param SavePinRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function savepin(SavePinRequest $request)
    {
        Log::info("updating PIN for student $request->studentid");
        $s = Student::findOrFail($request->studentid);
        if ($s->domain_id === Auth::user()->domain_id) {
            $s->apipin = $request->apipin;
            $s->save();
            return response()->json(["message" => "pin added or updated"]);
        }
        Log::error("Unable to save pin for student $s->id. Insufficient rights for user " . Auth::user()->id);
        return response()->json(["message" => "failed to save pin. Insufficient rights"], 403);
    }

    public function studentListContactsExport()
    {
        return Excel::download(new StudentListExport(), 'studentcontacts.xlsx');
    }

    /**
     * Exports current students for maillist import (excel format)
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportForMaillist(Request $request) {
        $sortOrder = $request['sortOrder'] ?: 'lastName';
        return Excel::download(new StudentMaillistExport($sortOrder), 'studentmaillist.xlsx');
    }

}
