<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Coursegroup;
use Illuminate\Support\Facades\Log;

class PricingController extends Controller
{
    /**
     * Get all active course groups for the requesting domain,
     * i.e.; there should be at least one course attached that is not archived.
     * At this point token validity has already been checked (middleware WordpressAuth).
     * The requesting domain id should be present in the request as attribute (also handled by the middleware)
     * @return \Illuminate\Http\JsonResponse
     */
    public function wppricing(Request $request)
    {
        $domainId = $request->attributes->get('requesting_domain', 0);
        $courseGroups = Coursegroup::getActiveCoursegroups($domainId);

        // remove groups that should not appear in the price list like trial lessons and rental
        $courseGroups = $courseGroups->filter(function ($courseGroup) {
            return !$courseGroup->ignore_for_price_list;
        });

        // Add the age range information to each course using the database fields
        foreach ($courseGroups as $courseGroup) {
            $coursesForThisCourseGroup = $courseGroup->courses;
            foreach ($coursesForThisCourseGroup as $course) {
                // Log warning if age range data is missing
                if (intval($course->target_age_min) === 0 || intval($course->target_age_max) === 0) {
                    Log::warning("Skipping course with id " . $course->id . " has missing age range data!");
                }
            }
        }

        return response()->json($courseGroups);
    }
}
