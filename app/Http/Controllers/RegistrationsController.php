<?php
/**
 * note: a registration is implemented by means of the table course_student
 */

namespace App\Http\Controllers;

use App\Http\Resources\ChecklistsResource;
use App\Http\Resources\RegistrationResource;
use App\Models\Checklist;
use App\Models\DefaultChecklist;
use App\Models\Domain;
use App\Models\Registration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\Mydat2Ndat;
use Scolavisa\scolib\StringManipulator;

class RegistrationsController extends Controller
{
    /**
     *
     * @param $registrationId
     * @return \Illuminate\Contracts\View\View
     */
    public function editregistration($registrationId)
    {
        Log::info("Getting registration info gor registration: $registrationId");
        $registration = Registration::findOrFail($registrationId);
        $studentId = $registration->student_id;
        return view('registrations.edit', ["registrationId" => $registrationId, "studentId" => $studentId]);
    }

    /**
     * @param $registrationId
     * @return RegistrationResource
     */
    public function getRegistration(Request $request, $registrationId)
    {
        $reg = Registration::where("id", "=", $registrationId)
            ->with("course.recurrenceoption")
            ->with("student.contacts")
            ->first();
        return new RegistrationResource($reg);
    }

    /**
     * Get the checklists for a specific registration
     * @param $registrationId
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function checklistsForRegistration($registrationId)
    {
        $checklists = Checklist::where('registration_id', '=', $registrationId)->get();
        return ChecklistsResource::collection($checklists);
    }

    /**
     * list all checklists of this domain
     * @return \Illuminate\Http\JsonResponse
     */
    public function defaultChecklists()
    {
        $defaultChecklists = DefaultChecklist::all();
        return response()->json($defaultChecklists);
    }

    /**
     * Update 1 item in a checklist
     * Expects $request to contain the fields 'itemnumber' and 'state'
     * @param Request $request
     * @param int $checklistid
     * @param int $registrationid
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function updateChecklistItem(Request $request, $checklistid = 0, $registrationid = 0)
    {
        $registration = Registration::findOrFail($registrationid);

        if (Auth::user()->domain->id === $registration->student->domain_id) {
            $checklist = Checklist::findOrFail($checklistid);
            // which field should we update?
            $field = "item" . $request->itemnumber . '_checked';
            $checklist->$field = $request->state ? 1 : 0;
            $checklist->save();
            // return the new collection of checklists
            return $this->checklistsForRegistration($registrationid);
        } else {
            return response()->json(['error' => 'not allowed'], 403);
        }
    }

    /**
     * Delete the checklist with ID $checklistid
     * @param int $checklistid
     * @param int $registrationid
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function delChecklist($checklistid = 0, $registrationid = 0)
    {
        $registration = Registration::findOrFail($registrationid);
        if (Auth::user()->domain->id === $registration->student->domain_id) {
            Checklist::findOrFail($checklistid)->delete();
            // return the new list of checklists
            return $this->checklistsForRegistration($registrationid);
        } else {
            return response()->json(['error' => 'not allowed'], 403);
        }
    }

    /**
     * Create a new checklist
     * and add it to the collection of checklists for this registration
     * @param int $defaultChecklistid
     * @param int $registrationid
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function addChecklist($defaultChecklistid = 0, $registrationid = 0)
    {
        $registration = Registration::findOrFail($registrationid);
        if (Auth::user()->domain->id === $registration->student->domain_id) {
            // create a new checklist, specifically for this registration
            $newChecklist = Checklist::createNewChecklist($defaultChecklistid, $registrationid);
            // add it to the collection
            $registration->checklists()->save($newChecklist);
            // return the new list of checklists
            return $this->checklistsForRegistration($registrationid);
        } else {
            return response()->json(['error' => 'not allowed'], 403);
        }
    }

    /**
     * @param int $registrationId
     *
     * @return \Illuminate\Contracts\View\View
     * @deprecated
     */
    public function editregistration_old($registrationId = 0)
    {
        Log::info("Getting registration info gor registration: $registrationId");
        $registration = Registration::findOrFail($registrationId);
        // determine possible student group for this students registration
        $std = $registration->student;
        $grps = $std->studentgroups;
        $grpRegistrationForCourse = 0;
        foreach ($grps as $grp) {
            $courseInGroup = $grp->courses; // will only be 1 course in a student group
            if ($courseInGroup[0]->id === $registration->course_id) {
                // this is the correct group, use this groups registration to create/edit the planning
                $grpRegistrationForCourse = $grp->getActiveRegistrations();
                $grpRegistrationForCourse = $grpRegistrationForCourse[0]->regid;
            }
        }

        if ($grpRegistrationForCourse > 0) {
            Log::info("This course is being taught in a group");
        } else {
            Log::info("This is an individual course");
        }
        // alle checklists ophalen om uit te kunnen kiezen
        $defaultChecklistst = DefaultChecklist::pluck('name', 'id')->all();
        if ((!empty($registration->checklist_id)) && ($registration->checklist_id > 0)) {
            $checklist = Checklist::findOrFail($registration->checklist_id);
        } else {
            $checklist = [];
        }
        $courseTaxRate = Auth::user()->domain->course_tax_rate;

        return view(
            'registrations.edit',
            compact(
                'registration',
                'defaultChecklistst',
                'checklist',
                'courseTaxRate',
                'grpRegistrationForCourse'
            )
        );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws \Exception
     */
    public function destroy($id)
    {
        $reg = Registration::findOrFail($id);

        // remove timetable if any (events are handled by cascading)
        Log::info("Deleting timetables associated with registration ID: $id");
        $reg->timetables()->delete();

        // remove checklist if any
        Log::info("Deleting checklists associated with registration ID: $id");
        $reg->checklists()->delete();

        // remove registration
        Log::info("Deleting registration with ID: $id");
        $reg->delete();
    }

    /**
     * open view for sign token
     * => public interface, no Auth object
     * @param $signToken
     *
     * @return $this|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function signRegistration($signToken)
    {
        Log::info("open registration conirmation view for $signToken");
        // check validity of token
        $registration = Registration::getRegistrationBySignCode($signToken);
        if (isset($registration)) {
            $student = $registration->student;
            $domain = Domain::findOrFail($student->domain_id);
            $domainLanguage = $domain->language;
            App::setLocale(
                in_array($domainLanguage, ['nl', 'en'])
                    ? $domainLanguage
                    : 'en'
            );

            if ((!empty($registration->signed)) && ($registration->signed == 1)) {
                Log::error("Registration id entered that has already been signed: " . $registration->id);
                return view('errors.404', [
                    "message" => trans('generic.alreadysigned')
                ], [
                    'id' => 'not_found',
                    'status' => '404'
                ]);
            } else {
                // happy flow
                // get the domain's tax rate (not logged in, so check the domain of the student of the registration)
                $course_tax_rate = $domain->course_tax_rate;
                // if this registration has an incidental tax rate
                if (!empty($registration->incidental_tax_rate)) {
                    $course_tax_rate = $registration->incidental_tax_rate;
                } elseif (!$registration->student->isAdult) {
                    $course_tax_rate = 0;
                }
                $schoolName = $domain->name;
                $termsUrl = $domain->rates_conditions_url;
                $privacyUrl = $domain->privacy_url;
                return view('registrations.sign_registration', [
                    'hideLogin' => true,
                    'courseTaxRate' => $course_tax_rate,
                    'schoolName' => $schoolName,
                    'termsUrl' => $termsUrl,
                    'privacyUrl' => $privacyUrl
                ])->with(['registration' => $registration]);
            }
        } else {
            Log::error('Sign id entered that does not belong to a registration: ' . $signToken);
            return view('errors.404', [
                "message" => trans("generic.regidnotvalid")
            ], [
                'id' => 'not_found',
                'status' => '404'
            ]);
        }
    }

    /**
     * Save the confirmation / signature for registration
     * => public interface, no Auth object
     * @param Request $request
     * @param $regId
     * @param $signToken
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function saveSigningOfRegistration(Request $request, $regId, $signToken)
    {
        Log::info("saving sign request");
        // check validity of token
        $ip = $_SERVER['REMOTE_ADDR'];
        $registration = Registration::getRegistrationBySignCode($signToken);
        $student = $registration->student;
        $domain = Domain::findOrFail($student->domain_id);
        $domainLanguage = $domain->language;
        App::setLocale(
            in_array($domainLanguage, ['nl', 'en'])
                ? $domainLanguage
                : 'en'
        );
        if (isset($registration)) {
            if ($registration->id == $regId) {
                // save
                if ($request->iagree === 'on') {
                    $registration->signed = 1;
                    $registration->signed_at = DB::raw('NOW()');
                    $registration->signed_user_agent = "/ IP: $ip" . $_SERVER["HTTP_USER_AGENT"];
                    $registration->save();
                    // agree social share - save in students-table
                    $student->agreeSocialShare =
                        $request->iagreesocial && $request->iagreesocial === 'on' ? '1' : '0';
                    Log::info("setting agreesocial for student $student->id to $student->agreeSocialShare");
                    $student->save();
                    return view('registrations.sign_registration_thankyou', ['hideLogin' => true]);
                } else {
                    Log::error('\'I Agree\' checkbox was not ticked');
                    return view('errors.404', [
                        "message" => trans("generic.iagreecheckboxnotticked")
                    ], [
                        'id' => 'not_found',
                        'status' => '404'
                    ]);
                }
            } else {
                Log::error('Registration not valid');
                return view('errors.404', [
                    "message" => trans("generic.regidnotvalid")
                ], [
                    'id' => 'not_found',
                    'status' => '404'
                ]);
            }
        } else {
            Log::error('Registration not found');
            return view('errors.404', [
                "message" => trans("generic.regidnotvalid")
            ], [
                'id' => 'not_found',
                'status' => '404'
            ]);
        }
    }

    /**
     * Remove checklist from registration
     * -- this deletes the checklist --
     *
     * @param int $registrationId
     * @param int $checklistId
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function removechecklist($registrationId = 0, $checklistId = 0)
    {
        $registration = Registration::findOrFail($registrationId);
        // only delete if this checklist is coupled to this registration
        if ($registration->checklist_id == $checklistId) {
            $registration->checklist_id = null;
            $registration->save();
            // delete checklist
            Checklist::destroy($checklistId);
        }
        // return to registrations edit
        return redirect()->route(
            'editregistration',
            [$registrationId]
        )->with(
            'message',
            ucfirst(trans("generic.checklistdeleted"))
        );
    }

    /**
     * Get all checklists for this registration
     * @param $registrationId
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllChecklists($registrationId)
    {
        $retArr = [];
        // check if the param is an int (the pk) or already an registration model object
        $registration = (gettype($registrationId) === "string" || gettype($registrationId) === "integer") ?
            Registration::findOrFail($registrationId) :
            $registrationId;

        foreach ($registration->checklists as $checklist) {
            $retArr[] = [
                'id' => $checklist->id,
                'name' => $checklist->name,
                'startDT' => Mydat2Ndat::getNdat($checklist->created_at),
                'isComplete' => $checklist->isComplete()
            ];
        }
        return response()->json($retArr);
    }

    /**
     * Incidentally override the default price of the course (special cases)
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function priceOverride(Request $request)
    {
        $registration = Registration::findOrFail($request->input('regId'));
        $registration->incidental_price_ex_tax = StringManipulator::price2database($request->input('price'));
        try {
            $registration->save();
            return response()->json(["message" => "price updated"]);
        } catch (\Exception $e) {
            return response()->json(["message" => "price could not be saved: " . $e->getMessage()], 422);
        }
    }

    /**
     * Reset Incidentally override of default price of the course
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetIncidentalPrice(Request $request)
    {
        $registration = Registration::findOrFail($request->input('regId'));
        $registration->incidental_price_ex_tax = null;
        try {
            $registration->save();
            return response()->json(["message" => "price rest"]);
        } catch (\Exception $e) {
            return response()->json(["message" => "price could not be reset: " . $e->getMessage()], 422);
        }
    }

    /**
     * Incidentally override standard tax rate (which is bases on students age)
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function taxRateOverride(Request $request)
    {
        $registration = Registration::findOrFail($request->input('regId'));
        $registration->incidental_tax_rate = StringManipulator::float2database($request->input('taxrate'));
        try {
            $registration->save();
            return response()->json(["message" => "tax rate updated"]);
        } catch (\Exception $e) {
            return response()->json(["message" => "tax rate could not be saved: " . $e->getMessage()], 422);
        }
    }

    /**
     * Reset incidentally override standard tax rate
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetIncidentalTaxRate(Request $request)
    {
        $registration = Registration::findOrFail($request->input('regId'));
        $registration->incidental_tax_rate = null;
        try {
            $registration->save();
            return response()->json(["message" => "tax rate rest"]);
        } catch (\Exception $e) {
            return response()->json(["message" => "tax rate could not be reset: " . $e->getMessage()], 422);
        }
    }
}
