<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContactMailRequest;
use App\Http\Requests\MailTemplateRequest;
use App\Models\Coursegroup;
use App\Models\Emaillogentry;
use App\Models\Mailtemplate;
use App\Models\Registration;
use App\Models\Student;
use App\Models\Studentgroup;
use App\Models\Tutor;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Jobs\ProcessMailgunApiCallJob;

class EmailController extends Controller
{
    public function mailtemplates() {
        return view('email.templates');
    }

    /**
     * Get mailtemplates
     * @param string $forTarget get only templates meant for this target
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMailTemplates($forTarget='')
    {
        if ($forTarget === '') {
            $templates = Mailtemplate::all();
        } else {
            $templates = Mailtemplate::where('targets', '=', $forTarget)->get();
        }
        return response()->json($templates);
    }

    public function updateMailTemplate(MailTemplateRequest $request)
    {
        $template = Mailtemplate::where([
            "id" => $request->id, 
            "domain_id" => Auth::user()->domain->id
        ])->first();
        if ($template->id) {
            $template->content = $request->mcontent;
            $template->targets = $request->mtarget;
            $template->save();
            return response()->json(["result" => "success"]);
        } else {
            return response(403)->json(["result" => "fail"]);
        }
    }

    public function createMailTemplate(MailTemplateRequest $request)
    {
        $template = new Mailtemplate();
        $template->domain_id = Auth::user()->domain->id;
        $template->label = $request->mlabel;
        $template->targets = $request->mtarget;
        $template->content = $request->mcontent;
        $template->save();
        return response()->json([
            "result" => "success", 
            "newTemplateId" => $template->id
        ]);
    }

    public function deleteMailTemplate (Request $request, $templateId)
    {
        Log::info("Deleting template id: $templateId");
        $template = Mailtemplate::where([
            "id" => $templateId, 
            "domain_id" => Auth::user()->domain->id
        ])->first();
        if ($template->destroy($templateId)) {
            return response()->json(["result" => "success"]);
        } else {
            return response(403)->json(["result" => "fail"]);
        }
    }

    public function getTemplateContent($id)
    {
        $template = Mailtemplate::findOrFail($id);
        return response()->json($template);
    }

    /**
     * list all available template variables and prefills with the data as far as possible
     * @param int $registrationId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTemplateVariables($registrationId=0)
    {
        $templateVariables = [
            "%studentfirstname%",
            "%studentfullname%",
            "%studentaccesslink%",      // used for preferences
            "%registrationsignlink%",   // used for registration signature
            "%studentaccesstoken%",     // generic use
            "%classyactivationlink%",
            "%schoolname%",
            "%schoolcontactperson%",
            "%schoollogo|width='100'%",
            "%schooltelephone%",
            "%schoolwebsite%",
            "%salutationforfinancial%",
            "%salutationforplanning%",
            "%salutationforpromotion%",
        ];

        // prefill with data
        $registration = Registration::find($registrationId);
        $student = $registration->student ?? null;
        $domain = Auth::user()->domain;
        $studentAccessLink = "<a href='" .
            url('/schedulepreference/' . ($student->accesstoken ?? '')) . "'>" .
            trans('generic.clickhere') . "</a>";
        $registraionSignLink = url('/registration/signregistration/' . ($registration->sign_code ?? ''));
        $registraionSignAnchorPlusRawLink = "<p><a href='$registraionSignLink'>" .
            trans('generic.confirmmyregistration') . "</a></p><p>" . $registraionSignLink . "</p>";
        $templateVariablesData = [
            "%studentfirstname%" => $student->firstname ?? '',
            "%studentfullname%" => $student->name ?? '',
            "%studentaccesslink%" => $registrationId > 0 ? $studentAccessLink : '',
            "%registrationsignlink%" => $registrationId > 0 ? $registraionSignAnchorPlusRawLink : '',
            "%studentaccesstoken%" => $student->accesstoken ?? '',
            "%classyactivationlink%" => $student->accesstoken ?? '',
            "%schoolname%" => $domain->name ?? '',
            "%schoolcontactperson%" => $domain->contact_person_name ?? '',
            "%schoollogo|width='100'%" => $domain->logo_url ?? '',
            "%schooltelephone%" => $domain->telephone ?? '',
            "%schoolwebsite%" => $domain->website_url ?? '',
            "%salutationforfinancial%" => $student->financialSalutation ?? '',
            "%salutationforplanning%" => $student->planningSalutation ?? '',
            "%salutationforpromotion%" => $student->promotionsSalutation ?? ''
        ];
        return response()->json(["variables" => $templateVariables, "data" => $templateVariablesData]);
    }

    public function mailContacts()
    {
        return view('email.contacts');
    }

    /**
     * Return the count of students with active registrations and at least one email address of the requested type
     * and tutors with at least one email address.
     * The request will specify the email type (financial, planning, promotions) and student, tutor or both
     * Example request: /api/contacts/email?students=true&staff=true&email_type=promotions
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function countContacts(Request $request)
    {
        $emailType = $request->email_type;
        // convert financial to finance because the database uses 'finance'
        if ($emailType === 'financial') {
            $emailType = 'finance';
        }
        $students = $request?->students === 'true';
        $staff = $request?->staff === 'true';
        $courseGroupFilterForStudents = $request?->course_group ?? '';
        $studentGroupFilterForStudents = $request?->student_group ?? '';
        // string->array, separate by ',' - but only if it's not '' because that would initialize the array with [0] (why?)
        $courseGroupFilterForStudents = $courseGroupFilterForStudents === '' ? [] : explode(',', $courseGroupFilterForStudents);
        $studentGroupFilterForStudents = $studentGroupFilterForStudents === '' ? [] : explode(',', $studentGroupFilterForStudents);

        $responseArray = $this->getContactsToBeEmailed(
            $emailType,
            $students,
            $staff,
            $courseGroupFilterForStudents,
            $studentGroupFilterForStudents
        );

        return response()->json($responseArray);
    }

    /**
     * Get the email addresses of the students and staff to be emailed
     * @param string $emailType
     * @param bool $students
     * @param bool $staff
     * @param array $courseGroupFilterForStudents
     * @param array $studentGroupFilterForStudents
     * @return array
     */
    private function getContactsToBeEmailed(
        string $emailType = "",
        bool   $students = false,
        bool   $staff = false,
        array  $courseGroupFilterForStudents = [],
        array  $studentGroupFilterForStudents = []
    ) {
        $allStudentAddresses = [];
        $allTutorAddresses = [];
        $responseArray = [];

        // ------------------------------------------------------------------
        // students have multiple email addresses and an email type
        // ------------------------------------------------------------------
        if ($students) {
            $studentIdsOfCoursegroup = [];
            $studentIdsOfStudentgroup = [];
            if (count($courseGroupFilterForStudents) > 0) {
                Log::info("Filtering students on course groups: " . implode(',', $courseGroupFilterForStudents));
                // to make sure if the chosen course group is empty, we don't get all students,
                // initialize with 0 in the array
                array_push($studentIdsOfCoursegroup, 0);
                // only keep student id's that are actively in the requested course groups
                foreach ($courseGroupFilterForStudents as $courseGroupId) {
                    $courseGroup = Coursegroup::find($courseGroupId);
                    if ($courseGroup) {
                        $studentIdsOfCoursegroup = array_merge($studentIdsOfCoursegroup, $courseGroup->getActiveStudentIds());
                    }
                }
            }
            if (count($studentGroupFilterForStudents) > 0) {
                Log::info("Filtering students on student groups: " . implode(',', $studentGroupFilterForStudents));
                // to make sure if the chosen student group is empty, we don't get all students,
                // initialize with 0 in the array
                array_push($studentIdsOfStudentgroup, 0);
                // only keep student id's that are actively in the requested student groups
                foreach ($studentGroupFilterForStudents as $studentGroupId) {
                    $studentGroup = Studentgroup::find($studentGroupId);
                    if ($studentGroup) {
                        $studentIdsOfStudentgroup = array_merge($studentIdsOfStudentgroup, $studentGroup->getActiveStudentIds());
                    }
                }
            }
            $onlyReturnTheseStudents = array_merge($studentIdsOfCoursegroup, $studentIdsOfStudentgroup);
            $onlyReturnTheseStudents = array_unique($onlyReturnTheseStudents);

            // convert financial to finance because the database uses 'finance'
            // Spec must be one of planning, finance, promotions, all, first
            if ($emailType === 'financial') {
                $emailType = 'finance';
            }
            $activeStudentsQuery = Student::whereHas('registrations', function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>', Carbon::now());
            });
            if (count($onlyReturnTheseStudents) > 0) {
                Log::info("Only return these students: " . implode(',', $onlyReturnTheseStudents));
                $activeStudentsQuery->whereIn('id', $onlyReturnTheseStudents);
            }
            $activeStudents = $activeStudentsQuery->get();

            foreach($activeStudents as $student) {
                $email = $student->getContactWithSpecOrFirstOfType('email', $emailType);
                // $email may be comma separated list of emails if multiple entries of a type
                // have been checked in the student card, so split and add each
                $emails = explode(',', $email);
                foreach ($emails as $email) {
                    // this will deduplicate the addresses
                    $allStudentAddresses[$email] = 1;
                }
            }
            $responseArray = [
                "studentAddresses" => array_keys($allStudentAddresses),
                "students" => count($allStudentAddresses)
            ];
        }

        // ------------------------------------------------------------------
        // staff has mandatory exactly 1 email address and no email type
        // ------------------------------------------------------------------
        if ($staff) {
            // get tutors where is_blocked field is 0 and without and end_date in the role_user table
            $activeTutors = Tutor::getActiveTutors();
            foreach ($activeTutors as $tutor) {
                if ($tutor->is_blocked !== 1) {
                    $allTutorAddresses[$tutor->email] = 1;
                }
            }
            $responseArray = array_merge($responseArray, [
                "staff" => $activeTutors->count(),
                "staffAddresses" => array_keys($allTutorAddresses)
            ]);
        }
        return $responseArray;
    }

    /**
     * Save the uploaded images in the public/images folder of the domain
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImages(Request $request)
    {
        // obfuscated version of the domain id (pk: unique and won't ever change)
        $domainIdentifier = md5(Auth::user()->domain->id);
        $uploadTargetFolder = '/storage/' . $domainIdentifier . '/images/email/';
        // images are array of files in the request images[]
        // each image is max 100kb and of type jpeg, png, jpg, gif, svg
        // total images size is max 8MB
        $request->validate([
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:1000',
        ]);
        // store the images in the public/images folder
        $images = [];
        // check if the public path exists and is writable
        if (!is_dir(public_path($uploadTargetFolder))) {
            mkdir(public_path($uploadTargetFolder), 0777, true);
        }
        foreach ($request->file('images') as $image) {
            $imageSize = $image->getSize();
            // store the image metadata before moving the file
            $imageOriginalName = $image->getClientOriginalName();
            $imageName = time() . "_" . md5($imageOriginalName) . '.'.$image->extension();
            $image->move(public_path($uploadTargetFolder), $imageName);
            $images[] = [
                'original_name' => $imageOriginalName,
                'size' => $imageSize,
                'name_in_url' => $imageName
            ];
        }
        Log::info(count($images) . " image(s) uploaded for email to targetdir: $uploadTargetFolder");
        // return list of filenames
        return response()->json([
            'baseUrlToImages' => url($uploadTargetFolder),
            'images' => $images,
            'message' => trans_choice('generic.youhavesuccessfullyuploadedimages', count($images), ['nrofimages' => count($images)])
        ]);
    }

    public function sendMailToContacts(ContactMailRequest $request)
    {
        // array->string
        $courseGroupFilter = empty($request->courseGroup) || !is_array($request->courseGroup)
            ? []
            : $request->courseGroup;
        $studentGroupFilter = empty($request->studentGroup) || !is_array($request->studentGroup)
            ? []
            : $request->studentGroup;
        // merge all email addresses into one array
        $allEmailAddresses = $this->getContactsToBeEmailed(
            $request->emailType,
            $request->cbStudents,
            $request->cbStaff,
            $courseGroupFilter,
            $studentGroupFilter
        );
        $allEmailAddresses = array_merge($allEmailAddresses['studentAddresses'] ?? [], $allEmailAddresses['staffAddresses'] ?? []);
        if (!empty($request->extraEmailAddresses) && is_array($request->extraEmailAddresses)) {
            $allEmailAddresses = array_merge($allEmailAddresses, $request->extraEmailAddresses);
        }
        // remove duplicates
        $allEmailAddresses = array_unique($allEmailAddresses);
        if (count($allEmailAddresses) === 0) {
            Log::info("No email addresses found to send to");
            return response()->json(["message" => trans('generic.noemailaddressfoundtosendto')], 403);
        }
        Log::info("Sending mail to " . count($allEmailAddresses) . " addresses");
        $body = view('email.emailcontacts', [
            "body" => $request->mailBody,
            "title" => $request->mailSubject,
            "domain" => Auth::user()->domain
        ])->render();

        $mailData = [
            'from' => Auth::user()->domain->email,
            'subject' => $request->mailSubject,
            'body' => $body
        ];
        foreach ($allEmailAddresses as $emailAddress) {
            $mailData['to'] = $emailAddress;
            $uniqueToken = md5($emailAddress . $mailData['subject'] . $mailData['body']);
            $data['token'] = $uniqueToken;
            $data['domain_id'] = Auth::user()->domain->id;
            $data['mail_data'] = $mailData;
            ProcessMailgunApiCallJob::dispatch($data);
            Log::info(" - Dispatched job for $emailAddress, token: $uniqueToken, status: queued");
            // Update email log. This is a log of all emails queued, sent, failed
            Emaillogentry::insert([
                'domain_id' => Auth::user()->domain->id,
                'to' => $emailAddress,
                'from' => $mailData['from'],
                'subject' => $mailData['subject'],
                'body' => $mailData["body"],
                'unique_token' => $uniqueToken,
                'status' => 'queued',
                'log' => date("Y-m-d H:m:s") . ' - ' . trans('email.statusClassWillSendThisEmailToRecipient'),
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now()
            ]);
        }

        return response()->json(["message" => trans('email.mail_sent_successfully')]);
    }

}
