<?php

namespace App\Http\Controllers;

use App\Http\Requests\StudentGroupRequest;
use App\Http\Resources\StudentgroupResource;
use App\Http\Resources\StudentResource;
use App\Models\Course;
use App\Models\Event;
use App\Models\RecurrenceOption;
use App\Models\Registration;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Studentgroup;
use App\Models\Timetable;
use App\Models\TrialcourseRelation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StudentgroupsController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        return view('studentgroups.list');
    }

    /**
     * Get all student groups
     *
     * If the user is an admin, this method retrieves all the student groups along with their students and courses.
     * If the user is a teacher, this method only retrieves the student groups associated with the teacher,
     * along with their students and courses.
     * The function adds the number of future appointments and the number of appointments to each student group.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection   Collection of StudentgroupResource objects representing the student groups
     */
    public function getAll()
    {
        Log::info("Getting all studentgroups");
        // if the user is a teacher we only return their own students
        if(!Auth::user()->userIsA('admin')) {
            $tutor = Tutor::findOrFail(Auth::id());
            $sgs = $tutor->myStudentGroups;
        } else {
            $sgs = Studentgroup::with("students", "courses")->orderBy('lastname')->get();
        }

        $schoolYear = Schoolyear::getCurrentOrFuture();
        foreach ($sgs as $sg) {
            $sg->future_appointments = 0;
            $sg->appointments = 0;
            $sg->course = null;
            if (isset($sg->courses[0])) {
                $registration = Registration::findOrFail($sg->courses[0]->pivot->id);
                $sg->future_appointments = $registration->getNrOfAppointments(true, $schoolYear);
                $sg->appointments = $registration->getNrOfAppointments(false, $schoolYear);
                $sg->course = $sg->courses[0];
                unset($sg->courses);
            }
        }
        return StudentgroupResource::collection($sgs);
    }

    /**
     * Get a studentgroup by its ID
     * The function adds the number of future appointments and the number of appointments to the student group.
     *
     * @param int $id The ID of the studentgroup
     * @return StudentgroupResource The studentgroup resource
     */
    public function get($id)
    {
        $sg = Studentgroup::with("students", "courses.recurrenceoption")
            ->where('id', '=', $id)
            ->first();
        $schoolYear = Schoolyear::getCurrentOrFuture();
        $sg->future_appointments = 0;
        $sg->appointments = 0;
        $sg->course = null;
        if (isset($sg->courses[0])) {
            $registration = Registration::findOrFail($sg->courses[0]->pivot->id);
            $sg->future_appointments = $registration->getNrOfAppointments(true, $schoolYear);
            $sg->appointments = $registration->getNrOfAppointments(false, $schoolYear);
            $sg->course = $sg->courses[0];
            unset($sg->courses);
        }
        return StudentgroupResource::make($sg);
    }

    /**
     * Show the form for creating a new resource.
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(Request $request)
    {
        $course = isset($request->course_id) ? Course::findOrFail($request->course_id) : null;
        return view('studentgroups.create', compact('course'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     * @deprecated
     */
    public function store(Request $request)
    {

        $studentgroup = new Studentgroup();
        $name = '-';
        $name .= ' ' . $request->lastname;
        $studentgroup->domain_id = Auth::user()->domain->id;

        $studentgroup->name = $name;
        $studentgroup->firstname = "-";
        $studentgroup->lastname = $request->lastname;
        $studentgroup->date_of_birth = "1800-01-01";

        $studentgroup->status = 'ACTIVE'; // this is probably no longer needed. leave for now, it's a mandatory field
        $studentgroup->save();
        // now i have an ID. create accesstoken - not sure yet if we need this for groups
        $studentgroup->accesstoken = md5($studentgroup->id . $name . $studentgroup->id);
        $studentgroup->save();

        // if a course id was send along with this request, connect the student group with that course
        if (isset($request->course_id)) {
            $course = Course::findOrFail($request->course_id);
            // but only if it's the correct domain (logged-in user)
            if ($course->domain_id === Auth::user()->domain->id) {
                $studentgroup->courses()->attach($request->course_id);
            }
        }

        // now go to the edit screen
        return redirect()->route('studentgroups.edit', $studentgroup->id)->with('message', ucfirst(trans("generic.datasaved")));
    }

    /**
     * Create a new student group from API call
     * @param StudentGroupRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiStore(StudentGroupRequest $request)
    {
        Log::info("Create new studentgroup");
        $studentgroup = new Studentgroup();
        $studentgroup->domain_id = Auth::user()->domain->id;
        $studentgroup->name = "- $request->lastname";
        $studentgroup->status = 'ACTIVE'; // this is probably no longer needed. leave for now, it's a mandatory field
        $studentgroup->firstname = "-";
        $studentgroup->lastname = $request->lastname;
        $studentgroup->date_of_birth = "1800-01-01";
        $studentgroup->has_access = 0;
        $studentgroup->save();

        return response()->json([
            "message"   => "save successfull",
            "id"        => $studentgroup->id
        ]);
    }

    public function apiUpdate(Request $request, $stgid)
    {
        Log::info("Update studentgroup $stgid");
        $studentgroup = Studentgroup::where("id", "=", $stgid)->first();
        if ( (isset($request->lastname)) && ($request->lastname !== "") ) {
            $lastname = $request->input("lastname");
            $studentgroup->name = "- $lastname";
            $studentgroup->lastname = $lastname;
            $studentgroup->save();
        }

        // associated models
        if ( (isset($request->courseId)) && ($request->courseId !== 0) ) {
            // check if allowed, then attach
            $course = Course::where('id', '=', $request->input('courseId'))->first();
            $studentgroup->courses()->attach($course->id);
        }
        if ( (isset($request->studentId)) && ($request->studentId !== 0) ) {
            // check if excists
            $student = Student::where('id', '=', $request->input('studentId'))->first();
            // check if already in this group
            $alreadyInGroup = $studentgroup->students()->where('student_id', '=', $student->id)->first();
            if (empty($alreadyInGroup)) {
                // check if the basis of participation is a trial course or a regular course
                $courses = $student->courses;
                $connectedCourse = $studentgroup->courses()->first();
                // check if the student has the connected course in their list. If not, then this is a trial participation
                $isTrialParticipant = true;
                foreach ($courses as $course) {
                    if ($course->id === $connectedCourse->id) {
                        $isTrialParticipant = false;
                    }
                }
                if ($isTrialParticipant) {
                    // check which course is the trial course
                    $trialCourseForThisCourse = $courses->filter(function ($course) use ($connectedCourse) {
                        return $course->isTrialCourseFor($connectedCourse->id);
                    })->first();

                }
                $studentgroup->students()->attach($student->id, [
                    'start_date' => date('Y-m-d'),
                    'end_date' => null,
                    'as_trial_student' => $isTrialParticipant && isset($trialCourseForThisCourse->id)
                        ? $trialCourseForThisCourse->id
                        : 0
                ]);
            }
        }
    }

    /**
     * Get all students that could possibly take part in a student group
     * Don't return:
     * - already in this student group
     * - already in a student group that teaches the same course
     */
    public function getPossibleStudentsForStudentgroup(Request $request, $stgid)
    {
        Log::info("getting possible students for group $stgid");
        $stg = Studentgroup::where([
            ['id', '=', $stgid],
            ['domain_id', '=', Auth::user()->domain_id]
        ])
            ->first();

        if (empty($stg)) {
            return response()->json(['message' => 'studentgroup not found'], 403);
        }
        if (count($stg->courses) === 0) {
            return response()->json(['message' => 'studentgroup has no associated course yet'], 401);
        }
        $course = $stg->courses[0];
        Log::debug("get trialcourse ids on top of groups course id");
        // get all students that are registered to this course
        // plus students that are registered to a trial course that leads to this course
        $courseIds = TrialcourseRelation::where('course_id', '=', $course->id)
            ->pluck("trialcourse_id")
            ->all();
        $courseIds[] = $course->id;
        $studentIds = Registration::whereIn('course_id', $courseIds)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=',DB::raw('NOW()'));
            })
            ->pluck("student_id")
            ->all();
        // any other student groups teaching the same?
        $allGroupsForCourse = $course->studentgroups;
        // which students are in those?
        foreach ($allGroupsForCourse as $groupForCourse) {
            // don't remove what's already there.
            // after a delete action in de FE all students should again be attachable
            // if they're not returned here, the FE has no knowledge about them
            if ($groupForCourse->id !== $stgid) {
                $students = $groupForCourse->students;
                foreach ($students as $student) {
                    if (in_array($student->id, $studentIds)) {
                        Log::debug("skipping student $student->id because of other studentgroup");
                        // diff keeps everything that is not in both arrays
                        $studentIds = array_diff($studentIds, [$student->id]);
                    }
                }
            }
        }

        // are there students left that follow this course individually?
        $studentRegistrationsThatHaveThisCourse =
            Registration::select('course_student.id AS regId', 'students.id as studentId')
                ->leftJoin('students', 'student_id', '=', 'students.id')
                ->whereIn('student_id', $studentIds)
                ->where(function($query) {
                    $query->where('firstname', '<>',  '-')
                        ->where('date_of_birth',  '<>', '1800-01-01');
                })
                ->where('course_id', '=', $course->id)
                ->where(function($query) {
                    $query->whereNull('end_date')
                        ->orWhere('end_date', '>=',DB::raw('NOW()'));
                })->get();
        // if these registrations have any future events: skip the associated student
        foreach ($studentRegistrationsThatHaveThisCourse as $reg) {
            $r = Registration::findOrFail($reg->regId);
            if (count($r->events) > 0) {
                Log::debug("skipping student $reg->studentId because of planned individually");
                // diff keeps everything that is not in both arrays
                $studentIds = array_diff($studentIds, [$reg->studentId]);
            }
        }

        // now get all students
        Log::debug("get data for all students that are still potentially addable");
        $students = Student::whereIn('id', $studentIds)->get();
        return StudentResource::collection($students);
    }

    /**
     * Only if no associated students and no associated events
     * @param $stgid
     * @param $courseId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeCourseFromStudentGroup($stgId, $courseId)
    {
        Log::info("Remove course $courseId from studentgroup $stgId");
        $studentgroup = Studentgroup::findOrFail($stgId);
        if (empty($studentgroup)) {
            return response()->json([
                "message" => "Cant find student group"
            ], 401);
        }
        if ((count($studentgroup->students) === 0) && (count($studentgroup->courses) === 1)) {
            // does this course registration have any future events in the planning?
            $tt = Timetable::where([
                ['course_student_id', $studentgroup->courses[0]->pivot->id],
                ['schoolyears.end_year', '>=', date('Y')]
            ])
                ->leftJoin('schoolyears', 'timetables.schoolyear_id', '=', 'schoolyears.id')
                ->pluck('timetables.id')
                ->all();

            $events = Event::where('datetime', '>=', DB::raw('now()'))
                ->whereIn('timetable_id', $tt)
                ->get();

            if (count($events) > 0) {
                // error: er zijn nog events in de planning
                return response()->json([
                    "message" => "please remove future events from planning before removing course. " . count($events) . " events found"
                ], 401);
            } else {
                try {
                    $studentgroup->courses()->detach($courseId);
                    return response()->json([
                        "message" => "course detached from student group"
                    ]);
                } catch (\Exception $e) {
                    return response()->json([
                        "message" => "error detaching course: possible attendancenotes coupled to past events? " . $e->getMessage()
                    ], 401);
                }
            }
        } else {
            // error still students attached to this group or no course found
            return response()->json([
                "message" => "please remove students before removing course"
            ], 401);
        }
    }

    public function removestudentfromstudentgroup($stgId, $studentId)
    {
        Log::info("Remove student $studentId from studentgroup $stgId");
        $studentgroup = Studentgroup::findOrFail($stgId);
        if (empty($studentgroup)) {
            return response()->json([
                "message" => "Cant find student group"
            ], 401);
        }
        // set enddate for the student with student_id = $studentId in the studentgroup registration
        $studentgroup->students()->updateExistingPivot($studentId, ['end_date' => date('Y-m-d')]);

        return response()->json([
            "message" => "student detached from student group"
        ]);
    }

    /**
     * This function returns all ids of courses that should _never_ be associated
     * with a student group. This basically means all courses that have only one recurrence
     * Currently not in use
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCourseIdsToExclude()
    {
        $optionsToFilter = RecurrenceOption::where("ends_after_nr_of_occurrences", "=", "1")
            ->pluck('id')
            ->all();
        $courseIds = Course::whereIn("recurrenceoption_id", $optionsToFilter)
            ->pluck('id')
            ->all();
        return response()->json($courseIds);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        Log::info("Fetching studentgroup");
        $studentgroup = Studentgroup::findOrFail($id);
        // logic is largely done by Vue, no need to add any more models to the view
        return view('studentgroups.edit', compact('studentgroup'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        Log::info("Saving Studentgroup data");
        $studentgroup = Studentgroup::findOrFail($id);

        $studentgroup->name = "- $request->lastname";
        $studentgroup->lastname = $request->lastname;

        $studentgroup->has_access = $request->has_access;
        $studentgroup->remarks = $request->remarks; // lijkt me niet meer van toepassing (is nu het logboek)
        $studentgroup->save();
        Log::info("base data saved");
        // course update (n:m)
        $syncArray = [];
        $theCourses = $request->get('courses');
        if (!empty($theCourses) && is_array($theCourses) && count($theCourses) > 0) {
            $theStartdates = $request->get('startdates');
            $theEnddates = $request->get('enddates');
            $theRegIds = $request->get('regid');
            $theSigned = $request->get('signed');
            // loop through courses
            foreach ($theCourses as $idx => $theCourse) {
                // the empty course placeholder will also be in the request.
                // recognise that it is empty
                if (!empty($theCourse)) {
                    if (!empty($theRegIds[$idx])) {
                        $tempRegArray = Registration::findOrFail($theRegIds[$idx])->toArray();
                    } else {
                        $tempRegArray = ["id" => null];
                    }
                    // mutable   - these field may be mutated by the interface
                    $tempRegArray["start_date"] = $theStartdates[$idx] == '' ? Date("Y-m-d") : Ndat2Mydat::getMydat($theStartdates[$idx]); // if empty: today.
                    $tempRegArray["end_date"] = empty($theEnddates[$idx]) ? null : Ndat2Mydat::getMydat($theEnddates[$idx]); // if empty: null
                    $tempRegArray["course_id"] = $theCourse;
                    $tempRegArray["student_id"] = $studentgroup->id;
                    $tempRegArray["signed"] = empty($theSigned[$idx]) ? null : $theSigned[$idx];
                    //  - these fields don't change, once they have a value
                    //  - the value will never be initiated by a request
                    $tempRegArray["sign_code"] = empty($tempRegArray["sign_code"]) ? uniqid("", true) : $tempRegArray["sign_code"];
                    $syncArray[] = $tempRegArray;
                }

                // if the enddate is filled: delete any appointments after that date
                if (!empty($theEnddates[$idx])) {
                    Log::info("enddate ($idx): " . $theEnddates[$idx] . " reg: " . $theRegIds[$idx]);
                    // look for future appointments for this registration
                    $nrOfEventsDeleted = Event::deleteEventsAfter($theEnddates[$idx], $theRegIds[$idx]);
                    Log::info("Found $nrOfEventsDeleted events after the registration enddate");
                    // todo: add to save message

                }
            }
            Log::info("Found " . count($syncArray) . " courses to save");

        } else {
            Log::info("No courses found");
        }

        // save the new course registrations (delete is handled by Vue code)
        // this being too complex to use the standard sync function,
        // We'll write our own save / sync function
        // We'll write our own save / sync function
        // but make sure the keep the current PK!
        foreach ($syncArray as $registration) {
            if (!empty($registration["id"])) {
                Log::info('registration updated');
                // original PK is saved
                $r = with(new Registration)->newInstance($registration, true);
                $r->save();
            } else {
                // new id (pk) is created
                $r = new Registration();
                $r->fill($registration);
                $r->save();
                Log::info('new registration created');
                // see if there are checklists to link
                $this->initiateChecklists($r->id);
                // this maybe a registration for a triallesson request:
                // --> is student id present in trialstudents?
                // --> has this trialstudent no registration yet?
                // --> is the course_id empty? ('other course' choosen)
                // then: register this registration with this trialstudent,
                // to make sure we know we can now schedule this registration
                $ts = Trialstudent::where([
                    "generated_student_id" => $studentgroup->id,
                ])
                    ->whereNull("generated_registration_id")
                    ->whereNull("course_id")
                    ->first(); // use first(). get() will return an array
                if (!empty($ts)) {
                    Log::info("add registration for trial lesson student $r->id");
                    $ts->generated_registration_id = $r->id;
                    $ts->save();
                } else {
                    Log::info("no trial lesson student");
                }
            }
        }

        // back to the edit screen
        return redirect()->route('studentgroups.edit', $id)->with('message', ucfirst(trans("generic.datasaved")));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        Log::info("request delete of studentgroup $id");
        $stg = Studentgroup::findOrFail($id);
        // check if there are related students (== no delete)
        $students = $stg->students;
        if (count($students) !== 0) {
            // cant delete, students present
            Log::error("Students associated with this group, can't delete");
            return response()->json(["error" => "cant delete studentgroup if students are still member"], 400);
        }
        Log::info("no students associated with this group");
        // first remove registrations: delete from course_student where student_id = $id
        Registration::destroyForStudent($id);

        // delete the studentgroup
        try {
            $stg->delete();
        } catch (\Exception $e) {
            // cant delete, students present
            return response()->json(["error" => "cant delete studentgroup. " . $e->getMessage()], 500);
        }
        Log::info("Studentgroup $id deleted");
        return response()->json(["result"  => "student group deleted"], 200);
    }

    /**
     * create a new studentgroup by means of an API call (esp. from the courses edit page)
     * @param Request $request
     */
    public function newstudentgroup(Request $request)
    {
        Log::info("request: create new studentgroup");
        $students = $request->students; // array
        $courseid = $request->courseid;
        $groupname = $request->groupname;
        // validate
        // no students, no problem
        // no courseid or groupname, no show
        if (!((empty($courseid)) || (empty($groupname)))) :
            $stg = new Studentgroup();
            $name = '-';
            $name .= ' ' . $request->lastname;

            $stg->name = $name;
            $stg->domain_id = Auth::user()->domain->id;
            $stg->firstname = "-";
            $stg->lastname = $groupname;
            $stg->date_of_birth = "1800-01-01";
            $stg->status = 'ACTIVE'; // this is probably no longer needed. leave for now for backwards compat

            $stg->save();

            // connect the course (registration)
            Log::info("register course $courseid with studentgroup $stg->id");
            $stg->courses()->attach($courseid);

            // if we have students, enlist them in this group
            if (count($students) > 0) :
                $stg->students()->attach($students);
            endif;
        else :
            Log::error("cant create studentgroup, course or groupname missing");
        endif;
    }

    /**
     * @param Request $request
     * @param $courseId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPossibleStudentgroupForCourse(Request $request, $courseId)
    {
        Log::info("Getting potential student groups for course id: $courseId");
        $stgroups = [];
        $course = Course::findOrFail($courseId);
        if ($course->is_trial_course) {
            $courseIds = TrialcourseRelation::where('trialcourse_id', '=', $course->id)
                ->pluck("course_id")
                ->all();
            // do we have student groups for these courses?
            foreach ($courseIds as $courseId) {
                $course = Course::findOrFail($courseId);
                $stgroups = array_merge($stgroups,$course->studentgroups->toArray());
            }
        } else {
            $stgroups = $course->studentgroups->toArray();
        }
        Log::info("Found " . count($stgroups) . " student group(s)");
        return response()->json($stgroups);
    }
}
