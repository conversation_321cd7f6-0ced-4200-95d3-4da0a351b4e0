<?php

namespace App\Http\Controllers;

use App\Http\Requests\EventsByRegAndSchoolYear;
use App\Models\Event;
use App\Models\Registration;
use App\Models\Schoolyear;
use App\Models\Task;
use App\Http\Requests\DeleteAppointmentsRequest;
use App\Http\Requests\EventFormRequest;
use App\Http\Resources\DayEventResource;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\Ndat2Mydat;
use Scolavisa\scolib\LocaleDate;

class EventsController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index() {
        //
    }

    /**
     * Get all events of a given type
     * @param string $futureEvents
     * @param int $type [0: all, 1: triallessons]
     * @param int $addEvent id of event that should always be returned
     * @return JsonResponse
     */
    public function apiIndexByType($futureEvents = 'false', $type = 1, $addEvent = 0) {
        if ($futureEvents == 'true') {
            $events = Event::where('datetime', '>=', date('Y-m-d'))->with('tutor')->get();
        } else {
            $events = Event::select('events.*')
                ->leftJoin("tasks", 'tasks.event_id', '=', 'events.id')
                ->leftJoin("courses", 'tasks.course_id', '=', 'courses.id')
                ->where([
                    ['courses.is_trial_course', '=', '1'],
                    ['tasks.tasktype_id', '<>', '1']        // actionaftertriallesson
                ])
                ->orWhere('events.id', '=', $addEvent)
                ->with('tutor')
                ->get();
        }

        return response()->json($events);
    }

    /**
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function eventsOfToday()
    {
        Log::info("Getting events of today");
        $events = Event::select("events.id as eventId", "datetime", "events.tutor_id",
            "events.timespan", "users.name", "courses.name as courseName",
            DB::raw("concat(students.firstname, ' ', substr(students.lastname, 1,1), '.') as studentName"))
            ->whereDate('datetime', Carbon::today())
            ->orderBy('datetime')
            ->leftJoin('users', 'events.tutor_id', '=', 'users.id')
            ->leftJoin('timetables', 'events.timetable_id', '=', 'timetables.id')
            ->leftJoin('course_student', 'timetables.course_student_id', '=', 'course_student.id')
            ->leftJoin('courses', 'course_student.course_id', '=', 'courses.id')
            ->leftJoin('students', 'course_student.student_id', '=', 'students.id')
            ->get();
        return DayEventResource::collection($events);
    }



    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {
        //
    }

    /**
     * Store a newly created resource in storage.
     * todo: check if an event is already present before creating a new one
     *
     * @param EventFormRequest|Request $request
     * @return \Illuminate\Http\Response
     * @throws Exception
     */
    public function store(EventFormRequest $request) {
        Log::info("creating " . count($request->get('dates')) . " course appointment(s).");

        $messageArr = [];

        // give all events in this set the same caluniqueid
        $caluniqueid = uniqid(null, true);

        if (count($request->get('dates')) > 0) {
            $theDates = $request->get('dates');
            $theTimes = $request->get('times');
            foreach ($theDates as $idx => $theDate) {
                $event = new Event();
                $event->caluniqueid = $caluniqueid;
                $event->timetable_id = $request->timetable_id;
                $event->location_id = $request->location_id;
                $event->tutor_id = $request->tutor_id;
                $event->timespan = $request->timespan;
                $event->datetime = $theDate . " " . $theTimes[$idx];
                $event->original_datetime = $event->datetime;
                $event->sequence = 0; // initial value
                $event->save();

                $theCourse = $event->timetable->registration->course;

                // check if we need to create a task for action after trial lesson or single lesson
                if ($theCourse->is_trial_course) {
                    Log::info('This is a trial course event');
                    if (!empty($request->createActionAfterTrialLessonTask)) {
                        Log::info('Creating action after trial lesson task');
                        $task = new Task();
                        $task->domain_id = Auth::user()->domain->id;
                        $task->tasktype_id = config('app.TASKTYPE_ACTION_AFTER_TRIAL_LESSON');
                        $task->date_opened = Ndat2Mydat::getMydat($theDate);
                        $task->course_id = $event->course->id;
                        $task->student_id = $event->student->id;
                        $task->tutor_id = $request->tutor_id;
                        $task->registration_id = $request->registration_id;
                        $task->event_id = $event->id;
                        $task->remarks = LocaleDate::getLocaleSpecificDate(date('Y-m-d'), App::getLocale()) . ": " . ucfirst(trans('generic.addedtaskfor') . " " . trans('generic.actionnaftertriallesson'));
                        $task->save();
                        $messageArr[] = ucfirst(trans('generic.addedtaskfor') . " " . trans('generic.actionnaftertriallesson'));
                    }
                } else if ($theCourse->recurrenceoption->ends_after_nr_of_occurrences === 1 && !$theCourse->is_trial_course) {
                    Log::info('This is a single lesson event, creating action after single lesson task');
                    $task = new Task();
                    $task->domain_id = Auth::user()->domain->id;
                    $task->tasktype_id = config('app.TASKTYPE_ACTION_AFTER_SINGLE_LESSON');
                    $task->date_opened = Ndat2Mydat::getMydat($theDate);
                    $task->course_id = $event->course->id;
                    $task->student_id = $event->student->id;
                    $task->tutor_id = $request->tutor_id;
                    $task->registration_id = $request->registration_id;
                    $task->event_id = $event->id;
                    $task->remarks = LocaleDate::getLocaleSpecificDate(date('Y-m-d'), App::getLocale()) . ": " . ucfirst(trans('generic.invoiceforsinglelesson'));
                    $task->save();
                    $messageArr[] = ucfirst(trans('generic.addedtaskfor') . " " . trans('generic.actionaftersinglelesson'));
                }

                // set close time of course subscription if it's a trial lesson or single lesson: recurrence = 1
                // only if the user didn't already determine an end date
                if ($theCourse->recurrenceoption->ends_after_nr_of_occurrences === 1 && empty($registration->end_date)) {
                    // after planning this 1 event, we know when the subscription ends
                    $endDate = new \DateTime($theDate . " " . $theTimes[$idx]);
                    // get minutes-part of timespan (the int part)
                    $minutes = intval(preg_replace('/[^0-9]+/', '', $request->timespan), 10);
                    $endDate->modify('+' . $minutes . ' minutes');
                    $registration = Registration::findOrFail($request->registration_id);
                    Log::info("setting enddate for registration $request->registration_id to " . $endDate->format("Y-m-d"));
                    $registration->end_date = $endDate->format("Y-m-d");
                    $registration->save();
                    $messageArr[] = ucfirst(trans('generic.enddateforcoursesubscriptionset'));

                } else {
                    Log::info('This is not a single lesson event nor a trial lesson or end date already set: NOT setting enddate for coursesubscription');
                }
            }
            $messageArr[] = ucfirst(trans("generic.datasaved"));
        } else {
            Log::info("No appointments to save");
        }
        $message = serialize($messageArr);
        // done.
        return redirect()->route('edittimetable', ['registrationid' => $request->registration_id])->with('message', $message);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        $event = Event::findOrFail($id);
        $s = $event->student;
        return response()->json($event);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {
        //
    }

    /**
     * Update the specified resource in storage.
     * For now: This only fires through ajax
     * You can't change the timespan field, because it is determined by the course
     * You can't change the caluniqueid, this must always remain the same
     * not all fields have to be available in the request, only posted fields are updated
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id) {
        $event = Event::findOrFail($id);
        $event->timetable_id = isset($request->timetable_id) ? $request->timetable_id : $event->timetable_id;
        $event->location_id = isset($request->location_id) ? $request->location_id : $event->location_id;
        $event->tutor_id = isset($request->tutor_id) ? $request->tutor_id : $event->tutor_id;
        $event->datetime = (isset($request->dates[0]) && isset($request->times[0])) ? $request->dates[0] . " " . $request->times[0] : $event->datetime;
        // increment sequence on any change
        $event->sequence = ((int)$event->sequence + 1);
        $event->save();
    }

    /**
     * Api call to delete an appointment or range of appointments
     * @param DeleteAppointmentsRequest $request
     * @return JsonResponse
     */
    public function deleteAppointments(DeleteAppointmentsRequest $request) {
        $eventId = $request->eventid;
        $option = $request->deleteoption;
        $untilDate = isset($request->deleteUntilDate) ? Ndat2Mydat::getMydat($request->deleteUntilDate . " 23:59:59") : '';

        $event = Event::findOrFail($eventId);
        // check correct domain
        $tutor = $event->tutor;
        $domainId = $tutor->domain_id;
        if (Auth::user()->domain_id === $domainId) {
            Log::info("request delete event(s) with option: $option for event id: $eventId");
            switch ($option) {
                case '0':
                    // delete only this date (1)
                    DB::transaction(function() use ($eventId) {
                        $event = Event::findOrFail($eventId);
                        $event->delete();
                    });
                    break;

                case '1':
                    // delete all events that have this caluniqueid (*)
                    try {
                        DB::transaction(function() use ($event) {
                            $events = Event::where('caluniqueid', "=", $event->caluniqueid)
                                ->get();
                            foreach ($events as $event) {
                                $event->delete();
                            }
                        });
                    } catch (Exception $e) {
                        return response()->json(["result" => "fail", "message" => $e->getMessage()], 500);
                    }
                    break;

                case '2':
                    // delete all events beginning with this and and all future ones (now - *)
                    try {
                        DB::transaction(function() use ($event) {
                            $events = Event::where('caluniqueid', "=", $event->caluniqueid)
                                ->where('datetime', '>=', $event->datetime)
                                ->get();
                            foreach ($events as $event) {
                                $event->delete();
                            }
                        });
                    } catch (Exception $e) {
                        return response()->json(["result" => "fail", "message" => $e->getMessage()], 500);
                    }
                    break;
                case '3':
                    // delete all events from the DT of this event until and including $untildate (range: now - until)
                    try {
                        DB::transaction(function() use ($event, $untilDate) {
                            $events = Event::where('caluniqueid', "=", $event->caluniqueid)
                                ->where('datetime', '>=', $event->datetime)
                                ->where('datetime', '<=', $untilDate)
                                ->get();
                            foreach ($events as $event) {
                                $event->delete();
                            }
                        });
                    } catch (Exception $e) {
                        return response()->json(["result" => "fail", "message" => $e->getMessage()], 500);
                    }
                    break;
            }
            return response()->json(["result" => "success"], 200);
        } else {
            // wrong domain logged in
            return response()->json(["result" => "fail"], 403);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {
        // remove associated tasks before deleting events
        Task::where('event_id', '=', $id)->delete();
        // now remove the event
        Event::destroy($id);
    }

    public function getEventShort($eventid) {

        $event = Event::findOrFail($eventid);
        $s = $event->student;
        $returnArr = [
            "id" => $event->id,
            "caluniqueid" => $event->caluniqueid,
            "datetime" => $event->datetime,
            "student" => ["id" => $event->student->id, "name" => $event->student->name],
            "course" => ["id" => $event->course->id, "name" => $event->course->name]
        ];
        return response()->json($returnArr);

    }

    /**
     * Get events for a specific registration and school year.
     *
     * @param EventsByRegAndSchoolYear $request The request object containing the registration ID and school year ID.
     * @return JsonResponse A JSON response containing the events.
     * @throws Exception If the registration or timetable cannot be found.
     */
    public function getEventsForRegAndSchoolYear(EventsByRegAndSchoolYear $request): JsonResponse
    {
        Log::info("Getting events for registration $request->registrationId and school year $request->schoolYearId");
        // reuse this request to only get the summary
        $onlySummary = $request->input('only-summary', false);
        $schoolYear = Schoolyear::findOrFail($request->schoolYearId);
        $registration = Registration::findOrFail($request->registrationId);
        // if no timetable exists, create one for every school year
        $registration->checkTimetables();

        $timetable = $registration?->timetables()->where('schoolyear_id', $schoolYear->id)->first();
        $eventsN = Event::getCalendarEvents($schoolYear->start_date, $schoolYear->end_date, $timetable->id);
        Log::info("Found " . count($eventsN) .
            " events for registration $request->registrationId ".
            " and school year $request->schoolYearId (timetable: $timetable->id)");
        foreach ($eventsN as $key => $eventAsArray) {
            $event = Event::find($eventAsArray["id"]);
            // check if the events are solitary (Event::isSolitary) and set the attribute
            $eventsN[$key]["isPartOfSeries"] = !(Event::isSolitary($event->caluniqueid, $event->sequence));
            // now check if the event has flags (potential conflicts)
            $eventsN[$key]["flags"] = $event->getFlags();
            $eventsN[$key]["timetable_id"] = $timetable->id;
        }
        // summary: [eventsTotal, eventsBlocked, eventsPast, eventsFuture]
        if ($onlySummary) {
            $eventsN = [
                "events" => [],
                "summary" => Event::getSummary($eventsN)
            ];
        } else {
            $eventsN = [
                "events" => $eventsN,
                "summary" => Event::getSummary($eventsN)
            ];
        }
        return response()->json($eventsN ?? []);
    }

    /**
     * Delete all future events for a specific registration and school year.
     *
     * @param EventsByRegAndSchoolYear $request The request object containing the registration ID and school year ID.
     * @return JsonResponse A JSON response containing the remaining events.
     * @throws Exception If the registration or timetable cannot be found.
     */
    public function deleteFutureEventsForRegAndSchoolYear(EventsByRegAndSchoolYear $request)
    {
        Log::info("Deleting future events for registration $request->registrationId and school year $request->schoolYearId");
        $registration = Registration::findOrFail($request->registrationId);
        $timetable = $registration?->timetables()->where('schoolyear_id', $request->schoolYearId)->first();
        $timetable?->events()->where('datetime', '>=', date('Y-m-d'))->delete();
        // retrieve all events remaining
        $events = $timetable?->events()->with(['tutor', 'location'])->get();
        return response()->json($events ?? []);
    }

    public function deleteEventById($eventId = 0)
    {
        Log::info("Deleting tutoring event with id $eventId");
        // start transaction
        DB::beginTransaction();
        // first remove associated tasks before deleting events
        Task::where('event_id', '=', $eventId)->delete();
        $event = Event::findOrFail($eventId);
        $event->delete();
        // commit transaction
        DB::commit();
        return response()->json(["result" => "success"], 204);
    }
}
