<?php

namespace App\Http\Controllers;

use App\Models\DateException;
use App\Models\Event;
use App\Models\Location;
use App\Models\Planninggroup;
use App\Models\RecurrenceOption;
use App\Models\Registration;
use App\Models\Schedulepreference;
use App\Models\Scheduleproposal;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Timetable;
use App\Models\Tutor;
use App\Exports\StudentPrefsExport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Requests\MultiEventICSRequest;
use App\Http\Requests\PlanninggroupRequest;
use App\Http\Requests\ScheduleproposalSaveRequest;
use App\Http\Requests\seriesEventDateTimeRequest;
use App\Http\Requests\SingleEventICSRequest;
use App\Mail\SendICSMultipleEvents;
use App\Mail\SendICSSingleEvent;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Scolavisa\scolib\Color;
use Scolavisa\scolib\Mydat2Ndat;
use Scolavisa\scolib\Ndat2Mydat;

class TimetablesController extends Controller
{

    /**
     * Show the full, or filleterd calendar
     */
    public function calendar()
    {
        $tutors = [];
        if (Auth::user()->userIsA("admin")) {
            $tutors = Tutor::getActiveTutors();
        }
        $locations = Location::all();
        return view('timetables.calendar', compact('tutors', 'locations'));
    }

    /**
     * @param int $registrationId
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function timetableedit($registrationId = 0)
    {
        Log::info("Getting data for registration $registrationId");
        $registration = Registration::findOrFail($registrationId);

        Log::info("checking if all schoolyears have a timetable for this registration");
        // if any school year doesn't have a timetable,
        // create it for every school year that doesn't have one
        $registration->checkTimetables();

        // get a set of colors to show cal items that are part of a set
        $colors = Color::getAllColorsOfSet("20contrasting");
        $colorsAsString = implode(",", $colors);

        return view('timetables/edit', compact('registration', "colorsAsString"));
    }

    /**
     * Get timetable for a registration in a given school year,
     * @param int $registrationId
     * @param int $schoolyearId
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function get($registrationId = 0, $schoolyearId = 0)
    {
        if (($registrationId > 0) && ($schoolyearId > 0)) {
            $timetable = Timetable::where([
                'schoolyear_id' => $schoolyearId,
                'course_student_id' => $registrationId
            ])->first();

            if (!empty($timetable)) {
                // get the date exceptions for this timetable (= this school year)
                $de = new DateException();
                $timetable['dateexceptions'] = $de->getFormatted($schoolyearId, true);

                // get the related data. this triggers the pivot function in the model (belongsTo)
                // this is why we don't do anything with these variables
                $e = $timetable->allEvents;
                foreach ($e as $index => $event) {
                    // check to see if it started life as a part of a series
                    // ie is the caluniqueid unique to this event
                    $timetable->allEvents[$index]["startedAsPartOfSeries"] = $event->startedAsPartOfSeries;
                }
            }

            return response()->json($timetable);
        }

        return response()->json(['error' => 'missing parameters']);
    }

    public function sendICSSingleEvent(SingleEventICSRequest $request)
    {
        $event = Event::findOrFail($request->eventid);
        $mailtext = $request->mailtext;
        $targetemail = $request->targetemail;
        // one or more email addresses, will be sent as separate emails
        $emails = explode(",", $targetemail);
        foreach ($emails as $email) {
            Log::info("Sending email to $email with ICS file attached for event " . $request->eventid);
            Mail::to($email)
                ->send(new SendICSSingleEvent($event, $email, $mailtext));
        }

        return response()->json(['success' => 'message(s) send']);
    }

    public function sendICSMultiEvent(MultiEventICSRequest $request)
    {
        Log::info("Sendrequest multiple events");

        $studentIds = $request->studentIds;
        $mailtext = $request->mailtext;
        $seriesUids = $request->seriesUids;

        // per student / 1 ICS for all series
        $icsArr = Event::getICSForMultipleSeries($seriesUids);
        $icsFile = Event::getICSForSeriesAsString($icsArr);
        if ($icsFile === "") {
            return response()->json(['error' => 'no events found to send'], 400);
        }

        // get student emails
        foreach ($studentIds as $studentId) {
            $s = Student::findOrFail($studentId);
            $targetemail = $s->planningEmail;
            $emails = explode(",", $targetemail);
            foreach ($emails as $email) {
                Log::info("Sending email to $email with ICS file attached for " . count($seriesUids) . " eventseries");
                Mail::to($email)
                    ->send(new SendICSMultipleEvents($email, $mailtext, $seriesUids, $s->id, $s->name, $icsFile));
            }
        }

        return response()->json(['success' => 'message(s) send']);
    }


    /**
     * Open the schedulingproposal view. A lot of data prefetching goes before returning the view
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function schedulingproposal()
    {
        return view("timetables.schedulingproposal");
    }


    /**
     * retrieve data to be used for schedulingproposal
     * to be used to initialize the Vue instance for this interface
     */
    public function getSchedulingProposalData()
    {
        // Section: individual registrations
        // -----------------------------

        // find students that have indicated they want to keep the current schedule
        // filter on active registration (now or future)
        $registrations_keep = Registration::where("please_keep_scheduled_time", "=", "1")
            ->where(function ($query) {
                return $query->whereNull("end_date")->orWhere("end_date", ">", DB::raw("now()"));
            })
            ->with(["student", "course"])
            ->get();

        // find out last max 5 events and check tutor and std day / time and interval
        foreach ($registrations_keep as $key => $reg) {
            if (!$reg->regIsForStudentGroup) {
                $registrations_keep[$key]["attributes"] = $reg->standardEventData;
            } else {
                // remove from return set, will be handled in the groups section
                unset($registrations_keep[$key]);
            }
        }

        // convert to array to be able to sort
        foreach ($registrations_keep as $keepreg) {
            $regs_keep[] = $keepreg;
        }

        usort($regs_keep, function ($a, $b) {
            $dayTimeA = intval($a->day . $a->time);
            $dayTimeB = intval($b->day . $b->time);
            return $dayTimeA - $dayTimeB;
        });

        // Section: group registrations
        // -----------------------------
        // zoek alle registraties van groepen die actueel zijn
        $studentGroupRegs = Registration::select(["students.lastname as groupname", "course_student.*"])
            ->leftJoin("students", "students.id", "=", "course_student.student_id")
            ->where([["firstname", "=", "-"], ["date_of_birth", "=", "1800-01-01"]])
            ->where(function ($query) {
                $query->whereNull("end_date")
                    ->orWhere("end_date", ">", DB::raw("now()"));
            })->get();

        return response()->json([
            "keep_individual" => $regs_keep,
            "studentgroups" => $studentGroupRegs
        ]);
    }

    /**
     * retrieve data to be used for schedulingproposal
     * to be used to initialize the Vue instance for this interface
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function getSchedulingProposalData_old()
    {
        $singles = [];
        // Get all registration that are active now plus in future (not started yet)
        $courseRegistrationsToPlan = Registration::getActiveNowAndFuture();
        Log::info("found " . count($courseRegistrationsToPlan) . " courseregistrations to plan");
        if (count($courseRegistrationsToPlan) === 0) {
            return response()->json([]);
        }

        // Iterate to get the events
        foreach ($courseRegistrationsToPlan as $index => $courseRegToPlan) {
            $courseRegScheduledOn = Schoolyear::getLastSchoolyearForPlanning($courseRegToPlan->id);

            if (empty($courseRegToPlan->course->is_trial_course)) {
                if (isset($courseRegScheduledOn[0]) && empty($courseRegToPlan->ignore_current_schedule)) {
                    // we have a current scheduled day/time
                    $hash = md5($courseRegScheduledOn[0]->dayname . $courseRegScheduledOn[0]->time);
                    $coursesCurrenSchedules[$hash][] = [
                        'currentDay' => trans('localisation.' . strtolower($courseRegScheduledOn[0]->dayname)),
                        'currentTime' => $courseRegScheduledOn[0]->time,
                        'regId' => $courseRegToPlan->id,
                        'planninggroup_id' => $courseRegToPlan->planninggroup_id,
                        'student_id' => $courseRegToPlan->student->id,
                        'student_name' => $courseRegToPlan->student->name,
                        'course_id' => $courseRegToPlan->course->id,
                        'course_name' => $courseRegToPlan->fullname,
                        'frequency' => $courseRegToPlan->course->recurrenceoption_id,
                        'wantstokeepdatetime' => "todo"
                    ];
                } else {
                    // new courseregistration to be scheduled
                    $coursesNoSchedules[] = [
                        'regId' => $courseRegToPlan->id,
                        'planninggroup_id' => $courseRegToPlan->planninggroup_id,
                        'student_id' => $courseRegToPlan->student_id,
                        'student_name' => $courseRegToPlan->student->name,
                        'course_id' => $courseRegToPlan->course_id,
                        'course_name' => $courseRegToPlan->fullname,
                        'frequency' => $courseRegToPlan->course->recurrenceoption_id,
                        'wantstokeepdatetime' => "todo"
                    ];
                }
            }
        }

        // Recognize groups
        /* e.g:
        individual
        ll1, ll1 ,ll4, ll7

        group
        g1 (course1): ll, ll2,ll3
        g2 (course1): ll1, ll4
        g3 (course2): ll5, ll6

        So:
        - ll4 has individual course plus is present in g2
        - ll1 has two individual courses
        - course1 has two different groups (for different students)
        */

        // now loop through the $coursesCurrenSchedules to find groups (more than 1 in the registration is a group)
        // the hash is based on the day/time in the current shedule
        foreach ($coursesCurrenSchedules as $hash => $registration) {
            if (count($registration) > 1) {
                $studentIds = [];
                // group all student ids.
                foreach ($registration as $reg) {
                    $studentIds[$reg["student_id"]] = ["name" => $reg["student_name"], "regid" => $reg["regId"]];
                }
                //use [0] as template, containing all student ids
                unset($registration[0]["student_id"]);
                unset($registration[0]["student_name"]);
                unset($registration[0]["regId"]);
                $registration[0]["studentIds"] = $studentIds;
                // this wil be considderd a group
                $coursegroupsCurrentSchedules[] = $registration[0];
                unset($coursesCurrenSchedules[$hash]);
            }
        }


        // finally, get the recurrence options
        $recOptions = RecurrenceOption::all();
        foreach ($recOptions as $recOption) {
            $recurrenceOptions["o" . $recOption->id] = $recOption;
        }

        // rebuild groups table
        // first remove all temp data from the groups table
        Planninggroup::where('manually_added', '<>', '1')->delete();
        $allRegIdsInGroups = [];
        // now add the rows as they appear in the current planning
        foreach ($coursegroupsCurrentSchedules as $coursegroup) {
            // maar het kan zijn dat de planninggroup er al is, maar manual, zodat ie dus niet verwijderd is
            // in dat geval hoef ik hier niets te doen.
            // maar het kan ook geen kwaad...dezelfde leerlingen registraties krijgen die groep toegewezen
            // maar nu is ie niet ingepland. maar als ie wel ingepland is gaat dit wel fout misschien, dan komt ie twee keer voor
            // dus per coursegroup kijken of de registraties die er in zitten als een group hebben en of die group manually_added=1 heeft
            $pg = new Planninggroup(['manually_added' => '0']);
            $pg->save();

            // add students to this group
            foreach ($coursegroup['studentIds'] as $studentId => $student) {
                $allRegIdsInGroups[] = $student["regid"];
                $registration = Registration::findOrFail($student["regid"]);
                $registration->planninggroup_id = $pg->id;
                $registration->save();
            }
        }

        // now select all groups and students, including the formerly manually added ones
        // this replaces $coursegroupsCurrentSchedules in the output, so the displayed groups are
        // all groups currently in the planninggroups table
        $planninggroups = Planninggroup::all();
        $allCourseGroups = [];
        foreach ($planninggroups as $planninggroup) {
            $allCourseGroups[] = $this->getPlanningFor($planninggroup);
        }

        // remove the regids that are in groups from individuals list
        // whatever is left are the singles. Turn into an array, otherwise it will be an object after
        // json encode. We don't need the hashes anymore
        foreach ($coursesCurrenSchedules as $coursesCurrenSchedule) {
            if (empty($coursesCurrenSchedule['planninggroup_id'])) {
                $singles[] = $coursesCurrenSchedule;
            }
        }

        // remove the regids that are in groups from individuals list
        foreach ($coursesNoSchedules as $index => $courseNoSchedules) {
            if (!empty($courseNoSchedules["planninggroup_id"])) {
                unset($coursesNoSchedules[$index]);
            }
        }

        // response
        return response()->json([
            "singleScheduledCourses" => $singles,           // scheduled for individual student
            "coursegroups" => $allCourseGroups,             // recognized as a student group or group lesson
            "newRegistrations" => $coursesNoSchedules,      // not yet scheduled
            "recurrenceOptions" => $recurrenceOptions
        ]);
    }

    /**
     * check if this planninggroup already has a planning
     * @param Planninggroup $planninggroup
     * @return array
     * @throws \Exception
     */
    private function getPlanningFor(Planninggroup $planninggroup)
    {
        $retArr = [
            'group_id' => $planninggroup->id,
            'currentDay' => '',
            'currentTime' => '',
            'course_id' => '',
            'course_name' => '',
            'frequency' => '',
            'wantstokeepdatetime' => "",
            'studentIds' => [
            ]
        ];
        $registrations = $planninggroup->registrations;
        $retArr['course_id'] = isset($registrations[0]) ? $registrations[0]->course->id : '';
        $retArr['course_name'] = isset($registrations[0]) ? $registrations[0]->fullname : '';
        $retArr['frequency'] = isset($registrations[0]) ? $registrations[0]->course->recurrenceoption_id : '';

        // see if they are planned (timetable -> events)
        foreach ($registrations as $registration) {
            $retArr["studentIds"][$registration->student->id] = [
                "name" => $registration->student->name,
                "regId" => $registration->id,
                "wantstokeepdatetime" => $registration->please_keep_scheduled_time
            ];
            $courseRegScheduledOn = Schoolyear::getLastSchoolyearForPlanning($registration->id);
            if (!empty($courseRegScheduledOn)) {
                $retArr['currentDay'] = trans('localisation.' . strtolower($courseRegScheduledOn[0]->dayname));
                $retArr['currentTime'] = $courseRegScheduledOn[0]->time;
            }
        }
        return $retArr;
    }

    /**
     * create a new planninggroup and associate it with the registration in the request
     * @param PlanninggroupRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createNewPlanGroup(PlanninggroupRequest $request)
    {
        $reg = Registration::findOrFail($request->registration_id);
        $pg = new Planninggroup(['manually_added' => $request->manuallyadded]);
        $pg->save();
        $reg->planninggroup_id = $pg->id;
        $reg->save();
        return response()->json(['result' => "success"]);
    }

    /**
     * Remove registration from group (step 1.)
     * note: if the registration was part of the group because of a current planning
     * it will reappear. therefore check if this is the case and set flag ignore_current_planning (step 2.)
     * @param PlanninggroupRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function removeStudentFromPlanGroup(PlanninggroupRequest $request)
    {
        $groupid = isset($request->groupid) ? $request->groupid : 0;
        $registrationid = isset($request->registration_id) ? $request->registration_id : 0;
        if ($groupid > 0 && $registrationid > 0) {
            Log::info("removing $registrationid from group: $groupid");
            // 1. reset group id
            $reg = Registration::findOrFail($registrationid);
            $reg->planninggroup_id = null;
            $reg->save();
            // if this was the last in this group, remove the group (independent of manually_added flag)
            $planGroup = Planninggroup::findOrFail($groupid);
            $regs = $planGroup->registrations;
            if (count($regs) === 0) {
                $planGroup->delete();
            } else {
                // if only one left in the group, this is no longer a group. but we may need the group anyway
                // add the manually flag so the group is preserved
                $planGroup->manually_added = '1';
                $planGroup->save();
            }

            // 2. check if the registration has a current schedule. The group will be back in this group automatically if he has
            //    in that case: delete the current schedule (how???? some ignore flag in the registration??)
            //    the use has already confirmed 'OK' for ignore current schedule
            //    don't forget to reset this variable if the new planning is cosolidated
            //    For now: just set the flag.
            $reg->ignore_current_schedule = '1';
            $reg->save();
        } else {
            Log::info("NOT removing $registrationid from group: $groupid");
        }
        return response()->json(['result' => "success"]);
    }


    public function addRegToPlangroup(PlanninggroupRequest $request)
    {
        $registrationid = isset($request->registration_id) ? $request->registration_id : 0;
        $plangroupid = isset($request->plangroupid) ? $request->plangroupid : 0;
        if ($registrationid > 0 && $plangroupid > 0) {
            Log::info("Adding $registrationid to group: $plangroupid");
            $reg = Registration::findOrFail($registrationid);
            $reg->planninggroup_id = $plangroupid;
            $reg->save();
        } else {
            Log::info("NOT adding $registrationid to group: $plangroupid");
        }
    }

    /**
     * if the tutor has no appointments on a day,
     * find out when the first upcoming event is scheduled
     * @param $userid
     * @return \Illuminate\Http\JsonResponse
     */
    public function firstupcomingappointment($userid)
    {
        // get the first upcoming appointment for the teacher role of this user
        $event = Event::where('tutor_id', '=', $userid)
            ->where("datetime", ">=", DB::raw("NOW()"))
            ->orderBy("datetime")->first();
        if (empty($event)) {
            return response()->json([
                "datetime" => "",
                "dayname" => "",
                "mydat" => ""
            ]);
        } else {
            $dt = Mydat2Ndat::getNdat($event->datetime);
            $dayname = date('l', strtotime($event->datetime));
            return response()->json([
                "datetime" => $dt,
                "dayname" => trans('localisation.' . strtolower($dayname) . '-short'),
                "mydat" => substr($event->datetime, 0, 10) // dont need the time
            ]);
        }
    }

    /**
     * update all events in a series that still have the original datetime
     * also update the original datetime, this keeps the events in a series on top of the caluniqueID
     * @param seriesEventDateTimeRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function saveSeriesNewDatetime(seriesEventDateTimeRequest $request)
    {
        $dayNumbers = [
            'monday' => 0,
            'tuesday' => 1,
            'wednesday' => 2,
            'thursday' => 3,
            'friday' => 4,
            'saturday' => 5,
            'sunday' => 6
        ];
        $newDay = Ndat2Mydat::getMydat($request->newDay);
        $newTime = Ndat2Mydat::getMydat($request->newTime);
        Log::info("moving series of events $request->seriesUID to new datetime $newDay -- $newTime");

        // only change events in the series that have orginal_datetime == current datetime
        $events = Event::where('caluniqueid', '=', $request->seriesUID)
            ->where('datetime', '=', DB::raw('`original_datetime`'))
            ->get()->toArray();

        $dt = $events[0]["datetime"];
        $DTorg = new \DateTime($dt);
        $dayOrg = strtolower($DTorg->format('l'));
        $timeOrg = $DTorg->format('H:i');

        // if the day has changed, update all events to have that day
        // if the time has changed, update all events to have that time
        foreach ($events as $event) {
            $seq = $event["sequence"];
            // get the week of this event
            $thisDatetime = new \DateTime($event["datetime"]);
            $weekNumber = $thisDatetime->format('W');
            $year = $thisDatetime->format('Y');
            Log::debug("Weeknumber: $weekNumber");
            // determine the new DT in that week
            $time = new \DateTime();
            $time->setISODate($year, $weekNumber); // is monday
            $time->add(new \DateInterval('P' . $dayNumbers[$newDay] . 'D'));

            Log::debug(
                "current DT: $dt which is $dayOrg at $timeOrg will become new DT: (dispacement: " . 'P' .
                $dayNumbers[$newDay] . 'D' . ") " . $time->format('Y-m-d') . ' ' . $newTime
            );

            // update datetime, original_datetime and sequence number ('original' because we are updating the whole series)
            $newEvent = Event::findOrFail($event["id"]);
            $newEvent->datetime = $time->format('Y-m-d') . ' ' . $newTime;
            $newEvent->original_datetime = $time->format('Y-m-d') . ' ' . $newTime;
            $newEvent->sequence = ($seq + 1);
            $newEvent->save();
        }

        return response()->json(['result' => trans('generic.successfullysavednewdatetime')], 200);
    }

    /**
     * set a registration on a Date Time in the proposals table
     */
    public function setproposalforregistration(ScheduleproposalSaveRequest $request)
    {
        $s = new ScheduleProposal;
        $s->domain_id = Auth::user()->domain_id;
        $s->registration_id = $request->registration_id;
        $s->location_id = $request->location_id;
        $s->tutor_id = $request->tutor_id;
        $s->schedule_dt = $request->schedule_dt;
        $s->save();
    }

    /**
     * update a registration to a new Date Time in the proposals table
     */
    public function updateproposalforregistration(ScheduleproposalSaveRequest $request, $sid)
    {
        $s = ScheduleProposal::findOrFail($sid);
        $s->registration_id = $request->registration_id;
        $s->location_id = $request->location_id;
        $s->tutor_id = $request->tutor_id;
        $s->schedule_dt = $request->schedule_dt;
        $s->save();
    }

    public function getproposalsforregistrations()
    {
        $proposals = Scheduleproposal::orderBy("schedule_dt")
            ->with(["tutor", "location", "registration"])
            ->get();

        // laravel 5.4 doesn't know hasOneThrough. We'll have to loop through it
        foreach ($proposals as $key => $proposal) {
            $reg = $proposal->registration;
            $proposals[$key]["student"] = $reg->student;
            $proposals[$key]["course"] = $reg->course;
        }

        return response()->json($proposals);
    }

    /**
     * Shows info about student preferred schedule times
     * Input when planning manually
     */
    public function exportstudentprefs()
    {
        return view('schedulepreferences.exportstudentprefs');
    }

    /**
     * Exports students preferences to excel format
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportPrefs(Request $request)
    {
        $sortOrder = $request['sortOrder'] ?: 'lastName';
        $se = new StudentPrefsExport($sortOrder);
        return Excel::download($se, 'studentprefs.xlsx');
    }

    /**
     * Get all filled out prefs
     * @param bool $onlyActive -- currently ignored
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllSchedulePreferences($onlyActive = true)
    {
        // we are only interested in students with an active course subscription
        $activeStudents = Student::getCurrentlyActiveStudents();
        $activeStudentIds = [];
        $tmpArr = $retArr = $modDateArr = [];
        $daynames = [
            '1' => 'monday',
            '2' => 'tuesday',
            '3' => 'wednesday',
            '4' => 'thursday',
            '5' => 'friday',
            '6' => 'saturday',
            '7' => 'sunday'
        ];


        foreach ($activeStudents as $activeStudent) {
            array_push($activeStudentIds, $activeStudent->id);
        }
        $prefs = Schedulepreference::select(
            "students.id as student_id",
            "schedulepreferences.id as pref_if",
            "students.name",
            "schedulepreferences.*"
        )
            ->leftJoin("students", "student_id", "=", 'students.id')
            ->where("students.domain_id", "=", Auth::user()->domain_id)
            ->whereIn("students.id", $activeStudentIds)
            ->orderBy("students.name")
            ->get();

        // assemble data to be send to client
        foreach ($prefs as $pref) {
            if (isset($pref->from_time)) {
                if ($pref->is_prefered) {
                    $tmpArr[$pref->name][$daynames[$pref->day_number]][] = "<span class='label label-primary'>" . $pref->from_time . '-' . $pref->to_time . "</span>";
                } else {
                    $tmpArr[$pref->name][$daynames[$pref->day_number]][] = "<span class='label label-success'>" . $pref->from_time . '-' . $pref->to_time . "</span>";
                }
            } elseif ($pref->block_day == '1') {
                $tmpArr[$pref->name][$daynames[$pref->day_number]][] = "<span class='label label-warning'>" . trans(
                        'generic.blocked'
                    ) . "</span>";
            }
            // all the same, i only need one
            $modDateArr[$pref->name]["most_recent_date"] = Mydat2Ndat::getNdat(substr($pref->updated_at, 0, 10));
        }
        // reformat the array so the vue table can read it directly
        // should be [name=>studentname, monday=>'from-to;from-to', tuesday=> etc...]
        foreach ($tmpArr as $studentname => $items) {
            foreach ($items as $dayname => $segmentsArr) {
                $dayArr[$dayname] = implode("<br/>", $segmentsArr);
            }
            // selected field creates a default for rendering in Vue (component:preferedscheduletabel). It's really a placeholder
            // last_modified_date field shows the last time the info was updated by the student
            $retArr[] = array_merge(
                [
                    "name" => $studentname,
                    "selected" => "0",
                    "last_modified_date" => $modDateArr[$studentname]["most_recent_date"]
                ],
                $dayArr
            );
        }

        return response()->json($retArr);
    }

    /**
     * @param int $schoolyear
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function report($schoolyear = 0)
    {
        if ($schoolyear === 0) {
            try {
                $schoolyear = Schoolyear::getCurrentOrFuture();
            } catch (\Exception $e) {
                Log::error("error requesting current schoolyear");
                Log::error($e->getMessage());
            }
        } else {
            // check if requested school year exists
            $schoolyear = Schoolyear::findOrFail($schoolyear);
        }
        $timetables = $schoolyear->timetables;
        return view("timetables.report", compact('timetables'));
    }

    /**
     * API version of the report method
     * @param int $schoolyear
     * @return \Illuminate\Http\JsonResponse
     */
    public function reportApi($schoolyear = 0)
    {
        if ($schoolyear === 0) {
            try {
                $schoolyear = Schoolyear::getCurrentOrFuture();
            } catch (\Exception $e) {
                Log::error("error requesting current schoolyear");
                Log::error($e->getMessage());
                return response()->json(['error' => 'Could not determine current schoolyear'], 500);
            }
        } else {
            // check if requested school year exists
            try {
                $schoolyear = Schoolyear::findOrFail($schoolyear);
            } catch (\Exception $e) {
                return response()->json(['error' => 'Schoolyear not found'], 404);
            }
        }

        $timetables = $schoolyear->timetables;

        // Transform the data for the API response
        $individualTimetables = [];
        $groupTimetables = [];

        foreach ($timetables as $timetable) {
            $timetableData = [
                'id' => $timetable->id,
                'registration_id' => $timetable->registration->id,
                'student_id' => $timetable->registration->student->id,
                'student_name' => $timetable->registration->student->name,
                'student_last_name' => $timetable->registration->student->lastname,
                'course_start' => $timetable->registration->start_date,
                'course_end' => $timetable->registration->end_date,
                'course_id' => $timetable->registration->course->id,
                'course_name' => $timetable->registration->course->name,
                'recurrence' => $timetable->registration->course->recurrenceoption->description,
                'event_count' => count($timetable->events)
            ];

            // ignore if the course has ended: has an end_date that is in the past
            if ($timetable->registration->end_date !== null && $timetable->registration->end_date < date('Y-m-d')) {
                continue;
            }

            if ((!$timetable->registration->student->isAStudentgroup) &&
                (!$timetable->registration->course->is_trial_course)) {
                $individualTimetables[] = $timetableData;
            } elseif ($timetable->registration->student->isAStudentgroup) {
                $groupTimetables[] = $timetableData;
            }
        }

        // Sort arrays by student name
        usort($individualTimetables, function ($a, $b) {
            return strcasecmp($a['student_name'], $b['student_name']);
        });

        usort($groupTimetables, function ($a, $b) {
            return strcasecmp($a['student_name'], $b['student_name']);
        });

        return response()->json([
            'schoolyear' => [
                'id' => $schoolyear->id,
                'name' => $schoolyear->name
            ],
            'individual_timetables' => $individualTimetables,
            'group_timetables' => $groupTimetables
        ]);
    }

}

