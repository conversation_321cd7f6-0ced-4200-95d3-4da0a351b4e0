<?php

namespace App\Http\Controllers;

use App\Http\Resources\TutorResource;
use App\Mail\ResetPassword;
use App\Models\Event;
use App\Models\RoleUser;
use App\Models\Tutor;
use App\Models\User;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\TutorRequest;
use App\Http\Requests\TutorUpdateRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Scolavisa\scolib\Ndat2Mydat;

define("_TUTORROLEID", 2);

class TutorsController extends Controller
{

    /**
     * Get all tutors, active and inactive
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getAll()
    {
        // the Model Tutor filters out only tutor roles
        $t = Tutor::orderBy('name')->get();
        return TutorResource::collection($t);
    }

    public function getTutor(Request $request, int $id)
    {
        $t = Tutor::findOrFail($id);
        return new TutorResource($t);
    }

    /**
     * Get all active tutors
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getAllActive()
    {
        // the Model Tutor filters out only tutor roles
        $t = Tutor::orderBy('name')
            ->where(function ($query) {
                return $query->whereNull("role_user.end_date")
                    ->orWhere("role_user.end_date", ">", DB::raw("now()"));
            })
            ->get();
        return TutorResource::collection($t);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\Factory
     */
    public function index()
    {
        return view('tutors.list');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (Auth::user()->domain->trialLimitReachedTutor) {
            Log::warning(
                "domain " . Auth::user()->domain->name . " has reached the account limit for tutors in a trial account."
            );
            Log::warning("not showing entryform for new tutor");
            return redirect()->route('home')->with(
                'message',
                "error: " . ucfirst(trans("generic.limitoftrialaccountreachedtutor"))
            );
        }
        $userObj = new User();
        $activeTutors = $userObj->getActiveTutors();
        $inactiveTutors = $userObj->getInactiveTutors();
        return view('tutors.create', compact('activeTutors', 'inactiveTutors'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param TutorRequest $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function store(TutorRequest $request)
    {
        Log::info("saving new Tutor");

        if (Auth::user()->domain->trialLimitReachedTutor) {
            Log::warning(
                "domain " . Auth::user()->domain->name . " has reached the account limit for tutors in a trial account."
            );
            Log::warning("not saving new tutor");
            return redirect()
                ->route('home')
                ->with('message', ucfirst(trans("generic.limitoftrialaccountreachedtutor")));
        }

        $tutor = new User();
        $tutor->domain_id = Auth::user()->domain->id;
        $tutor->name = $request->name;
        $tutor->email = $request->email;
        $tutor->hexcolor = $request->hexcolor;
        $tutor->telephone = $request->telephone;
        $tutor->telephone_extra = $request->telephone_extra;
        $tutor->password = ""; // only admin can create new password to give access to ClassE. Separate action.
        $tutor->address_street_1 = $request->address_street_1;
        $tutor->address_street_2 = $request->address_street_2;
        $tutor->address_zipcode = $request->address_zipcode;
        $tutor->address_city = $request->address_city;
        $tutor->address_country = $request->address_country;
        $tutor->save();
        // this user is a tutor: add role
        $role = new RoleUser();
        $role->user_id = $tutor->id;
        $role->role_id = config('app.TUTORROLEID');
        $role->start_date = Ndat2Mydat::getMydat($request->start_date);;
        if (!empty($request->end_date)) {
            $role->end_date = Ndat2Mydat::getMydat($request->end_date);
        }
        $role->save();
        // redirect to edit, catch form refresh causing accidental save
        return response()->json(["message" => "data saved successfully", "newid" => $tutor->id]);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        Log::info("Editing tutor $id data");
        $userObj = new User();
        $activeTutors = $userObj->getActiveTutors();
        $inactiveTutors = $userObj->getInactiveTutors();
        $tutor = User::findOrFail($id);
        $dates = $tutor->roledates(config('app.TUTORROLEID'));
        $start_date = $dates["start_date"];
        $end_date = $dates["end_date"];
        // filling enddate is only possible if the tutor has no future events!
        $hasFutureEvents = $tutor->hasFutureTutorEvents();

        return view(
            'tutors.edit',
            compact(
                'tutor',
                'activeTutors',
                'inactiveTutors',
                'start_date',
                'end_date',
                'hasFutureEvents'
            )
        );
    }

    /**
     * Update the specified resource in storage.
     *
     * @param TutorUpdateRequest $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(TutorUpdateRequest $request, $id)
    {
        Log::info("Updating tutor $id data");
        $isBlocked = $request->is_blocked === 'on' || $request->is_blocked === '1' || $request->is_blocked === 1 || $request->is_blocked === true;
        $tutor = User::findOrFail($id);
        $tutor->name = $request->name;
        $tutor->email = $request->email;
        $tutor->hexcolor = $request->hexcolor;
        $tutor->telephone = $request->telephone;
        $tutor->telephone_extra = $request->telephone_extra;
        $tutor->is_blocked = $isBlocked ? 1 : 0; // cast to integer
        $tutor->address_street_1 = $request->address_street_1;
        $tutor->address_street_2 = $request->address_street_2;
        $tutor->address_zipcode = $request->address_zipcode;
        $tutor->address_city = $request->address_city;
        $tutor->address_country = $request->address_country;
        $tutor->save();

        // if we have a start date / end date save this in the pivot
        // start date is mandatory
        $endDate = empty($request->end_date) ? null : $request->end_date;
        $this->setRoleDates($id, config('app.TUTORROLEID'), $request->start_date, $endDate);
        return response()->json(["message" => "data saved successfully"]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws \Exception
     */
    public function destroy($id)
    {
        // cascade role!
        User::where("id", $id)->delete();
    }

    /**
     * Get active tutors for api requests
     * @return \Illuminate\Http\JsonResponse
     */
    public function get()
    {
        $allTutors = User::getActiveTutors();
        return response()->json($allTutors);
    }

    /**
     * get schema's for lessons of all tutors
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function getTutorLessons(Request $request)
    {
        $retArr = [];
        // for now it's always 'minutes' in timespan....
        $events = Event::select(
            'events.id',
            'datetime',
            'timespan',
            'tutor_id',
            DB::raw("DATE_ADD(datetime, INTERVAL CAST(timespan as UNSIGNED) MINUTE) as until"),
            'c.name as coursename',
            's.name as studentname',
            'tt.id as timetableid',
            'cs.id as registrationid'
        )
            ->leftJoin("timetables as tt", "tt.id", "=", "events.timetable_id")
            ->leftJoin("course_student as cs", "cs.id", "=", "tt.course_student_id")
            ->leftJoin("students as s", "s.id", "=", "cs.student_id")
            ->leftJoin("courses as c", "c.id", "=", "cs.course_id")
            ->where([
                ['datetime', '>=', $request->earliestDate],
                ['datetime', "<=", $request->latestDate]
            ])
            // important: keep sort order
            ->orderBy('datetime')
            ->get();

        // prepare events to be send back to client
        // group by tutor for easy access by client
        foreach ($events as $event) {
            $retArr[$event->tutor_id][] = [
                'eventid' => $event->id,
                'timetableid' => $event->timetableid,
                'registrationid' => $event->registrationid,
                'from' => $event->datetime,
                'until' => $event->until,
                'timespan' => $event->timespan,
                'coursename' => $event->coursename,
                'studentname' => $event->studentname
            ];
        }

        return response()->json($retArr);
    }

    /**
     * @param ResetPasswordRequest $request
     * @throws \Exception
     */
    public function resetpw(ResetPasswordRequest $request)
    {
        Log::info("Reset PW for tutor $request->tutorid");
        $tutor = User::findOrFail($request->tutorid);
        if (isValidEmail($tutor->email)) {
            if (($tutor->userIsA('tutor')) && ($tutor->domain_id === Auth::user()->domain_id)) {
                $newPassword = uniqid();
                $tutor->password = Hash::make($newPassword);
                $tutor->save();
                // send email with new password to tutor
                Log::info("Success. Sending new PW to tutors e-mail address");
                Mail::to($tutor->email)
                    ->queue(
                        new ResetPassword(
                            $newPassword,
                            Auth::user()->domain
                        )
                    );
            } else {
                // error. must be a tutor!
                throw new \Exception("User must be a tutor!");
            }
        } else {
            throw new \Exception("Tutor does not have a valid e-mail address!");
        }
    }

    /**
     * Get availability for 1 tutor,
     * or all tutors if tutorId == 0
     * @param int $tutorId
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function getAvailabilityOfTutor($tutorId = 0)
    {
        if ($tutorId > 0) {
            $tutorAvailablities = DB::table('availabilities')
                ->where('tutor_id', '=', $tutorId)
                ->orderBy('day_number')->orderBy('from_time')
                ->get();
            return response()->json($tutorAvailablities);
        } else {
            return response()->json(['message' => 'requesting multiple tutors not implemented yet']);
        }
    }

    /**
     * This function is used to add tutor rights to an admin account
     * It can only be executed by the logged in admin
     * It always returns 200:success
     */
    public function addtutorrights()
    {
        if (Auth::user()->userIsA('admin') && !Auth::user()->userIsA('tutor')) {
            // this user is a tutor
            $role = new RoleUser();
            $role->user_id = Auth::user()->id;
            $role->role_id = config('app.TUTORROLEID');
            $role->save();
        }
    }

    /**
     * update pivot fields start- and end_date
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    private function setRoleDates($userid, $role, $start_date, $end_date)
    {
        if (empty($end_date)) {
            $data = [
                "start_date" => Ndat2Mydat::getMydat($start_date)
            ];
        } else {
            $data = [
                "start_date" => Ndat2Mydat::getMydat($start_date),
                "end_date" => Ndat2Mydat::getMydat($end_date)
            ];
        }

        RoleUser::where([
            ['user_id', '=', $userid],
            ["role_id", '=', $role]
        ])->update($data);
    }

}
