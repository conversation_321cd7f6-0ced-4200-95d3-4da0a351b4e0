<?php

namespace App\Http\Controllers;

use App\Models\Schoolyear;
use App\Http\Requests\SchoolyearFormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Scolavisa\scolib\Ndat2Mydat;

class SchoolyearController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index() {
        return view('schoolyears.list');
    }

    public function get() {
        $allSchoolyears = Schoolyear::query()
            ->where("end_date", '>', Carbon::now())
            ->orderBy('end_date')
            ->withCount('timetables')
            ->get();
        return response()->json($allSchoolyears);
    }

    public function apiGet($id)
    {
        $schoolyear = Schoolyear::query()
            ->where("id", $id)
            ->withCount('timetables')
            ->first();
        return response()->json($schoolyear);
    }



    /**
     * Updates an existing school year
     *
     * @param SchoolyearFormRequest $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiUpdate(SchoolyearFormRequest $request, $id)
    {
        Log::info("Updating schoolyear with id: " . $id);

        $startDate = Ndat2Mydat::getMydat($request->start_date);
        $endDate = Ndat2Mydat::getMydat($request->end_date);
        $domainId = Auth::user()->domain->id;

        // Check for overlap with existing school years
        if (Schoolyear::hasOverlapWithExistingSchoolyears($startDate, $endDate, $domainId, $id)) {
            return response()->json(['message' => trans('generic.errorOverlapExcistingSchoolYear')], 403);
        }

        // check if end_date is not before start_date
        if ($endDate < $startDate) {
            return response()->json(['message' => trans('generic.endisbeforestart')], 403);
        }
        if ($endDate < Carbon::now()) {
            return response()->json(['message' => trans('generic.endisnotinfuture')], 403);
        }

        // Check domain authorization
        $schoolyear = Schoolyear::findOrFail($id);
        if ($schoolyear->domain_id != $domainId) {
            Log::error("Unauthorized attempt to update schoolyear with id: " . $id);
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $starYear = substr($startDate, 0, 4);
        $endYear = substr($endDate, 0, 4);

        $schoolyear->label = $request->label;
        $schoolyear->start_date = $startDate;
        $schoolyear->end_date = $endDate;
        $schoolyear->start_year = $starYear;
        $schoolyear->end_year = $endYear;
        $schoolyear->save();

        return response()->json($schoolyear);
    }

    public function apiStore(SchoolyearFormRequest $request)
    {
        Log::info("Creating new schoolyear");

        $startDate = Ndat2Mydat::getMydat($request->start_date);
        $endDate = Ndat2Mydat::getMydat($request->end_date);
        $domainId = Auth::user()->domain->id;

        // Check for overlap with existing school years
        if (Schoolyear::hasOverlapWithExistingSchoolyears($startDate, $endDate, $domainId)) {
            return response()->json(['message' => trans('generic.errorOverlapExcistingSchoolYear')], 403);
        }
        // check if end_date is not before start_date
        if ($endDate < $startDate) {
            return response()->json(['message' => trans('generic.endisbeforestart')], 403);
        }
        if ($endDate < Carbon::now()) {
            return response()->json(['message' => trans('generic.endisnotinfuture')], 403);
        }

        $starYear = substr($startDate, 0, 4);
        $endYear = substr($endDate, 0, 4);

        $schoolyear = new Schoolyear();
        $schoolyear->domain_id  = $domainId;
        $schoolyear->label      = $request->label;
        $schoolyear->start_date = $startDate;
        $schoolyear->end_date   = $endDate;
        $schoolyear->start_year = $starYear;
        $schoolyear->end_year   = $endYear;
        $schoolyear->save();

        return response()->json($schoolyear);
    }

    public function apiDestroy($id)
    {
        Log::info("Deleting schoolyear with id: " . $id);
        $domainId = Auth::user()->domain->id;
        $schoolyear = Schoolyear::findOrFail($id);
        if ($schoolyear->domain_id != $domainId) {
            Log::error("Unauthorized attempt to delete schoolyear with id: " . $id);
            return response()->json(['message' => 'Unauthorized'], 403);
        }
        $schoolyear->delete();
        return response()->json(['success' => 'Schoolyear deleted successfully']);
    }
}

