<?php

namespace App\Http\Controllers;

use App\Http\Requests\TagRequest;
use App\Models\Document;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Exception;

class DocumentsController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index() {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {
        $document = new Document();
        $document->domain_id        = Auth::user()->domain->id;
        $document->type             = $request->type;
        $document->content_type     = $request->content_type;
        $document->label            = $request->label;
        $document->file_location    = $request->file_location;
        $document->url              = $request->url;
    }

    /**
     * Store a new URL document
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiStoreLink(Request $request)
    {
        $request->validate([
            'label' => 'required|string|max:50',
            'url' => 'required|url|max:255'
        ]);

        $document = new Document();
        $document->domain_id = Auth::user()->domain->id;
        $document->type = 'url';
        $document->label = $request->label;
        $document->url = $request->url;
        $document->save();

        return response()->json([
            'message' => 'Link created successfully',
            'document' => $document
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id) {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {
        //
    }

    /**
     * Get all tags for the current domain
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTags()
    {
        $tags = Tag::all();
        return response()->json($tags);
    }

    /**
     * Add a tag to a document
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $documentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function addTag(Request $request, $documentId)
    {
        $request->validate([
            'tag_name' => 'required|string|max:100'
        ]);

        $document = Document::findOrFail($documentId);

        // Find or create the tag
        $tag = Tag::firstOrCreate([
            'name' => $request->tag_name,
            'domain_id' => Auth::user()->domain->id
        ]);

        // Attach the tag to the document if not already attached
        if (!$document->tags()->where('tag_id', $tag->id)->exists()) {
            $document->tags()->attach($tag->id);
        }

        return response()->json([
            'message' => 'Tag added successfully',
            'tag' => $tag
        ]);
    }

    /**
     * Remove a tag from a document
     *
     * @param  int $documentId
     * @param  int $tagId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeTag($documentId, $tagId)
    {
        $document = Document::findOrFail($documentId);
        $document->tags()->detach($tagId);

        return response()->json([
            'message' => 'Tag removed successfully'
        ]);
    }

    /**
     * Get tags for a specific document
     *
     * @param  int $documentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDocumentTags($documentId)
    {
        $document = Document::with('tags')->findOrFail($documentId);
        return response()->json($document->tags);
    }

    /**
     * Update a URL document
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiUpdateLink(Request $request, $id)
    {
        $request->validate([
            'label' => 'required|string|max:50',
            'url' => 'required|url|max:255',
            'description' => 'nullable|string|max:1000'
        ]);
        Log::info('Updating link with ID: ' . $id);
        $document = Document::where('domain_id', Auth::user()->domain->id)
                           ->where('type', 'url')
                           ->findOrFail($id);

        $document->label = $request->label;
        $document->url = $request->url;
        $document->description = $request->description;
        $document->save();

        return response()->json([
            'message' => 'Link updated successfully',
            'document' => $document
        ]);
    }

    /**
     * Store a new tag
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiStoreTag(TagRequest $request)
    {
        Log::info('Creating tag: ' . $request->name . ' for domain: ' . Auth::user()->domain->id);
        try {
            $tag = Tag::create([
                'domain_id' => Auth::user()->domain->id,
                'name' => $request->name,
            ]);
            return response()->json([
                'id' => $tag->id,
                'name' => $tag->name,
                'label' => $tag->name
            ]);
        } catch (Exception $e) {
            Log::error('Failed to create tag: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to create tag'
            ], 500);
        }
    }
}
