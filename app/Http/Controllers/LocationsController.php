<?php

namespace App\Http\Controllers;

use App\Http\Requests\LocationFormRequest;
use App\Http\Resources\LocationResource;
use App\Models\Location;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LocationsController extends Controller
{
    public function getAll()
    {
        $locations = Location::orderBy('name')
            ->withCount('events')
            ->get()
            ->each(function ($location) {
                $location->in_use = $location->events_count > 0;
                $location->icon = "location_" . $location->domain_id . '_' . $location->id;
            });
        return LocationResource::collection($locations);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        return view('locations.list');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('locations.create');
    }


    public function store(LocationFormRequest $request)
    {
        Log::info("Saving new location");
        $courseLocation = new Location();
        $courseLocation->domain_id = Auth::user()->domain->id;
        $courseLocation->name = $request->name;
        $courseLocation->save();
        return response()->json($courseLocation);
    }

       /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function edit($id)
    {
        $allLocations = Location::get();
        $courseLocation = Location::findOrFail($id);
        // check if we have an icon, if not, get one
        $domain_id = Auth::user()->domain->id;
        if (!file_exists(base_path() . "/storage/app/public/location_icon_$domain_id" . "_" . "$id.svg")) {
            // Remote image URL
            $url = 'https://avatars.dicebear.com/v2/jdenticon/' .
                str_replace(" ", "", $courseLocation->name) . '.svg';
            // Image path
            $img = base_path() . "/storage/app/public/location_icon_$domain_id" . "_" . "$id.svg";
            // Save image
            file_put_contents($img, file_get_contents($url));
        }

        return view('locations.edit', compact('courseLocation', 'allLocations'));
    }

    public function update(LocationFormRequest $request, $id)
    {
        Log::info("Updating location with id $id");
        $courseLocation = Location::findOrFail($id);
        $courseLocation->name = $request->name;
        $courseLocation->save();
        return response()->json($courseLocation);
    }

    /**
     * returns the location occupation for future events
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFutureOccupied()
    {
        $occ = Location::getFutureOccupied();
        return response()->json($occ);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws \Exception
     */
    public function destroy($id)
    {
        Log::info("Deleting location with id $id");
        Location::where("id", $id)->delete();
    }

    public function get()
    {
        $allLocations = Location::get();
        return response()->json($allLocations);
    }
}
