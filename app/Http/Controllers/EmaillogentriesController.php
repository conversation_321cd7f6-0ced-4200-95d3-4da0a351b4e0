<?php

namespace App\Http\Controllers;

use App\Models\Emaillogentry;
use Illuminate\Http\Request;

class EmaillogentriesController extends Controller {

    /**
     * Show a complete list, data will be provided through API call
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index() {
        return view('email.listlogentries');
    }

    /**
     * Returns all messages as json with pagination
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiindex(Request $request) {
        $query = Emaillogentry::orderby("created_at", "desc");

        // Check if we should show all entries or just recent ones
        if (!$request->has('all') || $request->get('all') !== 'true') {
            // Default behavior: only entries from the last week
            $query->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-7 day')));
        }

        // Get page size from request, default to 25, min 10 max 100
        $perPage = $request->get('per_page', 25);
        // Ensure between 10 and 100
        $perPage = min(max((int)$perPage, 10), 100);

        $emailEntries = $query->paginate($perPage);

        return response()->json($emailEntries);
    }

    /**
     * Delete an entry from the log
     */
    public function destroy($id)
    {
        $emailEntry = Emaillogentry::find($id);
        if ($emailEntry) {
            $emailEntry->delete();
        } else {
            return response()->json(['success' => false], 404);
        }
        return response()->json(['success' => true]);
    }

}
