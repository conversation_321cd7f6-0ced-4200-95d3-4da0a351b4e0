<?php

namespace App\Providers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use App\Models\User;
use Illuminate\Support\Facades\Gate;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Forces the root url to be what's in the .env file,
        // some routes seem to know better otherwise.
        // This is especially necessary for Gitpod
        URL::forceRootUrl(Config::get('app.url'));
        if (str_contains(Config::get('app.url'), 'https://')) {
            URL::forceScheme('https');
        }
        Gate::define('viewPulse', function (User $user) {
            return $user->userIsA("scolavisa");
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // bugsnag integration
        // see https://docs.bugsnag.com/platforms/php/laravel/
        // If you'd like to keep logging to your original logger as well as Bugsnag, you can bind 'bugsnag.multi' rather than 'bugsnag.logger'.
        // no bugsnag in development
        if ($this->app->environment() !== 'development') {
            $this->app->alias('bugsnag.multi', \Illuminate\Contracts\Logging\Log::class);
            $this->app->alias('bugsnag.multi', \Psr\Log\LoggerInterface::class);
        }

        // Voeg deze regel toe voor enum support
        // No longer needed in Laravel 11
        // https://laravel.com/docs/11.x/upgrade#doctrine-dbal-removal
//        if (class_exists('Doctrine\DBAL\Types\Type') && !\Doctrine\DBAL\Types\Type::hasType('enum')) {
//            \Doctrine\DBAL\Types\Type::addType('enum', \Doctrine\DBAL\Types\StringType::class);
//        }
    }
}
