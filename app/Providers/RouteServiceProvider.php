<?php

namespace App\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //
        $this->configureRateLimiting();
        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();

        //
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware('web')
             ->namespace($this->namespace)
             ->group(base_path('routes/web.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
             ->middleware('api')
             ->namespace($this->namespace)
             ->group(base_path('routes/api.php'));
    }
    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting()
    {
        if (app()->environment('testing')) {
            // Disable rate limiting for testing
            RateLimiter::for('api', function (Request $request) {
                return Limit::none();
            });
            RateLimiter::for('web', function (Request $request) {
                return Limit::none();
            });
            RateLimiter::for('login', function (Request $request) {
                return Limit::none();
            });
         } else {
             RateLimiter::for('api', function (Request $request) {
                 return Limit::perMinute(300)->by(optional($request->user())->id ?: $request->ip());
             });
             RateLimiter::for('web', function (Request $request) {
                 return Limit::perMinute(60)->by(optional($request->user())->id ?: $request->ip());
             });
             RateLimiter::for('login', function (Request $request) {
                 return Limit::perMinute(20)->by(optional($request->user())->id ?: $request->ip());
             });
         }
    }
}
