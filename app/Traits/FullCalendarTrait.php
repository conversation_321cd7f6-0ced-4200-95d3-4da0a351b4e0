<?php

namespace App\Traits;

use App\Models\Event;
use App\Models\Student;
use App\Models\Studentgroup;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait FullCalendarTrait
{
    /**
     * Get all calendar events that start between the requested start- and enddates
     * Optimized for speed, which is why there are many joins instead of using eloquent relation definitions.
     * The process timing is there to keep an eye on performance (getrusage functions);
     *
     * @param String $startDate
     * @param String $endDate
     * @param int $timetableId optional only get events for a specific timetable
     * @return array
     * @throws \Exception
     */
    public static function getCalendarEvents($startDate, $endDate, $timetableId = 0)
    {
        $rustart = getrusage();
        $retArray = [];
        $where = [
            ["s.domain_id", "=", Auth::user()->domain->id],
        ];

//DB::enableQueryLog();
        // get all events (whole school)
        $eventsQuery = Event::query()
            ->from("events as ev")
            ->select(
                "ev.id as eventid", "ev.datetime", "ev.timespan", "ev.caluniqueid", "ev.sequence", "ev.remarks",
                "s.id as studentid", "s.name as studentname", "s.firstname", "s.lastname", "s.date_of_birth",
                "c.id as courseid", "c.name as coursename", "c.is_trial_course",
                "t.id as tutorid", "t.name as tutorname", "t.hexcolor",
                "l.id as locationid", "l.name as locationname",
                "rco.id as recurrenceoptionid", "rco.description as recurrenceoptiondescription"
            )
            ->leftJoin("timetables as tt", "tt.id", "=", "ev.timetable_id")
            ->leftJoin("course_student as cs", "cs.id", "=", "tt.course_student_id")
            ->leftJoin("students as s", "s.id", "=", "cs.student_id")
            ->leftJoin("courses as c", "c.id", "=", "cs.course_id")
            ->leftJoin("recurrenceoptions as rco", "c.recurrenceoption_id", "=", "rco.id")
            ->leftJoin("users as t", "ev.tutor_id", "=", "t.id")
            ->leftJoin("locations as l", "ev.location_id", "=", "l.id")
            ->where($where);
            // check the query in dateexception, orWhereRaw parts. why are they absent here?
        if ($timetableId > 0) {
            $eventsQuery->where("ev.timetable_id", $timetableId);
        } else {
            // no need for this if we already know which timetable to use
            $eventsQuery->whereBetween("datetime", [$startDate, $endDate]);
        }
        $events = $eventsQuery->get();

//$log = DB::getQueryLog();
//Log::debug(print_r($log, true));
//DB::disableQueryLog();

        // format the events to be used by fullcalendar
        foreach ($events as $event) {
            $relationEvent = Event::findOrFail($event->eventid);
            $attendanceNotes = $relationEvent->attendanceNotes;
            // An event is considered solitary if the sequence number combined with the caluniqueid is unique in the database.
            $evIsSolitary = Event::isSolitary($event->caluniqueid, $event->sequence);

            $timespan = $event->timespan;
            // be sure this is English spelling, so fullCalendar will be able to display correctly
            $timespan = str_replace("minuten", "minutes", $timespan);
            $endDT = new \DateTime($event->datetime);
            $endDT = $endDT->add(\DateInterval::createFromDateString($timespan))->format("Y-m-d H:i");

            $isAStudentgroup = $event->firstname == '-' && $event->date_of_birth == '1800-01-01';

            if ($isAStudentgroup) {
                $studentGroup = Studentgroup::findOrFail($event->studentid);
                $studentGroup->students = Studentgroup::getStudentsInGroupAtDT($studentGroup->id, $event->datetime);
                $nrOfStudents = count($studentGroup->students);
                $students = $studentGroup->students;
                // lastname is the group name
                $studentName = $event->lastname;
                $studentLinkName = $event->lastname;

                // add relevant course registration dates
                $dtEvent = new \DateTime($event->datetime);
                foreach ($students as $key => $student) {
                    $student->regDates = [];
                    $s1 = Student::findOrFail($student->id);
                    $courseArray = array_filter($s1->courses->toArray(), function ($row) use ($studentGroup) {
                        // Fixme: this might also be a course that leads to the course in de studentgroup!
                        // Fixme, so we need OR here
                        return $row["id"] === $studentGroup->courses[0]->id;
                    });
                    // now check if the student is registered for the course at the datetime of the event
                    // for this we need to know if -one- of the student->regDates is valid for the event->datetime
                    $student->isRegistered = false;
                    foreach ($courseArray as $courseReg) {
                        // Fixme: start time is 00:00:00, end time is 23:59:59 - currently not registered in student card
                        // Fixme: we need this for two events of 1 student group on the same day
                        $startDateStudentString = strlen($courseReg["pivot"]["start_date"]) > 10
                            ? $courseReg["pivot"]["start_date"]
                            : $courseReg["pivot"]["start_date"] . " 00:00:00";
                        $endDateStudentString = empty($courseReg["pivot"]["end_date"])
                            ? ""
                            : (strlen($courseReg["pivot"]["end_date"]) > 10
                                ? $courseReg["pivot"]["end_date"]
                                : $courseReg["pivot"]["end_date"] . " 23:59:59");
                        $student->regDates[] = [
                            "reg_start_date" => $startDateStudentString,
                            "reg_end_date" => $endDateStudentString
                        ];
                        $startDTStudentReg = new \DateTime($startDateStudentString);
                        $endDTStudentReg = new \DateTime($endDateStudentString);
                        if ($dtEvent >= $startDTStudentReg && (empty($endDateStudentString) || $dtEvent <= $endDTStudentReg)) {
                            $student->isRegistered = true;
                        }
                    }
                    if (!$student->isRegistered) {
                        // remove the student from the array of students
                        unset($students[$key]);
                        $nrOfStudents--;
                    } else {
                        $student->attendance = $attendanceNotes->where("student_id", $s1->id)->first() ?: [
                            "student" => ["id" => $student->id, "name" => $student->name],
                            "event_id" => $event->eventid,
                            "attendanceoption_id" => 0
                        ];
                    }
                }
            } else {
                $nrOfStudents = 1;
                $studentName = $event->studentname;
                $studentLinkName = $event->studentname .
                    "<span data-student-id='" . $event->studentid . "' class='btn btn-outline-primary btn-sm ml-1' v-tooltip='\'studentcard\''>" .
                    "   <i data-student-id='" . $event->studentid . "' class='fa fa-id-card'></i>" .
                    "</span>";
                $studentAttendance = $attendanceNotes->where("student_id", $event->studentid)->first() ?: [
                    "student" => ["id" => $event->studentid, "name" => $event->studentname],
                    "event_id" => $event->eventid,
                    "attendanceoption_id" => 0
                ];
                $students = [[
                        "id" => $event->studentid,
                        "name" => $event->studentname,
                        "attendance" => $studentAttendance
                    ]
                ];
            }

            $courseName = ($event->is_trial_course) ? ucfirst(trans('generic.triallesson')) . ': ' . $event->coursename : $event->coursename;
            // is it an allday event?
            $allDayEvent = substr($event->datetime, 12) === '00:00';
            // get tutor's acronym
            $acronym = '';
            $parts = preg_split("/\s+/", $event->tutorname);
            foreach ($parts as $p) {
                $acronym .= mb_substr($p, 0, 1);
            }
            // color sidebar in calendar events denotes the tutor
            $tutorBar =
                "position: absolute;" .
                "float: left;" .
                "background-color:" . $event->hexcolor . ";" .
                "width: 5px;" .
                "height: 100%;";
            // if a remark present for this event, add a flag so the user's attention is drawn
            // see sc-2817
            $hasRemarks = empty($event->remarks) ? "" : "hasRemarks";
            $content = "<div class='$hasRemarks' style='$tutorBar'></div><span>($acronym)</span>";
            $retArray[] = [
                // mandatory to get it to render
                "id" => $event->eventid,
                "eventId" => $event->eventid, // fullcalendar does not allow 'id' for some reason
                "title" => "<div style='$tutorBar'></div>" . $studentLinkName,
                "start" => $event->datetime,
                "end" => $endDT,
                "flag_sticky" => $relationEvent->flag_sticky,
                "flag_publish" => $relationEvent->flag_publish,
                "backgroundColor" => $event->hexcolor,
                "textColor" => getContrastColor($event->hexcolor),
                "remarks" => $event->remarks ?: "",
                // specials if I want to use the render function
                "students" => $students,
                "studentId" => $event->studentid,       // if the event is for a student group, this is the group id, and name is the group name
                "studentName" => $studentName,          // in private tutoring, this is redundant, because of $students array
                "studentLastName" => $event->lastname,  // but probably still in use in the calendar
                "nrOfStudents" => $nrOfStudents,
                "isAStudentGroup" => $isAStudentgroup,
                "courseName" => $courseName,
                "courseId" => $event->courseid,
                "isSolitary" => $evIsSolitary,
                "allDay" => $allDayEvent,
                "eventType" => 'appointment',
                "timespan" => $timespan,
                "tutor" => $event->tutorname,
                "tutorId" => $event->tutorid,
                "tutorColor" => $event->hexcolor,
                "location" => $event->locationname,
                "locationId" => $event->locationid,
                // columns based on tutor
                "split" => $event->tutorid,
                "class" => "location" . $event->locationid,
                "content" => $content
            ];
        }
        $ru1 = getrusage();

        Log::debug("This query process used " . rutime($ru1, $rustart, "utime") . " ms for its computations");
        Log::debug("It spent " . rutime($ru1, $rustart, "stime") . " ms in system calls");

        return $retArray;
    }
}
