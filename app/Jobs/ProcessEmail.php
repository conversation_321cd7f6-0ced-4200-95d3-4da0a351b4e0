<?php

namespace App\Jobs;

use App\Mail\PreferenceInvitation;
use Illuminate\Bus\Queueable;
use Illuminate\Database\DetectsLostConnections;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;

class ProcessEmail implements ShouldQueue {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, DetectsLostConnections;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    protected $preferenceInvitation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(PreferenceInvitation $preferenceInvitation) {
        $this->preferenceInvitation = $preferenceInvitation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        try {
            $this->preferenceInvitation->build();
        } catch (\Exception $e) {
            // generic logging
            Log::error($e->getMessage());
        }
    }
}
