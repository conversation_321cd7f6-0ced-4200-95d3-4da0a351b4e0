<?php

namespace App\Services\DateExceptions;

use App\Services\DateExceptions\ConflictStrategies\ConflictStrategyInterface;
use Illuminate\Support\Facades\DB;
use App\Models\DateException;
use Illuminate\Support\Facades\Log;

class ConflictChecker {
    private ConflictStrategyInterface $strategy;
    
    public function setStrategy(ConflictStrategyInterface $strategy) {
        Log::debug('>>>>>>Setting strategy');
        $this->strategy = $strategy;
    }
    
    public function getConflictingEvents(DateException $dateException) {
        Log::debug('.... Checking for conflicts');
        $events = $this->getEventsInTimeRange($dateException);
        Log::debug('.... Found ' . $events->count() . ' events in time range');
        return $events->filter(function($event) use ($dateException) {
            return $this->strategy->hasConflict($dateException, $event);
        })
        ->map(function($event) use ($dateException) {
            $isStudentGroup = $event->date_of_birth === '1800-01-01' && $event->firstname === '-';
            return [
                'event_id' => $event->event_id,
                'event_start' => $event->start_time,
                'student_name' => $isStudentGroup ? $event->lastname : ($event->firstname . ' ' . $event->lastname),
                'student_id' => $event->student_id,
                'course_id' => $event->course_id,
                'course_name' => $event->course_name,
                'type' => $isStudentGroup ? 'student group' : 'individual',
                'flag_sticky' => (bool)$event->flag_sticky,
            ];
        });
    }
    
    private function getEventsInTimeRange(DateException $dateException) {
        return DB::table('events')
            ->join('timetables', 'events.timetable_id', '=', 'timetables.id')
            ->join('course_student', 'timetables.course_student_id', '=', 'course_student.id')
            ->join('students', 'course_student.student_id', '=', 'students.id')
            ->join('courses', 'course_student.course_id', '=', 'courses.id')
            ->select(
                'events.id as event_id',
                'events.flag_sticky',
                "events.location_id",
                "events.tutor_id",
                "events.datetime",
                'students.firstname',
                'students.lastname',
                'students.date_of_birth',
                'students.id as student_id',
                'courses.id as course_id',
                'courses.name as course_name',
                DB::raw('DATE_ADD(events.datetime, INTERVAL events.timespan MINUTE) as end_time'),
                'events.datetime as start_time'
            )
            ->where(function($query) use ($dateException) {
                $query->where(function($q) use ($dateException) {
                    // Event start valt binnen de date exception
                    $q->where('events.datetime', '>=', $dateException->datetime_start)
                        ->where('events.datetime', '<=', $dateException->datetime_end);
                })->orWhere(function($q) use ($dateException) {
                    // Event eind valt binnen de date exception
                    $q->whereRaw('DATE_ADD(events.datetime, INTERVAL events.timespan MINUTE) >= ?', [$dateException->datetime_start])
                        ->whereRaw('DATE_ADD(events.datetime, INTERVAL events.timespan MINUTE) <= ?', [$dateException->datetime_end]);
                });
            })
            ->get();
    }
}
