/node_modules
/public/storage
# Legacy build artifacts (no longer used)
# /public/mix-manifest.json
/public/images/icon_add.png
/public/images/icon_remove.png
# Vite build artifacts
/public/build/

# Old build artifacts (from before Vite migration)
/public/js/tmpl3/
/public/css/tmpl3/

/storage/*
/vendor
/.idea
/.theia
/database/ClassModel.mwb.bak
/database/app/
/database/database/
/wordpress/node_modules/
/wordpress/settings.php
.DS_Store
Homestead.json
Homestead.yaml
.env
/.phpunit.result.cache

/.phpunit.cache/test-results
/public/uploads/
/coverage/
/public/hot
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
/authState.json
/cypress/
/tests/e2e/screenshots/
/logs/laravel.log
/.vscode/settings.json
/docs/node_modules/
/docs/.vitepress/cache/


# Generated component imports
/resources/js/generated/
/.phpunit.cache/
/chrome.log
/public/manifest.json
/public/assets/
/public/js/
