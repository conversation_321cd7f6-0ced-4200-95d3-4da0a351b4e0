<?php

use Illuminate\Http\Request;
use \Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

/**
 * Routes that do not require authentication
 */
Route::middleware(['throttle:api'])->group(function () {
    // schedule prefs, student version
    Route::get('getuserprefs', 'SchedulepreferenceController@getUserPrefs');
    Route::post('updateschedulepreferences', 'SchedulepreferenceController@saveforuser');
    Route::put('keepscheduletime/{accesstoken}', 'SchedulepreferenceController@keepscheduletime');
    Route::get('getdomaininfobyaccesstoken', 'DomainsController@getdomaininfobyaccesstoken');
    // broadcast
    Route::get('locationbroadcast/{locationid?}', 'LocationBroadcastController@list')
        ->middleware('checkipaddress');
    Route::get('getdomaintoken', 'LocationBroadcastController@gettoken');
    Route::get('oh-dear-health-check', 'HealthCheckController@ohDearHealthCheck');

});

Route::middleware(['auth:api', 'throttle:api'])->group(function () {

    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    Route::get('/contacts/email/count', 'EmailController@countContacts');
    Route::post('/contacts/email/upload', 'EmailController@uploadImages');
    Route::post('/contacts/email', 'EmailController@sendMailToContacts');

    /* Alerts */
    Route::get('/alertstatus', 'AlertController@status');
    Route::post('hidealert', 'AlertController@hideMe');
    Route::put('unhidealert', 'AlertController@unhideMe');
    Route::delete('clearhidenalerts', 'AlertController@clearHidden');
    Route::get('singlealertstatus', 'AlertController@alertstatus');

    /* Courses */
    Route::get('/courses', 'CoursesController@getAll');
    Route::get('/courses/{courseId}', 'CoursesController@getAPi');
    Route::post('/courses', 'CoursesController@storeApi');
    Route::put('/courses/{courseId}', 'CoursesController@updateApi');

    Route::get('allcourses', 'CoursesController@indexApi');
    Route::get('coursetrialcourse', 'CoursesController@coursetrialcourse');
    Route::post('trialcourserelation', 'CoursesController@linkcoursetrialcourse');
    Route::delete('trialcourserelation', 'CoursesController@unlinkcoursetrialcourse');
    Route::get('coursesfortargetcourse/{courseid}', 'CoursesController@coursesfortargetcourse');

    /* trial students / requests */
    Route::post('trialstudents', 'TrialstudentsController@store');
    Route::get('trialcourses', 'TrialstudentsController@trialcourses');
    Route::get('trialcourse/{courseid}', 'TrialstudentsController@trialcourse');
    Route::get('gettrialrequests', "TrialstudentsController@apiIndex");
    Route::delete('trialstudents/{id}', 'TrialstudentsController@destroy');
    Route::get('studentswithcourse/{courseid}/{notingroup?}', 'StudentsController@studentsWithCourse');
    Route::get('coursesNotTrial', 'StudentsController@coursesNotTrial');
    Route::post('studentfromtrialstudents', 'StudentsController@createFromTrialRequest');
    Route::get('trialrequeststatuses', 'TrialstudentsController@trialrequeststatuses');
    Route::put('trialstudents/{id}', 'TrialstudentsController@update');

    /* Recurrence options */
    Route::get('recoptions', 'RecurrenceOptionsController@apiIndex');
    Route::post('recoptions', 'RecurrenceOptionsController@apiStore');
    Route::put('recoptions/{recid}', 'RecurrenceOptionsController@apiUpdate');
    Route::delete('recoptions/{recid}', 'RecurrenceOptionsController@apiDestroy');

    /* Coursegroups */
    Route::get('coursegroups', 'CoursegroupController@apiIndex');
    Route::get('coursegroups/{id}', 'CoursegroupController@getApi');
    Route::post("coursegroups", "CoursegroupController@apiStore");
    Route::put('coursegroups/{id}', 'CoursegroupController@apiUpdate');
    Route::delete('coursegroups/{id}', 'CoursegroupController@ApiDestroy');
    Route::get("gettutorsforcoursegroup/{coursegroupid}", "CoursegroupController@gettutorsforcoursegroup");
    Route::put("toggletutorforcoursegroup", "CoursegroupController@toggletutorforcoursegroup");
    Route::put("toggletutorforcourseandcheck", "CoursegroupController@toggletutorforcourseandcheck");
    Route::put("toggleagegroupfortutor", "CoursegroupController@toggleagegroupfortutor");

    /* Students */
    Route::get('students/studentsnocourses', 'ApiController@studentsnocourses');
    Route::get('students/studentsactive', 'ApiController@studentsactive');
    Route::get('students/{studentid}', "StudentsController@get");
    Route::put('addAllActiveRegistrationToStudentlist/{studentlistId}', 'ApiController@addAllActiveRegistrationToStudentlist');
    Route::put('removeAllStudentsFromList/{studentlistId}', 'ApiController@removeAllStudentsFromList');
    Route::put('removeSelectedStudentsFromList/{studentlistId}', 'ApiController@removeSelectedStudentsFromList');
    Route::put('addSelectedStudentsToList/{studentlistId}', 'ApiController@addSelectedStudentsToList');
    Route::delete('students/full/{studentid}', 'StudentsController@delFull');

    // student: contacts
    Route::get('/studentcontacts/{id}', 'StudentsController@getAllContacts');
    Route::delete('/studentcontact/{id}', 'StudentsController@deleteStudentContact');
    Route::put('/updateContactApplyFor/{id}', 'StudentsController@updateContactApplyFor');
    // student: log
    Route::get('/studentlogentries/{id}', 'StudentsController@getAllLogentries');
    Route::post('/studentlogentry/{id}', 'StudentsController@saveLogentry');
    Route::delete('/studentlogentry/{id}', 'StudentsController@deleteLogentry');
    // Student: bankdata
    Route::get('/studentbankdata/{id}', 'StudentsController@getBankData');
    Route::post('/studentbankdata/{id}', 'StudentsController@saveBankData');
    Route::put('/changemandatenumber/{id}', 'StudentsController@createNewMandateNumber');
    // Student: course registrations
    Route::get('/studentcoursedata/{id}', 'StudentsController@getCourseData');
    Route::get('/studentcourseregistration/{id}', 'StudentsController@getStudentCourseRegistration');
    Route::delete('/studentcourseregistration/{id}', 'StudentsController@deleteStudentCourseRegistration');
    Route::put('/students/addCourseRegistration', 'CreateCourseRegistration'); // Single Action Controller
    // Student: studentlists (lists you can put students on, not list of students!)
    Route::get('studentlists', 'StudentlistController@studentlists');
    Route::post('studentlists', 'StudentlistController@addStudentlist');
    Route::put('studentlists/{slid}', 'StudentlistController@updateStudentlist'); // fixme <<<< error 422 - empty fields request
    Route::delete('studentlists/{slid}', 'StudentlistController@delStudentlist');
    // Student: studentlists participation
    Route::get('/studentstudentlists/{studentid}', 'StudentlistController@getStudentListsOfStudent');
    Route::put('/studentstudentlists/{studentid}', 'StudentlistController@saveStudentListsParticipationOfStudent');
    // Student: attendance options
    Route::get('/attendanceoptions', 'AttendanceoptionsController@getAll');
    Route::get('/attendanceoptions/{id}', 'AttendanceoptionsController@get');
    Route::post('/attendanceoptions', 'AttendanceoptionsController@create');
    Route::put('/attendanceoptions/{id}', 'AttendanceoptionsController@update');
    Route::delete('/attendanceoptions/{id}', 'AttendanceoptionsController@destroy');

    // Students: Default Checklists
    Route::get('defaultchecklists', 'DefaultChecklistsController@apiIndex');
    Route::post('defaultchecklists', 'DefaultChecklistsController@apiStore');
    Route::put('defaultchecklists/{checklistid}', 'DefaultChecklistsController@apiUpdate');
    Route::delete('defaultchecklists/{checklist}', 'DefaultChecklistsController@apiDestroy');

    // Students: Access
    Route::delete('revokeAccessFromAll/{filter}', "StudentsController@revokeAccessFromAll");
    Route::post('grantAccessToAllActive/{filter}', "StudentsController@grantAccessToAllActive");
    Route::put('removeAccess/{studentId}', 'StudentsController@removeAccess');
    Route::put('addAccess/{studentId}', 'StudentsController@addAccess');

    Route::get('searchstudents', "StudentsController@apiindex");
    Route::get('getActiveStudents', 'StudentsController@apiStudentsForPlanning');
    Route::get('/students', 'StudentsController@getAll');
    Route::get('studentswithaccess', 'ApiController@studentswithaccess');
    Route::get('studentsjson', 'ApiController@studentsjson');
    Route::put('studentsavepin', 'StudentsController@savepin'); // new, C3 version
    Route::post('students/savepin', 'StudentsController@savepin_c2'); // C2 version - deprecated
    Route::post('students/newfromotherstudent', 'StudentsController@newFromOtherStudent');
    Route::get('birthdays', 'AlertController@birthdays');
    // schedule prefs, admin version
    Route::get ('admingetuserprefs',                'SchedulepreferenceController@getuserprefs');           // new, expects studentId in request
    Route::post('adminupdateschedulepreferences',   'SchedulepreferenceController@saveforuser');            // new, expects studentId in request
    Route::put ('adminkeepscheduletime/{studentid}','SchedulepreferenceController@adminkeepscheduletime');  // new

    /* Studentgroups */
    Route::get('studentgroups', 'StudentgroupsController@getAll');
    Route::get('possiblestudentsforstudentgroup/{stgid}', 'StudentgroupsController@getPossibleStudentsForStudentgroup');
    Route::get('getstudentgroupsforcourse/{courseid}', 'StudentgroupsController@getPossibleStudentgroupForCourse');
    Route::get('studentgroups/{stgid}', 'StudentgroupsController@get');
    Route::delete('studentgroups/{stgid}', 'StudentgroupsController@destroy');
    Route::post('studentgroups', 'StudentgroupsController@apiStore');
    Route::put('studentgroups/{stgid}', 'StudentgroupsController@apiUpdate');
    Route::delete('removecoursefromstudentgroup/{stgId}/{courseId}', 'StudentgroupsController@removecoursefromstudentgroup');
    Route::delete('removestudentfromstudentgroup/{stgId}/{studentId}', 'StudentgroupsController@removestudentfromstudentgroup');
    Route::get('coursesexcludeforstudentgroups', 'StudentgroupsController@getCourseIdsToExclude');
    Route::get('studentgroupsjson', 'ApiController@studentgroupsjson');
    Route::get('getstudentgroup/{stgid}', 'StudentsController@getDetailsOfStudentgroup');
    Route::get('getstudentgroupsofcourse/{courseid}', 'CoursesController@getStudentgroupsOfCourse');
    Route::get('getstudentsofcourse/{courseid}', 'CoursesController@getStudentsOfCourse');
    Route::post('delStudentFromGroup', 'StudentsController@delStudentFromStudentgroup');
    Route::post('addStudentToGroup', 'StudentsController@addStudentToStudentgroup');
    Route::post('delCourseFromGroup', 'StudentsController@delCourseFromGroup');
    Route::post('addCourseToGroup', 'StudentsController@addCourseToGroup');
    Route::post('newstudentgroup', 'StudentgroupsController@newstudentgroup');

    /* Registration */
    Route::post('registration/requestsignature', 'StudentsController@sendSignRequest');
    Route::get('activeregistration/{studentid}', 'StudentsController@activeregistration');
    Route::get('getreg/{studentid}', 'StudentsController@getreg');
    Route::get('registrationswithchecklists', 'ReportsController@registrationsOpenChecklistsData');
    Route::get('registration/{registrationId}', 'RegistrationsController@getRegistration');
    Route::get('checklistsforregistration/{registrationId}', 'RegistrationsController@checklistsForRegistration');
    Route::get('defaultchecklists', 'RegistrationsController@defaultChecklists');
    Route::put('/updatecheckboxforchecklist/{checklistid}/{regid}', 'RegistrationsController@updateChecklistItem');
    Route::delete('/deletechecklisttoregistration/{checklistid}/{regid}', 'RegistrationsController@delChecklist');
    Route::put('/addchecklisttoregistration/{defchecklistid}/{regid}', 'RegistrationsController@addChecklist');
    Route::put('/price_override', 'RegistrationsController@priceOverride');
    Route::put('/tax_rate_override', 'RegistrationsController@taxRateOverride');
    Route::put('/reset_incidental_price', 'RegistrationsController@resetIncidentalPrice');
    Route::put('/reset_incidental_tax_rate', 'RegistrationsController@resetIncidentalTaxRate');
    Route::get('/getregistrationdata', 'ReportsController@getRegistrationData');


    /* Tasks */
    Route::get('/gettasks', "TasksController@apiIndex");
    Route::get('/gettask/{id}', "TasksController@apiGet");
    Route::get('/gettaskforstudent/{studentId}', "TasksController@apiGetForStudent");
    Route::put('/task/{id}', "TasksController@apiPut");
    Route::put('/task/{id}/close', "TasksController@apiClose");
    Route::delete('/task/{id}', "TasksController@apiDelete");

    /* Calendar */
    Route::get('calendarevents', 'ApiController@calendarevents');
    Route::get('dateexceptionevents', 'ApiController@dateexceptionevents');
    Route::delete('deleteAppointments', 'EventsController@deleteAppointments');
    // CLASS 3
    Route::get('/eventsoftoday', 'EventsController@eventsOfToday');
    Route::get('/allcalendarevents', 'CalendarController@calendarevents');
    Route::put('/calendarevent/{caleventids}', 'CalendarController@updateCalendarEvent');
    Route::get('dateexceptions', 'DateExceptionsController@dateExceptions');
    Route::get('dateexceptions/{id}/lessonconflicts', 'DateExceptionsController@lessonConflicts');
    Route::get('dateexceptions/{id}', 'DateExceptionsController@show');
    Route::delete('dateexceptions/{id}', 'DateExceptionsController@destroy');
    Route::put('dateexception', 'DateExceptionsController@update');
    Route::post('dateexception', 'DateExceptionsController@insert');
    Route::get('/attendanceoptions', 'AttendanceController@getAttendanceoptions');
    Route::put('/togglestickyevent/{id}', 'CalendarController@toggleStickyEvent');
    Route::get('/dateexceptions/forschoolyear/{syid}', 'DateExceptionsController@dateExceptionsForTimetable');
    Route::get("/dateexceptionsforlist", "DateExceptionsController@dateExceptionsForList");

    /* Scheduling */
    Route::get('getinitialdata', 'TimetablesController@getSchedulingProposalData');
    Route::post('createNewPlanGroup', 'TimetablesController@createNewPlanGroup');
    Route::put('removeStudentFromPlanGroup', 'TimetablesController@removeStudentFromPlanGroup');
    Route::put('addRegToPlangroup', 'TimetablesController@addRegToPlangroup');
    Route::get('firstupcomingappointment/{userid}', 'TimetablesController@firstupcomingappointment');
    Route::get('getLocationOccupation', "LocationsController@getFutureOccupied");
    Route::get('gettimetable/{regid}/{schoolyearid}', 'TimetablesController@get');
    Route::post('sendicssingleevent', 'TimetablesController@sendICSSingleEvent');
    Route::post('sendicsmultievent', 'TimetablesController@sendICSMultiEvent');
    Route::get('timetables/report/{schoolyear?}', 'TimetablesController@reportApi');
    Route::post('saveSeriesNewDatetime', 'TimetablesController@saveSeriesNewDatetime');
    Route::get('getproposalsforregistrations', 'TimetablesController@getproposalsforregistrations');
    Route::post('setproposalforregistration', 'TimetablesController@setproposalforregistration');
    Route::put('updateproposalforregistration/{sid}', 'TimetablesController@updateproposalforregistration');
    Route::get('getAllSchedulePreferences', 'TimetablesController@getAllSchedulePreferences');
    Route::post('getTutorLessons', 'TutorsController@getTutorLessons'); // for all tutors
    Route::get('/eventsreginsy', 'EventsController@getEventsForRegAndSchoolYear');
    Route::delete('/eventsreginsy', 'EventsController@deleteFutureEventsForRegAndSchoolYear');
    Route::delete('/eventbyid/{eventId}', 'EventsController@deleteEventById');
    /* layered planning */
    Route::post('/analysePlanRequest', 'PlanningController@analysePlanRequest');
    Route::post("/savenewevents", 'PlanningController@saveNewEvents');
    Route::get("/eventflags", 'PlanningController@eventflags');
    Route::get("/planningforday", 'PlanningController@planningForDay');

    /* school years */
    Route::get('getschoolyears', 'SchoolyearController@get'); // returns only school years with end_date > now()
    Route::get('/schoolyears/{id}', 'SchoolyearController@apiGet');
    Route::put('/schoolyears/{id}', 'SchoolyearController@apiUpdate');
    Route::post('/schoolyears', 'SchoolyearController@apiStore');
    Route::delete('/schoolyears/{id}', 'SchoolyearController@apiDestroy');

    /* Logentries*/
    Route::get('logentry/{logentryId}', 'ApiController@logentry');
    Route::get('logentry/{logentryId}', 'ApiController@logentry');
    Route::get('logentries/{studentId}', 'ApiController@logentries');
    Route::post('logentry/{logentryId?}', 'ApiController@saveLogentry');
    Route::delete('logentry/{logentryId}', 'ApiController@delLogentry');

    /* Timeslice */
    Route::post('addTimeslice', 'ApiController@addTimeslice');
    Route::get('getAvailabilityOfTutor/{tutorId?}', 'TutorsController@getAvailabilityOfTutor');
    Route::put('updateTimeslice', 'ApiController@updateTimeslice');
    Route::delete('deleteTimeslice', 'ApiController@deleteTimeslice');

    /* Email */
    Route::get('templates', 'ApiController@getTemplates'); // CLASS2
    Route::get('getMailTemplates/{forTarget?}', 'EmailController@getMailTemplates'); // CLASS3
    Route::get('emailcontacts/{studentid}/{forusage?}', 'ApiController@getEmailContacts');
    Route::post('sendsingleemail', 'MailerController@sendsingleemail');
    Route::post('sendmultiemail', 'MailerController@sendmultiemail');
    Route::get('emaillogentries', 'EmaillogentriesController@apiindex');
    Route::delete('emaillogentries/{id}', 'EmaillogentriesController@destroy');
    Route::get('mailTemplateContent/{id}', 'EmailController@getTemplateContent');
    Route::put('updateMailTemplate', 'EmailController@updateMailTemplate');
    Route::post('createMailTemplate', 'EmailController@createMailTemplate');
    Route::delete('deleteMailTemplate/{templateId}', 'EmailController@deleteMailTemplate');
    Route::get('templateVariables/{registrationId?}', 'EmailController@getTemplateVariables');

    /* Tutors */
    Route::get('tutors', 'TutorsController@getAll');                // active + inactive tutors
    Route::get('tutors/{id}', 'TutorsController@getTutor');         // get 1 tutor (active or inactive)
    Route::get('activetutors', 'TutorsController@getAllActive');    // active tutors
    Route::get('gettutors', 'TutorsController@get');                // also: active tutors (CL2)
    Route::put('addtutorrights', 'TutorsController@addtutorrights');
    Route::delete('tutor/{id}', 'TutorsController@destroy');
    Route::put('tutor/{id}', 'TutorsController@update');
    Route::post('tutor', 'TutorsController@store');
    Route::post('resetpassword', 'TutorsController@resetpw');

    /* Locations */
    Route::get('locations', 'LocationsController@getAll');
    Route::get('/getlocations', 'LocationsController@get');

    /* Libraries */
    Route::get('libraries', 'LibrariesController@getAll');
    Route::delete('libraries/{libid}', 'LibrariesController@apidestroy');
    Route::put('libraries/{libid}', 'LibrariesController@apiupdate');
    Route::post('libraries', 'LibrariesController@apicreate');
    Route::put('libraries/{libid}/shares', 'LibrariesController@apiupdateShares');

    /* Documents */
    Route::get('documents/{libid}', 'LibrariesController@documentsExcludingLib');
    Route::put('attachdoctolib/{libid}/{docid}', 'LibrariesController@adddoctolib');
    Route::put('detachdocfromlib/{libid}/{docid}', 'LibrariesController@deletedocfromlib');
    Route::post('uploadfile', 'LibrariesController@fileupload');
    Route::post('storelink', 'DocumentsController@apiStoreLink');
    Route::put('updatelink/{id}', 'DocumentsController@apiUpdateLink');
    Route::get('/document-tags', 'DocumentsController@getTags');
    Route::post('/documents/{documentId}/tags', 'DocumentsController@addTag');
    Route::delete('/documents/{documentId}/tags/{tagId}', 'DocumentsController@removeTag');
    Route::get('/documents/{documentId}/tags', 'DocumentsController@getDocumentTags');
    Route::post('/tags', 'DocumentsController@apiStoreTag');

    /* Broadcasts */
    Route::get('broadcasts', 'MessageController@getBroadcastsAsJson');

    /* Messages for ClassE */
    Route::delete("adminmessages/{messageid}", "MessageController@destroy");
    Route::get("adminmessages", "MessageController@get");
    Route::post("adminmessages", "MessageController@create");

    /* Domain */
    Route::get('/getdomaininfo', 'DomainsController@getdomaininfo');
    Route::put('/updatedomaininfo', 'DomainsController@updatedomaininfo');

    /* Users */
    Route::get('/getactiveusers', 'UsersController@getActiveUsers'); // tutors + admins, at least one active role

    Route::get('/profile', 'UsersController@getProfile');  // CLASS3
    Route::put('/profile', 'UsersController@updateProfile');
    Route::post('/changepassword', 'UsersController@changePassword');
});

// /////////////////////////////////////////
// ClassE > CLASS Educators App
// /////////////////////////////////////////
Route::group(['middleware' => ['auth:sanctum', 'mustbetutor']], function () {

    // resource USER
    Route::prefix('user')->group(function () {
        Route::get('/', "ClassEController@user");
    });

    // resource CALENDER
    Route::prefix('calendar')->group(function () {
        Route::get('/myday', "ClassEController@myday");
        Route::get('/myfutureevents', "ClassEController@getCalendarEvents"); // for backward compatibility, use 'myevents' instead
        Route::get('/myevents', "ClassEController@getCalendarEvents");
        Route::get('/event/{eventId}', "ClassEController@getCalendarEvent");
    });

    // resource MESSAGES
    Route::prefix('messages')->group(function () {
        Route::get('/', "ClassEController@getMyInbox");
        Route::put('/{messageId}/setOpened', 'ClassEController@messageOpened');
        Route::get('/myinbox', "ClassEController@getMyInbox");
        Route::put('/messageopened', 'ClassEController@messageOpened');
    });

    // resource: APPOINTMENTS
    Route::prefix('appointments')->group(function () {
        Route::get('/', "ClassEController@getMyAppointments");
        Route::put('/{appointmentId}/confirm', 'ClassEController@confirmAppointment');
        Route::post('/request', 'ClassEController@requestAppointment');
    });

    // resource ATTENDANCE
    Route::prefix('attendance')->group(function () {

        Route::get('/attendancecount/{tutorId?}', 'ClassEController@attendanceCountForTutor'); // not used yet - rather use emptyAttendancenotes below
        Route::get('/getEmptyAttendances', 'ClassEController@emptyAttendanceNotes'); // not used yet

        Route::post('/setAttendance', 'ClassEController@upsertattendance');  // one or multiple event/student
        Route::get('/attendanceOptions', 'ClassEController@attendanceOptions');

        Route::get('/tutoractionsempty/{tutorId?}', 'ClassEController@getAttendanceNotesWithEmptyTutorAction');
    });

    // resource LIBRARY
    Route::prefix('library')->group(function () {
        Route::post('/saveURL', 'ClassEController@saveURLforEvent');
        Route::delete('/delURL/{id}', 'ClassEController@deleteURLfromEvent');
    });
});

// /////////////////////////////////////////
// Wordpress > plugins for website
// /////////////////////////////////////////
// API for wordpress calendar
Route::group(["middleware" => ['wordpressauth']], function () {
    Route::get('wpcal', 'CalendarController@wpcal');
    Route::get('wppricing', 'PricingController@wppricing');
});

// /////////////////////////////////////////
// API v4 for Class 4
// /////////////////////////////////////////
Route::middleware(['throttle:api','auth:api'])->prefix('v4')->group(function () {
    Route::post('verify-2fa', 'LoginsecurityController@verify2fa');
    Route::get('/user', "UsersController@userApiResponse");
});
