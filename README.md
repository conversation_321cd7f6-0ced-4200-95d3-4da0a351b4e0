# CLASS

[![Gitpod ready-to-code](https://img.shields.io/badge/Gitpod-ready--to--code-blue?logo=gitpod)](https://gitpod.io/#https://github.com/Scolavisa/class)

Lots of people have a lot of knowledge. Either professionally or as a hobby, people want to teach others their knowledge. If you are a bit successful, your student base might just grow a bit faster than you expected, and some administration might come in handy. 

That's where CLASS can help you. It helps administrate your teaching schedule, your pricelist, all the steps you need to do when enlisting a new student like adding them to your facebook group and mailing list, printing a registration form, scheduling holidays, etc.   

In CLASS you can quickly find your students' registration info 

If you have multiple courses (multiple topics, targeted audience, etc.) and maybe more than 1 teacher, you'll find having things orderly in one application invaluable. 

As most applications nowadays, CLASS is constantly being developed in close collaboration with people in the field of teaching. Having all these opinions and wishes coming to us is crucial for the development of CLASS. 


### What is this repository for?
This is the repository for CLASS development. 

### Local setup for development
CLASS is a laravel application, currently based on Laravel 8. 
We use composer and NPM for our dependencies and building setup. Make sure you use the correct versions, see below. If you run into trouble concerning the node version: please use [NVM](https://github.com/nvm-sh/nvm) to obtain a specific version of Node.js and the node package manager (npm).  

```
# composer --version
Composer version 2.4.4 2022-10-27 14:39:29
# node -v
v16.18.1
# npm -v
8.19.2
```

After cloning the repository, you need to 
* (linux only) make storage and bootstrap/cache writable (-R) `sudo chmod -R a+w bootstrap/cache` and `sudo chmod -R a+w storage/`
* run `composer install`
* run `npm install`
* run `npm run dev`
* create a .env file in the root of the project (see test environment on scolaserver for an example)
  * setup your database in /.env. You need 3 databases: class, classapi (for classy) and classportal
  * set APP_URL to your local host
  * set APP_ENV to development
  * make sure you use mailtrap as a mail catcher
  * setup your application key: `php artisan key:generate`
  * setup your oauth keys: `php artisan passport:keys`
* make sure your database is in sync with the test environment

## setup mysql
(fill in the .... with a sensible password)
```mysql
CREATE USER 'scolar'@'localhost' IDENTIFIED BY '....';
GRANT ALL PRIVILEGES ON *.* TO 'scolar'@'localhost' WITH GRANT OPTION;
CREATE USER 'scolar'@'%' IDENTIFIED BY '....';
GRANT ALL PRIVILEGES ON *.* TO 'scolar'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
```
Next create 3 databases: 
```mysql
create database class;
create database classapi;
create database classportal;
```

To start CLASS we use the Laravel development server. Go to the work directory and execute: 
```
php artisan serve
```
Class will be available at http://localhost:8000. 
To log in, you need an admin account. For security reasons, the only way to add an admin account is to create one in the database. 
You can use Laravel tinker for that. In the work directory execute: 
```
php artisan tinker

```
You are now in an interactive shell that uses the same settings that CLASS uses. Next create a user to log in: 
```
$u = new \App\Models\User();
$u->name = "My Admin";
$u->domain_id = "1";
$u->email = "<EMAIL>";
$u->password = "secret";
$u->save();
```
and give it the role admin
```
$user->roles()->attach(1);
```
You should now be able to log in, ready to set up your 2FA.

### How to run tests
We use phpunit for our tests. To run the tests, go to the work directory and execute: 
```
./vendor/bin/phpunit
```
We use Cypress for e2e tests. To run the tests, go to the work directory and execute:
```
npm run cypress:open
```
