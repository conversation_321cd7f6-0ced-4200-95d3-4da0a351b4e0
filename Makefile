all: artisan composer
	@echo "Add 'dev' or 'prod' to include npm build"

artisan:
	php artisan clear-compiled
	php artisan cache:clear
	php artisan view:clear
	php artisan config:cache
	php artisan config:clear
	php artisan route:clear
	php artisan optimize:clear

composer:
	composer dump-autoload
	composer clear-cache

resetdb:
	php artisan db:wipe
	php artisan migrate --seed

vite:
	rm -rf node_modules/.vite

dev: resetdb artisan composer vite
	php artisan serve & npm run watch & wait

prod: artisan composer
	bash -c "source ~/.nvm/nvm.sh && npm run prod"

watch:
	php artisan serve & npm run watch & wait
