#!/usr/bin/env node

/**
 * This script copies files from the build directory to the locations expected by the GitHub workflow.
 * It's executed after the build process completes.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Define source and destination directories
const sourceJsDir = path.resolve(__dirname, '../public/build/js');
const destJsDir = path.resolve(__dirname, '../public/js');
const sourceCssDir = path.resolve(__dirname, '../public/build/css');
const destCssDir = path.resolve(__dirname, '../public/css');
const sourceAssetsDir = path.resolve(__dirname, '../public/build/assets');
const destAssetsDir = path.resolve(__dirname, '../public/assets');

// Create destination directories if they don't exist
if (!fs.existsSync(destJsDir)) {
    fs.mkdirSync(destJsDir, { recursive: true });
    console.log(`Created directory: ${destJsDir}`);
}

if (!fs.existsSync(destCssDir)) {
    fs.mkdirSync(destCssDir, { recursive: true });
    console.log(`Created directory: ${destCssDir}`);
}

if (!fs.existsSync(destAssetsDir)) {
    fs.mkdirSync(destAssetsDir, { recursive: true });
    console.log(`Created directory: ${destAssetsDir}`);
}

// Copy JS files
if (fs.existsSync(sourceJsDir)) {
    copyFilesInDirectory(sourceJsDir, destJsDir);
    console.log(`Copied JS files from ${sourceJsDir} to ${destJsDir}`);
} else {
    console.log(`Source directory does not exist: ${sourceJsDir}`);
}

// Copy CSS files
if (fs.existsSync(sourceCssDir)) {
    copyFilesInDirectory(sourceCssDir, destCssDir);
    console.log(`Copied CSS files from ${sourceCssDir} to ${destCssDir}`);
} else {
    console.log(`Source directory does not exist: ${sourceCssDir}`);
}

// Copy asset files
if (fs.existsSync(sourceAssetsDir)) {
    copyFilesInDirectory(sourceAssetsDir, destAssetsDir);
    console.log(`Copied asset files from ${sourceAssetsDir} to ${destAssetsDir}`);
} else {
    console.log(`Source directory does not exist: ${sourceAssetsDir}`);
}

/**
 * Recursively copy all files from source directory to destination directory
 */
function copyFilesInDirectory(sourceDir, destDir) {
    const files = fs.readdirSync(sourceDir);
    
    for (const file of files) {
        const sourcePath = path.join(sourceDir, file);
        const destPath = path.join(destDir, file);
        
        const stats = fs.statSync(sourcePath);
        
        if (stats.isDirectory()) {
            // Create the destination directory if it doesn't exist
            if (!fs.existsSync(destPath)) {
                fs.mkdirSync(destPath, { recursive: true });
            }
            
            // Recursively copy files in subdirectory
            copyFilesInDirectory(sourcePath, destPath);
        } else {
            // Copy the file
            fs.copyFileSync(sourcePath, destPath);
        }
    }
}

console.log('Build files copied successfully!');
