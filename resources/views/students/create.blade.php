@extends('layouts.tmpl3.app-bs5')

@section('content')
<form class="form" action="{{route('students.store')}}" method="post" accept-charset="UTF-8">
    @csrf

    {{-- Generic data of the student --}}
    <student-generic></student-generic>

    {{-- Floating bar for buttons that will always be in the view --}}
    <floating-bar>
        <a href='/home' class="btn btn-secondary">{{ucfirst(trans('generic.dashboard'))}}</a>
        <a href='/students' class="btn btn-secondary">{{ucfirst(trans('generic.list'))}}</a>
        @if (Auth::user()->userIsA('admin'))
            <button class="btn btn-primary" onclick="window.dirty=false">
                <i class="fa fa-save"></i>
                {{ucfirst(trans('generic.save'))}}
            </button>
        @endif
    </floating-bar>
</form>

@endsection
