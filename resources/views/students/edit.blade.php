@extends('layouts.tmpl3.app-bs5')

@section('content')
    {{-- Some browsers need this to be post.
    {{-- The hidden fields makes sure laravel treats it as PUT --}}
    <form class="form" action="{{route('students.update', ['student' => $student])}}" method="post">
        <input name="_method" type="hidden" value="PUT">
        @csrf

        {{-- Generic data of the student --}}
        <student-generic
                :student-id="{!! $student->id !!}"
                firstname="{!! $student->firstname !!}"
                preposition="{!! $student->preposition !!}"
                lastname="{!! $student->lastname !!}"
                address="{!! $student->address !!}"
                zipcode="{!! $student->zipcode !!}"
                city="{!! $student->city !!}"
                date-of-birth="{!! $student->date_of_birth !!}"
                access-token="{!! $student->accesstoken !!}"
                has-access="{!! $student->has_access !!}"
                :agree-social-share="{!! $student->agreeSocialShare !!}"
        ></student-generic>

        {{-- other sections are Vue components --}}
        <student-contact student-id="{{ $student->id }}"></student-contact>

        <bank-data studentid="{{ $student->id }}"></bank-data>

        <student-log :studentid="{{ $student->id }}"></student-log>

        {{-- userisadmin: convert php bool to js bool--}}
        <student-course-data
                :student="{{ $student }}"
                :userisadmin="{{Auth::user()->userIsA('admin') ? 'true' : 'false'}}"
                :domain="{{Auth::user()->domain}}"
        >
        </student-course-data>


        <div class="row">
            <div class="col-md-6">
                <student-lists studentid="{{ $student->id }}"></student-lists>
            </div>
            <div class="col-md-6">
                <student-tasks student-id="{{ $student->id }}"></student-tasks>
            </div>
        </div>


        {{-- Floating bar for buttons that will always be in the view --}}
        <floating-bar>
            <a href='{{url("/home")}}' class="btn btn-secondary">
                <i class="fa fa-columns"></i>
                {{ucfirst(trans('generic.dashboard'))}}
            </a>
            {{-- no list page yet, hide for now --}}
            <a href='{{url("/students")}}' class="btn btn-secondary hide">
                <i class="fa fa-list"></i>
                {{ucfirst(trans('generic.list'))}}
            </a>
            @if (Auth::user()->userIsA('admin'))
                {{-- if student is deletable - got to profile page --}}
                <button class="btn btn-success" onclick="window.dirty=false">
                    <i class="fa fa-save"></i>
                    {{ucfirst(trans('generic.save'))}}
                </button>
            @endif
        </floating-bar>
    </form>

@endsection
