{{-- ----------------------------- --}}
{{-- page to ask student to sign   --}}
{{-- the registration - preferably --}}
{{-- before courses start          --}}
{{-- ----------------------------- --}}
{{-- this page is NOT behind UN/PW --}}
{{-- ----------------------------- --}}

@extends('layouts.tmpl3.app-bs5')

@section('extra_style')
    <style>
        .clickable {
            cursor: pointer;
        }

        .hidden {
            display: none !important;
        }

        .panel {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 10px 0 10px;
            margin-bottom: 20px;
            background-color: lightgray;
        }

        .panel .panel-heading {
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .panel .panel-footer {
            background-color: #f5f5f5;
            border-top: 1px solid #ddd;
            margin-top: 10px;
            padding: 10px;
        }
    </style>
@endsection

@section('content')
    <div class="container">
        <div class="content">

        <h3 class="mt-2">{{ucfirst(trans('generic.confirmregistration'))}}</h3>
        <div class="row">
            <div class="col-md-12">
                <p>{!! trans('generic.explainsignrequestpage') !!}</p>
            </div>
        </div>

        <hr/>
        @include('registrations.registration_specs')

        <form action="/registration/savesigningofregistration/{{$registration->id}}/{{$registration->sign_code}}"
              method="post">
            <input type="hidden" name="regid" value="{{$registration->id}}">
            <input type="hidden" name="_token" value="{{ csrf_token() }}">
            <div class="panel">
                <div class="row panel-heading">
                    <div class="col-12">
                        <i class="fa fa-check "></i>
                        {{ucfirst(trans('generic.statementsofagreement'))}}
                    </div>
                </div>
                @if(strlen($termsUrl))
                    <div class="row">
                        <div class="col-12">
                            <em>
                                {{ucfirst(trans('generic.termsandconditionsapply', ['customerName' => $schoolName]))}}
                            </em>
                        </div>
                    </div>
                @endif
                <hr>
                <div class="row">
                    <div class="col-1">
                    <input
                        type="checkbox"
                        id="iagree" name="iagree"
                        autocomplete="off"
                    >
                    </div>
                    <div class="col-11">
                        <label for="iagree" class="clickable">
                            {{ucfirst(trans('generic.iagree'))}}
                        </label>
                    </div>
                </div>

                @if(strlen($privacyUrl))
                    <div class="row">
                        <div class="col-1">
                            <input
                                type="checkbox"
                                id="iagreesocial" name="iagreesocial"
                                autocomplete="off"
                            >
                        </div>
                        <div class="col-11">
                            <label for="iagreesocial" class="clickable">
                                {{ucfirst(trans('generic.confirmshare', ['school' => $schoolName]))}}
                            </label>
                        </div>
                    </div>
                @endif

                <div class="row panel-footer hidden" id="saveBtn">
                    <button type="submit" class="btn btn-success btn-lg">{{ucfirst(trans('generic.send'))}}</button>
                </div>
            </div>
        </form>
    </div>
    </div>

    <script>
        (function () {
            document
                .querySelector("#iagree")
                .addEventListener("click", function (event) {
                    // find out if the student ticked the agree box
                    var iagreeChecked = document.getElementById("iagree").checked,
                        saveBtn = document.getElementById("saveBtn");
                    if (iagreeChecked) {
                        saveBtn.classList.remove('hidden');
                    } else {
                        saveBtn.classList.add('hidden');
                    }
                });
        })();
    </script>
@endsection

