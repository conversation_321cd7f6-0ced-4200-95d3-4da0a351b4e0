<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle"
       href="#" id="navbarSettings" role="button" data-bs-toggle="dropdown"
       aria-haspopup="true" aria-expanded="false"
    >
        <i class="fas fa-cog hide-llg-and-down"></i>
        {{ ucfirst(trans('generic.settings')) }}
    </a>
    <div class="dropdown-menu" aria-labelledby="navbarSettings">
        <a class="dropdown-item" href="{{ url('/recurrenceoptions') }}">{{ ucfirst(trans_choice('generic.recurrenceoptions',2)) }}</a>
        <a class="dropdown-item" href="{{ url('/defaultchecklists') }}">{{ ucfirst(trans_choice('generic.checklists',2)) }}</a>
        <a class="dropdown-item" href="{{ url('/studentlists') }}">{{ ucfirst(trans_choice('generic.studentlists',2)) }}</a>
        <a class="dropdown-item" href="{{ url('/schoolyears') }}">{{ ucfirst(trans_choice('generic.schoolyears',2)) }}</a>
        <a class="dropdown-item" href="{{ url('/mailtemplates') }}">{{ ucfirst(trans('generic.mailtemplates')) }}</a>
        {{-- removed, see issue sc-4405 --}}
        {{--        <a class="dropdown-item" href="{{ url('/attendanceoptions') }}">{{ ucfirst(trans_choice('generic.attendanceoptions', 2)) }}</a>--}}
        <a class="dropdown-item" href="{{ url('/domainsettings') }}">{{ ucfirst(trans('generic.domainsettings')) }}</a>
    </div>
</li>
