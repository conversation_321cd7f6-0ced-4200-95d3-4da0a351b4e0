@extends('layouts.tmpl3.app-bs5')

@section('content')
    <h3>{{ ucfirst(trans_choice('generic.studentgroups',1)) }}</h3>
    <div class="row">
        <div class="col-12 col-lg-6 d-flex">
            <student-group-generic :id="{{ isset($studentgroup) ? $studentgroup->id : 0 }}" class="flex-fill"></student-group-generic>
        </div>
        @if(isset($studentgroup))
            <div class="col-12 col-lg-6 d-flex">
                <student-group-course-data :id="{{ $studentgroup->id }}" class="flex-fill"></student-group-course-data>
            </div>
        @endif
    </div>
    @if(isset($studentgroup))
        <div class="row">
            <div class="col-12 col-lg-6 d-flex">
                <student-group-students class="flex-fill"></student-group-students>
            </div>
            <div class="col-12 col-lg-6 d-flex">
                <student-log :studentid="{{ $studentgroup->id }}" class="flex-fill"></student-log>
            </div>
        </div>
    @endif
@endsection
