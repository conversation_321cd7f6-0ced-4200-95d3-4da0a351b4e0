@extends("layouts.tmpl3.app-bs5")

@section("content")
    <div class="container" id="setupwizard">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-1">{{ ucfirst(trans('generic.setupwizard')) }}</h5>
                <small class="text-muted ms-2">{{ trans('generic.explainsetuppage') }}</small>
            </div>
            <div class="card-body">
                <h5 class="mb-3">{{ ucfirst(trans('generic.missingdata')) }}:</h5>
                <div class="row">
                    <div class="col-md-12">
                        <ul class="list-group list-group-flush">
                            @foreach($incompleteSetupOptions as $key => $option)
                                <li class="list-group-item px-0">
                                    <div class="mb-2">{{ucfirst($option["message"])}}</div>
                                    <div class="d-flex flex-wrap align-items-center gap-2">
                                        <a href="{{$option["link"]}}?setupfix=true" class="btn btn-sm btn-primary">
                                            <i class="fa fa-wrench me-1"></i>
                                            {{ trans('generic.fixthishere') }}
                                        </a>
                                        @if($key == 'tutors')
                                            <span class="text-muted">{{trans('generic.mayalsomakeadminatutor')}}:</span>
                                            <a href="/users/profile?setupfix=true" class="btn btn-sm btn-primary">
                                                <i class="fa fa-wrench me-1"></i>
                                                {{ trans('generic.fixthishere') }}
                                            </a>
                                        @endif
                                    </div>
                                </li>
                            @endforeach
                        </ul>
                    </div>{{--/col--}}
                </div>{{--/row--}}
            </div>
        </div>
    </div> {{--/container--}}
@endsection
