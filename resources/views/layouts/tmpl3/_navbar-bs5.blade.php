<nav class="navbar @auth navbar-expand-xxl @endauth navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="/">
            <img src="/images/class-4.png" class="class4logo" alt="Class 4 logo"/>
        </a>
        
        @auth
            {{-- Only show hamburger menu when logged in --}}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                    aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    {{-- Logged in School --}}
                    <li class="nav-item px-2 hide-llg-and-down">
                        <div class="text-light opacity-75 fst-italic small">
                            {{Auth::user()->domain->name}}
                        </div>
                    </li>
                    {{-- DASHBOARD --}}
                    <li class="nav-item ms-4">
                        <a class="nav-link" href="{{url('/home')}}">
                            <i class="fas fa-tachometer-alt hide-llg-and-down"></i>
                            {{ ucfirst(trans('generic.dashboard')) }}
                        </a>
                    </li>
                    {{-- NEW --}}
                    <x-menu-new></x-menu-new>
                    {{-- BASIC DATA --}}
                    <x-menu-basic-data></x-menu-basic-data>
                    {{-- REPORTS --}}
                    <x-menu-reports></x-menu-reports>
                    {{-- TIMETABLES/PLANNING --}}
                    <x-menu-course-schedule></x-menu-course-schedule>
                    {{-- CLASSY --}}
                    {{-- <x-menu-classy></x-menu-classy> --}}
                    {{-- TOOLS --}}
                    <x-menu-tools></x-menu-tools>
                    {{-- SETTINGS --}}
                    <x-menu-settings></x-menu-settings>
                </ul>
                {{-- USER / Profile --}}
                <x-menu-logout-bs5></x-menu-logout-bs5>
            </div>
        @else
            {{-- When not logged in, show login link directly --}}
            <x-menu-login></x-menu-login>
        @endauth
    </div>
</nav>
