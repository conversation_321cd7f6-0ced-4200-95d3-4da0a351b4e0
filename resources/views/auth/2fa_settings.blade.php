@extends('layouts.tmpl3.app-bs5')
@section('content')
    <div class="container">
        <div class="row justify-content-md-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header"><strong>{{ ucfirst(trans('generic.twofactorauthentication')) }}</strong></div>
                    <div class="card-body">
                        <p>{{trans('generic.twofactorexplain')}}</p>
                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if($data['user']->loginSecurity == null)
                            <form method="POST" action="{{ route('generate2faSecret') }}">
                                {{ csrf_field() }}
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-primary">
                                        {{ ucfirst(trans('generic.twofactorgeneratekey')) }}
                                    </button>
                                </div>
                            </form>
                        @elseif(!$data['user']->loginSecurity->google2fa_enable)
                            1. {{ trans('generic.twofactorstep1') }} <code>{{ $data['secret'] }}</code><br/>
                            {!!  $data['google2fa_url'] !!}
                            <br/><br/>
                            2. {{ trans('generic.twofactorstep2') }}<br/><br/>
                            <form method="POST" action="{{ route('enable2fa') }}">
                                {{ csrf_field() }}
                                <div class="mb-3">
                                    <label for="secret" class="form-label">
                                        {{ ucfirst(trans('generic.twofactorauthenticatorcode')) }}
                                    </label>
                                    <input id="secret" type="password" class="form-control @error('verify-code') is-invalid @enderror" name="secret" required style="max-width: 300px;">
                                    @error('verify-code')
                                        <div class="invalid-feedback">
                                            <strong>{{ $message }}</strong>
                                        </div>
                                    @enderror
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    {{ ucfirst(trans('generic.twofactorconfirm')) }}
                                </button>
                            </form>
                        @elseif($data['user']->loginSecurity->google2fa_enable)
                            <div class="alert alert-success">
                                {!! trans('generic.twofactorisenabled') !!}
                            </div>
                            <a href="/home" class="btn btn-primary">{{ ucfirst(trans('generic.gottodashboard')) }}</a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
