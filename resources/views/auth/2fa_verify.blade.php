@extends('layouts.tmpl3.app-bs5')
@section('content')
    <div class="container">
        <div class="row justify-content-md-center">
            <div class="col-md-8 ">
                <div class="card">
                    <div class="card-header"><strong>{{ ucfirst(trans('generic.twofactorauthentication')) }}</strong></div>
                    <div class="card-body">
                        <p>{{trans('generic.twofactorexplain')}}</p>
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        {{ trans('generic.twofactorstep2') }}:<br/><br/>
                        <form action="{{ route('2faVerify') }}" method="POST">
                            {{ csrf_field() }}
                            <div class="mb-3">
                                <label for="one_time_password" class="form-label">
                                    {{ ucfirst(trans('generic.twofactorauthenticatorcode')) }}
                                </label>
                                <input
                                        id="one_time_password"
                                        name="one_time_password"
                                        class="form-control @error('one_time_password-code') is-invalid @enderror"
                                        type="text"
                                        autofocus
                                        required
                                        style="max-width: 300px;"
                                >
                                @error('one_time_password-code')
                                    <div class="invalid-feedback">
                                        <strong>{{ $message }}</strong>
                                    </div>
                                @enderror
                            </div>
                            <button class="btn btn-primary" data-testid="authenticate-btn" type="submit">{{ ucfirst(trans('generic.twofactorauthenticate')) }}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
