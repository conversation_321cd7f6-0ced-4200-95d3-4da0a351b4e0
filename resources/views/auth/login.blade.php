@extends('layouts.tmpl3.app-bs5')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header">
                <h3 class="text-center fw-light my-4">{{ ucfirst(trans('auth.login')) }}</h3>
            </div>
            <div class="card-body">
                <form role="form" method="POST" action="{{ url('/login') }}">

                    {{ csrf_field() }}

                    <div class="mb-3">
                        <label class="form-label" for="inputEmailAddress">
                            {{ucfirst(trans('auth.emailaddress'))}}
                        </label>
                        <input class="form-control py-3 @error('email') is-invalid @enderror"
                               id="inputEmailAddress"
                               name="email"
                               type="email"
                               placeholder="Enter email address"
                               autocomplete="off"
                               required
                               value="{{ old('email') }}">
                        @error('email')
                            <div class="invalid-feedback">
                                <strong>{{ $message }}</strong>
                            </div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label class="form-label" for="inputPassword">{{ucfirst(trans('auth.password'))}}</label>
                        <input class="form-control py-3 @error('password') is-invalid @enderror"
                               id="inputPassword"
                               name="password"
                               type="password"
                               placeholder="Enter password"
                               autocomplete="off"
                               required>
                        @error('password')
                            <div class="invalid-feedback">
                                <strong>{{ $message }}</strong>
                            </div>
                        @enderror
                    </div>

                    <div class="d-flex align-items-center justify-content-between mt-4 mb-0">
                        <button type="submit" class="btn btn-primary" data-testid="login-button">
                            {{ucfirst(trans('auth.login'))}}
                        </button>
                    </div>
                </form>
                @if (session('login_error'))
                    <div class="alert alert-danger mt-3">
                        {{ session('login_error') }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
