/* *************************************************************
 * This file handles all global state, utilities,
 * and browser-wide features
 * ************************************************************* */
import _ from 'lodash';
import mitt from 'mitt';
import useLang from "./composables/generic/useLang.js";

// Global dependencies
window._ = _; // todo: get rid of this global!
window.emitter = mitt();

// Initialize dirty flag functionality
window.dirty = false;
window.onbeforeunload = function () {
    return typeof dirty !== 'undefined' && window.dirty
        ? useLang().translate('generic.warnunsavedchanges')
        : null;
};

// we need a form to log out the standard Laravel way
// we should replace this by an api call
export const initLogoutButton = () => {
    const logoutBtn = document.querySelector('#logoutButton');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', event => {
            event.preventDefault();
            document.querySelector('#logout-form').submit();
        });
    }
};
