<template>
    <div>
        <!-- Show edit form if a student list is selected for editing -->
        <edit-student-list v-if="studentlistIdToEdit >= 0" />
        
        <!-- Show list of student lists -->
        <list-student-lists />
        
        <!-- Floating bar with save button -->
        <floating-bar>
            <div class="btn-group" role="group" aria-label="floating-bar-functions">
                <button v-if="isEditing || isNewRecord" class="btn btn-primary" @click="saveStudentList" :disabled="!isSaveable">
                    {{ ucFirst(translate('generic.save')) }}
                </button>
            </div>
        </floating-bar>
    </div>
</template>

<script setup>
import { onMounted, watch } from "vue";
import EditStudentList from "../components/StudentLists/EditStudentList.vue";
import ListStudentLists from "../components/StudentLists/ListStudentLists.vue";
import FloatingBar from "../components/Layout/FloatingBar.vue";
import useStudentLists from "../composables/useStudentLists.js";
import useLang from "../composables/generic/useLang.js";

const { 
    studentlistIdToEdit,
    isEditing,
    isSaveable,
    isNewRecord,
    saveStudentList, 
    getStudentLists,
    dirty
} = useStudentLists();
const { ucFirst, translate } = useLang();

// Initialize data
onMounted(async () => {
    await getStudentLists();
});

// Set up the dirty-flag for unsaved changes warning
watch(dirty, () => {
    window.dirty = dirty.value;
});
</script>

