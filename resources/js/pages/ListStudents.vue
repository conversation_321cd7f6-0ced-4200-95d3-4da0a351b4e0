<template>
    <Panel
        :busy="busy"
        :collapsible="true"
    >
        <template #title>
            <i class="text-success fas fa-users"></i>
            {{ucFirst(translate('generic.current'))}} {{translateChoice('generic.students', 2)}}
        </template>
        <template #subtitle>
            <div class="cls-grid">
                <div class="input-group">
                    <span class="input-group-text" id="btnGroupAddon"><i class="fa fa-search"></i></span>
                    <input id="searchbox_students"
                           :placeholder="translate('generic.searchfilter')"
                           v-model="studentSearchFilter"
                           name="searchbox_students"
                           type="text"
                           class="form-control"
                           autocomplete="off"
                    >
                </div>
                <a href="/exportcontactlist" class="btn btn-sm btn-primary ms-2 me-2">
                    <font-awesome-icon icon="file-export" />
                    {{ucFirst(translate('generic.exporttoexcel'))}}
                </a>
                <a href="/students/create" class="btn btn-sm btn-success">
                    <font-awesome-icon icon="plus" />
                    {{ucFirst(translate('generic.newstudent'))}}
                </a>
            </div>
        </template>
        <table class="table">
            <thead>
            <tr>
                <th class="text-center"><font-awesome-icon icon="edit" /></th>
                <th>{{ucFirst(translate('generic.name'))}}</th>
                <th>{{ucFirst(translate('generic.email'))}}</th>
                <th>{{ucFirst(translate('generic.telephone'))}}</th>
                <th>
                    {{ucFirst(translate('generic.current'))}} {{translateChoice('generic.courses', 2)}}<br/>
                    <small><font-awesome-icon icon="lock" /> = {{translate('generic.keepcurrentschedule')}}</small>
                </th>
                <th>
                    {{ucFirst(translate('generic.preferencesince'))}}
                </th>
                <th>
                    {{ucFirst(translate('generic.classyaccess'))}}<br/>
                    <small><em>{{translate('generic.clicktorevealpin')}}</em></small>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="student in activeStudents" :key="student.id">
                <td class="text-center">
                    <a :href="'/students/'+student.id + '/edit'"
                       class="btn btn-sm btn-success"
                       v-tooltip="translate('generic.edit')">
                        <font-awesome-icon icon="edit" />
                    </a>
                </td>
                <td>{{student.name}}</td>
                <td v-html="student.contacts
                                .filter(c => c.contacttype==='email')
                                .map(e => `<a href='mailto:${e.value}'>${e.value}</a>`)
                                .join('<br>')"
                >
                </td>
                <td v-html="student.contacts
                                .filter(c => c.contacttype==='telephone')
                                .map(e => `<a href='tel:${e.value}'>${e.value}</a>`)
                                .join('<br>')"
                >
                </td>
                <td v-html="showCourseDetails(student.currentcoursesdetails)"></td>
                <td>
                    <span v-if="student.lastprefdate==='-'">{{translate('generic.unknown')}}</span>
                    <span v-else><a :href="'/students/preferredschedule/' + student.id">{{student.lastprefdate}}</a></span>
                </td>
                <td>
                    <input
                        type="password"
                        :value="student.apipin"
                        class="form-control"
                        @click.prevent="$event.target.type='text'"
                        @blur="$event.target.type='password'"
                        @change="handleSavePinForStudent($event, student.id)"
                        autocomplete="off"
                    >
                    <br/>
                    <span class="text-success" :id="'msg_' + student.id"></span>
                </td>
            </tr>
            </tbody>
        </table>
    </Panel>
    <Panel
        :busy="busy"
        :collapsible="true"
    >
        <template #title>
            <i class="fas fa-users"></i>
            {{ucFirst(translateChoice('generic.formerstudents', 2))}}
        </template>
        <table class="table">
            <thead>
            <tr>
                <th class="text-center"><font-awesome-icon icon="edit" /></th>
                <th>{{ucFirst(translate('generic.name'))}}</th>
                <th>{{ucFirst(translate('generic.email'))}}</th>
                <th>{{ucFirst(translate('generic.telephone'))}}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="student in inactiveStudents" :key="student.id">
                <td class="text-center">
                    <a :href="'/students/'+student.id + '/edit'"
                       class="btn btn-sm btn-success"
                       v-tooltip="translate('generic.edit')">
                        <font-awesome-icon icon="edit" />
                    </a>
                </td>
                <td>{{student.name}}</td>
                <td v-html="student.contacts
                                .filter(c => c.contacttype==='email')
                                .map(e => `<a href='mailto:${e.value}'>${e.value}</a>`)
                                .join('<br>')"
                >
                </td>
                <td v-html="student.contacts
                                .filter(c => c.contacttype==='telephone')
                                .map(e => `<a href='tel:${e.value}'>${e.value}</a>`)
                                .join('<br>')"
                >
                </td>
            </tr>
            </tbody>
        </table>
    </Panel>
</template>

<script setup>
import { onMounted } from 'vue';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import Panel from '../components/Layout/Panel.vue';
import useStudent from "../composables/useStudent.js";
import useToast from "../composables/generic/useToast.js";
import useLang from "../composables/generic/useLang.js";

const { translate, translateChoice, ucFirst } = useLang();
const { successToast, failToast } = useToast();
const { 
    getStudents, 
    savePinForStudent, 
    showCourseDetails, 
    busy, 
    studentSearchFilter, 
    activeStudents, 
    inactiveStudents 
} = useStudent();

onMounted(async () => {
    try {
        await getStudents();
    } catch (error) {
        failToast(
            ucFirst(translate('generic.errorloadingstudents')) + ' ' + error,
            ucFirst(translate('generic.error'))
        );
    }
});

const handleSavePinForStudent = async (event, studentid) => {
    const apipin = event.target.value;
    try {
        const result = await savePinForStudent(studentid, apipin);
        if (result.success) {
            successToast(
                result.message,
                ucFirst(translate('generic.success'))
            );
        }
    } catch (error) {
        failToast(
            error.message || ucFirst(translate('generic.errorsavingpin')),
            ucFirst(translate('generic.notsaving'))
        );
    }
};
</script>

<style scoped>
.cls-grid {
    grid-template-columns: auto 250px 200px;
    display: inline-grid;
}
</style>
