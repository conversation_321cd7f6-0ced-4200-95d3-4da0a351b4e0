<template>
<div>
    <edit-school-year v-if="schoolYearIDToEdit >= 0"/>
    <list-school-years />
    <floating-bar>
        <div class="btn-group" role="group" aria-label="floating-bar-functions">
            <button class="btn btn-primary" @click="saveSchoolyear" :disabled="!isSaveable">
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </div>
    </floating-bar>
</div>
</template>

<script setup>
import { onMounted } from "vue";
import ListSchoolYears from "../components/SchoolYears/ListSchoolYears.vue";
import EditSchoolYear from "../components/SchoolYears/EditSchoolYear.vue";
import useBaseData from "../composables/generic/useBaseData.js";
import useSchoolYear from "../composables/useSchoolYear.js";
import useLang from "../composables/generic/useLang.js";
import FloatingBar from "../components/Layout/FloatingBar.vue";

const { initBaseData }  = useBaseData();
const { isSaveable, schoolYearIDToEdit, saveSchoolyear } = useSchoolYear();
const { ucFirst, translate } = useLang();

const init = async () => {
    await initBaseData({schoolYears: true});
}
onMounted(async () => {
    await init();
});

</script>

<style scoped>

</style>
