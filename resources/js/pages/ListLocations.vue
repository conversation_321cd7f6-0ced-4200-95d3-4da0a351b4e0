<template>
    <panel v-if="showEdit">
        <template v-slot:title>
            {{ ucFirst(translate('generic.editlocation')) }}
        </template>
        <EditLocation
            :location="locationToEdit"
        />
    </panel>
    <panel :busy="busy">
        <template v-slot:title>
            {{ ucFirst(translateChoice('generic.locations', allLocations?.length)) }}<br/>
            <small>{{ ucFirst(translate('generic.explaindeleterestrictionlocation')) }}</small>
        </template>
        <template v-slot:subtitle>
            <button
                class="btn btn-success"
                @click="createNewLocation"
            >
                {{ ucFirst(translate('generic.newlocation')) }}
            </button>
        </template>

        <table class="table">
            <thead>
            <tr>
                <th class="text-center">{{ ucFirst(translate('generic.delete')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.edit')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.icon')) }}</th>
                <th>{{ ucFirst(translate('generic.name')) }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="location in allLocations" :key="location.id">
                <td class="text-center">
                    <button
                        v-if="!location.in_use"
                        class="btn btn-danger me-1"
                        data-bs-toggle="modal"
                        data-bs-target="#confirm-delete-location"
                        @click="locationIdToDelete = location.id"
                    >
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td class="text-center">
                    <button
                        class="btn btn-success"
                        @click="editLocation(location)"
                    >
                        <i class="fa fa-edit"></i>
                    </button>
                </td>
                <td class="text-center">
                    <span
                        v-html="location.icon"
                        class="loc-icon-background"
                    />
                </td>
                <td>
                    {{ location.name }}
                </td>
            </tr>
            </tbody>
        </table>
    </panel>
    <AreYouSure
        :button-text="ucFirst(translate('generic.delete'))"
        modal-id="confirm-delete-location"
        @confirmclicked="deleteLocation"
    />
</template>

<script setup>
import { onMounted, ref } from "vue";
import Panel from "../components/Layout/Panel.vue";
import useLang from "../composables/generic/useLang.js";
import useBaseData from "../composables/generic/useBaseData.js";
import useLocation from "../composables/useLocation.js";

import AreYouSure from "../components/Layout/AreYouSure.vue";
import EditLocation from "../components/Location/EditLocation.vue";

const { translate, translateChoice, ucFirst } = useLang();
const { busy, initBaseData, allLocations } = useBaseData();
const { createNewLocation, deleteLocation, editLocation, locationIdToDelete, locationToEdit, showEdit } = useLocation();


onMounted(async () => {
    await initBaseData({locations: true});
});
</script>

<style scoped>
.table th:first-child {
    width: 8%;
}

.table th:nth-child(2) {
    width: 8%;
}

.table th:nth-child(3) {
    width: 10%;
}

.table th:nth-child(4) {
    width: 74%;
}

.loc-icon-background {
    background-color: white;
    padding: 1rem 0 0.5rem 0;
}
</style>
