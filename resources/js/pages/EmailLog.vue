<template>
  <panel>
    <template #title>
      {{ ucFirst(translate("generic.emaillog")) }}
    </template>
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h5 class="mb-0">
        {{ ucFirst(translate("generic.recentlogentries")) }}&nbsp;
        <small v-if="!showingAll">({{ translate('generic.lastweek') }})</small>
        <small v-else>({{ translate('generic.allentries') }})</small>
      </h5>
      <div>
        <button
          v-if="!showingAll"
          class="btn btn-sm btn-outline-primary"
          @click="showAllEntries"
        >
          {{ translate("generic.seefulllog") }}
        </button>
        <button
          v-else
          class="btn btn-sm btn-outline-secondary"
          @click="showRecentEntries"
        >
          {{ translate("generic.showrecent") }}
        </button>
      </div>
    </div>

    <!-- Pagination Controls (Top) -->
    <pagination
      :pagination-data="paginationData"
      :per-page="perPage"
      @page-changed="handlePageChange"
      @per-page-changed="handlePerPageChange"
      class="mb-3"
    />

    <table class="table">
      <thead>
        <tr>
          <th>&nbsp;</th>
          <th>{{ ucFirst(translate("generic.recipient")) }}</th>
          <th>{{ ucFirst(translate("generic.subject")) }}</th>
          <th>{{ ucFirst(translate("generic.status")) }}</th>
          <th>{{ ucFirst(translate("generic.created")) }}</th>
          <th>{{ ucFirst(translate("generic.updated")) }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="entry in emailLog" :key="entry.id">
          <td>
            <button
              class="btn btn-sm btn-primary"
              data-bs-toggle="modal"
              data-bs-target="#log_entry_preview"
              @click="showEmailLog(entry.id)"
            >
              <font-awesome-icon icon="eye" />
            </button>
            <button
              v-if="entry.status !== 'queued'"
              class="btn btn-sm btn-danger"
              data-bs-toggle="modal"
              data-bs-target="#del_areyousure"
              @click="emailLogEntryToDelete = entry.id"
            >
              <font-awesome-icon icon="trash" />
            </button>
          </td>
          <td>{{ entry.to }}</td>
          <td>{{ entry.subject }}</td>
          <td
            :class="{
              'text-success': entry.status === 'sent',
              'text-danger': entry.status === 'failed',
              'text-warning': entry.status === 'queued',
            }"
          >
            {{ translate("generic." + entry.status) }}
            <span v-tooltip="entry.log">
              <font-awesome-icon
                :icon="entry.status === 'sent' ? 'check' : entry.status === 'queued' ? 'clock' : 'times'"
              />
            </span>
          </td>
          <td>{{ displayDateTime(entry.created_at, true) }}</td>
          <td>{{ displayDateTime(entry.updated_at, true) }}</td>
        </tr>
      </tbody>
    </table>

    <!-- Pagination Controls (Bottom) -->
    <pagination
      :pagination-data="paginationData"
      :per-page="perPage"
      @page-changed="handlePageChange"
      @per-page-changed="handlePerPageChange"
      class="mt-3"
    />

    <are-you-sure
      :button-text="ucFirst(translate('generic.deletelogentry'))"
      @confirmclicked="handleDeleteConfirm"
      modal-id="del_areyousure"
    ></are-you-sure>
    <log-entry-preview
      :email-log="emailLogEntryToShow"
      modal-id="log_entry_preview"
    ></log-entry-preview>
  </panel>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import useEmailLog from '../composables/useEmailLog.js';
import usePagination from '../composables/generic/usePagination.js';
import useDateTime from '../composables/generic/useDateTime.js';
import AreYouSure from '../components/Layout/AreYouSure.vue';
import LogEntryPreview from '../components/Email/LogEntryPreview.vue';
import Panel from '../components/Layout/Panel.vue';
import Pagination from '../components/Layout/Pagination.vue';
import useLang from "../composables/generic/useLang.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { translate, ucFirst } = useLang();
const { displayDateTime } = useDateTime();

// Email log composable
const {
  emailLog,
  showingAll,
  getEmailLog,
  deleteFromEmailLog,
  stopAutoRefresh
} = useEmailLog();

// Pagination composable
const {
  paginationData,
  currentPage,
  perPage,
  updatePaginationData,
  changePage,
  changePerPage,
  getPaginationParams
} = usePagination(25);

// Component state
const emailLogEntryToShow = ref({});
const emailLogEntryToDelete = ref(null);

// Load email log with current pagination settings
const loadEmailLog = async (all = false) => {
  const params = {
    ...getPaginationParams(),
    ...(all && { all: 'true' })
  };

  const response = await getEmailLog(params);
  updatePaginationData(response);
};

// Event handlers
const handlePageChange = async (page) => {
  if (changePage(page)) {
    stopAutoRefresh();
    await loadEmailLog(showingAll.value);
  }
};

const handlePerPageChange = async (newPerPage) => {
  if (changePerPage(newPerPage)) {
    stopAutoRefresh();
    await loadEmailLog(showingAll.value);
  }
};

const handleDeleteConfirm = async () => {
  await deleteFromEmailLog(emailLogEntryToDelete.value);
  // Reload current page after deletion
  await loadEmailLog(showingAll.value);
  emailLogEntryToDelete.value = null;
};

const showEmailLog = (id) => {
  emailLogEntryToShow.value = emailLog.value.find((entry) => entry.id === id);
};

const showAllEntries = async () => {
  stopAutoRefresh();
  await loadEmailLog(true);
};

const showRecentEntries = async () => {
  stopAutoRefresh();
  await loadEmailLog(false);
};

onMounted(() => {
  loadEmailLog();
});
</script>
<style scoped></style>
