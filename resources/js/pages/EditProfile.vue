<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('generic.editprofile')) }}</h3>
        </template>
        <div class="row">
            <div class="col-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <profile-entry />
                    </div>
                </div>
            </div>
            <div class="col-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <change-password />
                    </div>
                </div>
            </div>
            <div class="col-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <delete-student />
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import useLang from "../composables/generic/useLang.js";
import useProfile from "../composables/useProfile.js";
import useToast from "../composables/generic/useToast.js";
import Panel from "../components/Layout/Panel.vue";
import ProfileEntry from "../components/User/ProfileEntry.vue";
import ChangePassword from "../components/User/ChangePassword.vue";
import DeleteStudent from "../components/User/DeleteStudent.vue";

const { ucFirst, translate } = useLang();
const { busy, getProfile } = useProfile();
const { failToast } = useToast();

onMounted(async () => {
    try {
        await getProfile();
    } catch (error) {
        failToast(error.message);
    }
});
</script>

<style scoped>

</style>
