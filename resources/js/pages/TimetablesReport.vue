<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('generic.reportappointments')) }}</h3>
        </template>
        <template #subtitle>
            <!-- drop den to choose a school year -->
            <div class="d-flex justify-content-between align-items-center">
                <div class="me-2">
                    {{ ucFirst(translate('generic.schoolyear')) }}:
                </div>
                <select class="form-select" v-model="selectedSchoolYearId">
                    <option v-for="schoolyear in allSchoolYears" :key="schoolyear.id" :value="schoolyear.id">
                        {{ schoolyear.label }}
                    </option>
                </select>
            </div>
        </template>
        <div class="row">
            <div class="col-12">
                <individual-students-timetables/>
            </div>
            <div class="col-12 mt-4">
                <group-timetables/>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { computed, onMounted, ref, watch } from "vue";
import useLang from "../composables/generic/useLang.js";
import useTimetableReport from "../composables/useTimetableReport.js";
import useBaseData from "../composables/generic/useBaseData.js";
import Panel from "../components/Layout/Panel.vue";
import IndividualStudentsTimetables from "../components/Timetables/IndividualStudentsTimetables.vue";
import GroupTimetables from "../components/Timetables/GroupTimetables.vue";

const { ucFirst, translate } = useLang();
const { busy, fetchTimetablesData } = useTimetableReport();
const { allSchoolYears, initBaseData } = useBaseData();
const selectedSchoolYearId = ref(null);

onMounted(async () => {
    await initBaseData({ schoolYears: true });
    // set the current or future school year as selected if there is no selection yet
    if (!selectedSchoolYearId.value) {
        selectedSchoolYearId.value = currentOrFutureSchoolYear.value.id;
    }
});

watch(selectedSchoolYearId, (newValue, oldValue) => {
    if (newValue && newValue !== oldValue) {
        fetchTimetablesData(newValue);
    }
}, { immediate: true });

// determine the current or future school year
const currentOrFutureSchoolYear = computed(() => {
    const now = new Date();
    const retSchoolYears = allSchoolYears.value.filter(schoolYear => {
        const end = new Date(schoolYear.end_date);
        return (end >= now);
    });
    return retSchoolYears.length > 0 ? retSchoolYears[0] : null;
});
</script>
