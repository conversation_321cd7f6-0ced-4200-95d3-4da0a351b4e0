<template>
    <div class="d-flex flex-row mb-3">
        <!-- Bootstrap 5 flex layout -->
        <div class="me-3" style="flex: 2">
            <CourseGeneric
                :course-groups="filteredCourseGroups"
                :recurrence-options="filteredRecurrenceOptions"
                :domain="domain"
            />
        </div>
        <div style="flex: 1" v-if="courseId > 0">
            <CourseScheduling></CourseScheduling>
        </div>
    </div>
    <!-- only in edit mode -->
    <template v-if="courseId > 0">
        <StudentRegistrationList filter="active_registration"></StudentRegistrationList>
        <StudentRegistrationList filter="no_active_registration"></StudentRegistrationList>
        <CourseStudentGroups></CourseStudentGroups>
    </template>
    <floating-bar>
        <div class="btn-group" role="group" aria-label="floating-bar-functions">
            <a href='/courses' class="btn btn-secondary">
                <font-awesome-icon icon="fa-solid fa-list" />
                {{ucFirst(translate('generic.list'))}}
            </a>
            <button class="btn btn-primary" @click="saveCourseData" :disabled="!isSaveable">
                <font-awesome-icon icon="fa-solid fa-save" />
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </div>
    </floating-bar>
</template>

<script setup>
import { computed, onMounted } from "vue";
import useBaseData from "../composables/generic/useBaseData.js";
import useLang from "../composables/generic/useLang.js";
import useCourse from "../composables/useCourse.js";

import CourseGeneric from "../components/Courses/CourseGeneric.vue";
import StudentRegistrationList from "../components/Courses/StudentRegistrationList.vue";
import CourseScheduling from "../components/Courses/CourseScheduling.vue";
import CourseStudentGroups from "../components/Courses/CourseStudentGroups.vue";
import FloatingBar from "../components/Layout/FloatingBar.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate } = useLang();
const { courseId, isSaveable, initCourseData, saveCourseData } = useCourse();

const props = defineProps({
    courseId: {
        type: Number,
        default: 0
    },
});

const { allCourseGroups, allRecurrenceOptions, domain, initBaseData } = useBaseData();
const filteredCourseGroups = computed(() => {
    // we only need the course groups, not all related data
    return allCourseGroups.value.map(cg => {
        return {
            id: cg.id,
            name: cg.name,
        };
    });
});

const filteredRecurrenceOptions = computed(() => {
    return allRecurrenceOptions.value.map(ro => {
        return {
            id: ro.id,
            name: ro.description,
        };
    });
});

onMounted(() => {
    initBaseData({
        courses: true,
        courseGroups: true,
        domain: true,
        recurrenceOptions: true,
    });
    courseId.value = props.courseId;
    initCourseData();

});

</script>

<style scoped>

</style>
