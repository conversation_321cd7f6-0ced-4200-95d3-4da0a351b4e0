<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-users"></i>
            {{ ucFirst(translate('generic.genericdata')) }}
        </template>
        <div class="row">
            <div class="col-12">
                <div class="mb-3">
                    <label for="studentGroupName" class="form-label">
                        {{ ucFirst(translate('generic.studentgroupname')) }}
                    </label>
                    <input
                        class="form-control"
                        id='studentGroupName'
                        v-model="studentGroup.name"
                    >
                </div>
            </div>
        </div>
        <button
            class="btn btn-primary"
            @click="doSaveStudentGroup"
        >
            {{ ucFirst(translate('generic.save')) }}
        </button>
    </panel>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import Panel from '../Layout/Panel.vue';
import useLang from '../../composables/generic/useLang.js';
import useEditStudentGroup from '../../composables/useEditStudentGroup.js';
import useToast from '../../composables/generic/useToast.js';

const props = defineProps({
    id: {
        type: Number,
        default: 0
    }
});

const { ucFirst, translate } = useLang();
const { successToast, failToast } = useToast();
const busy = ref(false);
const {
    getStudentGroup,
    saveStudentGroup,
    studentGroup
} = useEditStudentGroup();

/**
 * initialize.
 * the props.id needs to be present before getStudentGroup is executed
 */
onMounted(async () => {
    busy.value = true;
    studentGroup.value.id = props.id;
    await getStudentGroup();
    busy.value = false;
});

const doSaveStudentGroup = async () => {
    try {
        await saveStudentGroup();
        successToast(ucFirst(translate('generic.saved')));
    } catch (error) {
        failToast(error);
    }
};
</script>

<style scoped>

</style>
