<template>
    <panel>
        <template v-slot:title>
            <i class="fas fa-users"></i>
            {{ ucFirst(translateChoice('generic.students', 2)) }}
            ({{ students?.length }})
        </template>
        <template v-slot:subtitle v-if="course && course.id > 0">
            <choose-student-for-course-group
                :button-label="ucFirst(translate('generic.addstudenttogroup'))"
                :popup-title="ucFirst(translate('generic.addstudenttogroup'))"
                display-fields="name"
                :api-get-string="'/api/possiblestudentsforstudentgroup/' + studentGroup.id"
                @record-chosen="addStudentToStudentGroup"
                :exclude-ids="excludeIds"
            ></choose-student-for-course-group>
        </template>

        <!-- add a course before a student -->
        <template v-if="course && course.id > 0">
            <table class="table">
                <thead>
                <tr>
                    <th>{{ ucFirst(translate('generic.functions'))}}</th>
                    <th>{{ ucFirst(translate('generic.studentname'))}}</th>
                    <th>{{ ucFirst(translate('generic.participation'))}}</th>
                    <th>{{ ucFirst(translate('generic.remarks'))}}</th>
                </tr>
                </thead>
                <tbody>
                    <tr v-for="student in students" :key="student.id">
                        <td>
                            <button
                                v-tooltip="translate('generic.delete')"
                                class="btn btn-sm btn-outline-danger"
                                @click="removeStudentFromStudentGroup(student.id)"
                            >
                                <i class="fa fa-trash"></i>
                            </button>
                        </td>
                        <td>
                            <a :href="'/students/' + student.id + '/edit'">
                                {{ student.name }}
                            </a>
                        </td>
                        <td>
                            <student-group-participation
                                :student="student"
                                :target-course="course"
                            ></student-group-participation>
                        </td>
                        <td>
                          <span class="text-info" v-if="student.pivot.as_trial_student>0">
                            <i class="fa fa-flag"></i>
                            {{translate('generic.triallesson')}}
                          </span>
                        </td>
                    </tr>
                </tbody>
            </table>

        </template>
        <template v-else>
            <p>{{ ucFirst(translate('generic.pleaseaddacoursefirst'))}}</p>
        </template>
    </panel>
</template>

<script setup>
import { computed } from "vue";
import Panel from "../Layout/Panel.vue";
import useLang from "../../composables/generic/useLang.js";
import useEditStudentGroup from "../../composables/useEditStudentGroup.js";
import ChooseStudentForCourseGroup from "../Layout/QuickJump.vue";
import StudentGroupParticipation from "./StudentGroupParticipation.vue";

const { translate, translateChoice, ucFirst } = useLang();

const {
    addStudentToStudentGroup,
    course,
    students,
    studentGroup,
    removeStudentFromStudentGroup
} = useEditStudentGroup();

// keep track of already associated students,
// so they will not be shown in the search list
const excludeIds = computed(() => {
    return students.value.map(student => student.id);
});

</script>

<style scoped>

</style>
