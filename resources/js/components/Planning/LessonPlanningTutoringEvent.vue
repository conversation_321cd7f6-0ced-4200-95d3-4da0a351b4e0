<template>
    <div
        class="tutoring-event"
        :class="[
            { 'tutoring-event--upcoming': tutoringEvent?.upcoming },
            { 'tutoring-event--notoncalendar': tutoringEvent?.flags?.warnings.length > 0 && !tutoringEvent?.flag_sticky }
            ]"
        draggable="true"
        @dragstart="dragStart"
    >
        <!-- is the event part of a series (>1) -->
        <div v-if="tutoringEvent?.isPartOfSeries"
             class="tutoring-event__series-link"
             v-tooltip="translate('generic.partofseries')"
        >
            <i class="fas fa-link"></i>
        </div>
        <div class="tutoring-event__drag-handle">
            <i class="fas fa-grip-lines"></i>
        </div>
        <div class="tutoring-event__datetime">
            {{ displayDateTime(tutoringEvent?.start, false, true) }}
        </div>
        <!-- location -->
        <div class="tutoring-event__location">
            {{ tutoringEvent?.location }}
        </div>
        <!-- tutor -->
        <div class="tutoring-event__tutor">
            {{ tutoringEvent?.tutor }}
        </div>
        <div class="tutoring-event__actions d-flex justify-content-between">
            <div>
                <button
                    class="btn btn-sm btn-danger"
                    @click="loadAreYouSure"
                    data-bs-toggle="modal"
                    :data-bs-target="'#confirm-delete-event-' + tutoringEvent.id"
                >
                    <i class="fas fa-trash"></i>
                </button>
                <button
                    class="btn btn-sm btn-primary"
                    @click="doEditEvent"
                >
                    <i class="fas fa-edit"></i>
                </button>
                <button
                    v-if="hasFlags"
                    class="btn btn-sm"
                    :class="{'btn-default': !tutoringEvent.flag_sticky, 'btn-info': tutoringEvent.flag_sticky}"
                    @click="toggleStickyById(tutoringEvent.id)"
                    v-tooltip="{ content: translate('generic.' + (tutoringEvent.flag_sticky ? 'un' : '') + 'setsticky'), html: true }"
                >
                    <i class="fas fa-thumbtack"></i>
                </button>
            </div>
            <div class="mt-1 me-1">
                <!-- red flag for errors (tutoringEvent.flags.errors) -->
                <span
                    v-if="tutoringEvent?.flags?.errors.length > 0"
                    class="badge"
                    :class="{'bg-danger': !tutoringEvent.flag_sticky, 'bg-secondary': tutoringEvent.flag_sticky}"
                >
                    <span v-tooltip="tutoringEvent.flags.errors.join(', ')">
                        <i class="fas fa-exclamation-triangle"></i>
                    </span>
                </span>
                <!-- yellow flag for warnings (tutoringEvent.flags.warnings) -->
                <span
                    v-if="tutoringEvent?.flags?.warnings.length > 0"
                    class="badge"
                    :class="tutoringEvent.flag_sticky ? 'bg-secondary' : 'bg-warning'"
                >
                    <span
                        v-tooltip="{
                            content: tutoringEvent.flags.warnings.join(', ') + '<br>' + (tutoringEvent.flag_sticky ? ucFirst(translate('generic.notactive')) : ''),
                            html: true
                        }">
                        <i class="fas fa-exclamation-triangle"></i>
                    </span>
                </span>
                <span v-if="!hasFlags" class="badge bg-success">
                    <span v-tooltip="translate('generic.noerrorsorwarnings')">
                        <i class="fas fa-check"></i>
                    </span>
                </span>
            </div>
        </div>
        <component
            v-if="AreYouSure" :is="AreYouSure"
            :modal-id="'confirm-delete-event-' + tutoringEvent.id"
            @confirmclicked="deleteEventById(tutoringEvent.id)"
            :button-text="ucFirst(translate('generic.delete'))"
        >
            <div>
                {{ translateChoice('generic.followingtaskswillbedeleted', 1) }}
                <br>
                <strong>{{ displayDateTime(tutoringEvent.start, false, true) }}</strong>
                <br>
                {{ translateChoice('generic.locations', 1) }}: {{ tutoringEvent.location }}
                <br>
                {{ translateChoice('generic.tutors', 1) }}: {{ tutoringEvent.tutor }}
            </div>
        </component>
    </div>
</template>

<script setup>
import {computed, nextTick, ref} from 'vue';
import usePlanning from '../../composables/usePlanning.js';
import useEditEvents from '../../composables/useEditEvent.js';
import useDateTime from '../../composables/generic/useDateTime.js';
import useLang from '../../composables/generic/useLang.js';

const {ucFirst, translate, translateChoice} = useLang();
const {displayDateTime} = useDateTime();
const {deleteEventById, toggleStickyById} = usePlanning();
const {eventToEdit} = useEditEvents();

const props = defineProps({
    tutoringEvent: {
        type: Object,
        default: () => {
        }
    }
});

/**
 * Open the modal to edit the event
 * If you send a setDate, this will override the eventToEdit start date value
 * This is used when dragging and dropping an event
 * @param setStartDateTo string | null format 'YYYY-MM-DD HH:mm:ss'
 */
const doEditEvent = (setStartDateTo = null) => {
    eventToEdit.value = props.tutoringEvent;

    if ((setStartDateTo !== null) && (setStartDateTo.length === 19)) {
        // setStartDateTo must be a string in the format 'YYYY-MM-DD HH:mm:ss'
        eventToEdit.value.start = setStartDateTo;
    }
    // now open the modal on nextTick, the component is not yet loaded (v-if)
    nextTick().then(() => {
        const modalElement = document.getElementById('editEvent');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        }
    });
};

defineExpose({
    doEditEvent
});

// Lazy-Load the components
// 1 - Define the components as async components
const AreYouSure = ref(null);
// 2 - Load the components when needed
const loadAreYouSure = async () => {
    AreYouSure.value = () => import('../Layout/AreYouSure.vue');
};
// END Lazy-Load the components

const hasFlags = computed(() => {
    return props.tutoringEvent.flags.errors.length > 0 || props.tutoringEvent.flags.warnings.length > 0;
});

const dragStart = (event) => {
    const dragData = {
        eventId: props.tutoringEvent.eventId || props.tutoringEvent.id,
        startsAt: props.tutoringEvent.start,
        endsAt: props.tutoringEvent.end
    };
    event.dataTransfer.setData("tutoring-event", JSON.stringify(dragData));
    event.dataTransfer.effectAllowed = "move";
};
</script>

<style scoped lang="scss">
@use "sass:color";
@use '../../../sass/tmpl3/variables' as *;
.tutoring-event {
    border: 1px solid $classblue;
    padding: .5rem;
    height: 8rem;
    width: 15rem;
    position: relative;
    border-radius: 5px;
    cursor: move;
    transition: all 0.2s ease;

    &:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &__location {
        text-overflow: ellipsis;
    }

    &__tutor {
        text-overflow: ellipsis;
    }

    &__series-link {
        // in relation to the .tutoring-event, which is relative
        position: absolute;
        top: 0;
        right: 2px;
    }

    &__drag-handle {
        // in relation to the .tutoring-event, which is relative
        position: absolute;
        top: 0;
        right: 30px;
        cursor: move;
    }

    &__actions {
        background-color: color.scale($classblue, $lightness: 20%);
        padding: 0.1rem;
        border: ridge 1px $classblue;
        border-radius: 0.25rem;
    }

    &--upcoming {
        // if both upcoming AND notoncalendar, this border will still indicate upcoming
        border: solid 2px $classred;
        background-color: $classblue;
        color: white;
    }

    &--notoncalendar {
        background-color: color.scale($classblue, $lightness: 40%);
        color: white;
    }
}
</style>
