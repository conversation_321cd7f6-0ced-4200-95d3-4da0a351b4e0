<template>
    <panel v-if="trialRequest?.trialstudent?.id">
        <template #title>{{ ucFirst(translate('generic.details'))}}</template>

        <div class="mb-3 row">
            <label for="name" class="col-sm-3 col-form-label">{{ ucFirst(translate('generic.name')) }}</label>
            <div class="col-sm-9">
                <input type="text" readonly class="form-control-plaintext" id="name" :value="getFullName(trialRequest.trialstudent)">
            </div>
            <!---->
            <label for="email" class="col-sm-3 col-form-label">{{ ucFirst(translate('generic.email')) }}</label>
            <div class="col-sm-9">
                <a :href="'mailto:' + trialRequest.trialstudent.email" target="_blank">{{ trialRequest.trialstudent.email }}</a>
            </div>
            <!---->
            <label for="telephone" class="col-sm-3 col-form-label">{{ ucFirst(translate('generic.telephone')) }}</label>
            <div class="col-sm-9">
                <a :href="'tel:' + trialRequest.trialstudent.telephone" target="_blank">{{ trialRequest.trialstudent.telephone }}</a>
            </div>
            <!---->
            <label for="coursename" class="col-sm-3 col-form-label">{{ ucFirst(translate('generic.coursename')) }}</label>
            <div class="col-sm-9">
                <input
                    type="text"
                    readonly
                    class="form-control-plaintext"
                    id="coursename"
                    :value="trialRequest.trialstudent?.course?.name ?? 'Anders'"
                >
            </div>
            <!---->
            <label for="requestedstartdate" class="col-sm-3 col-form-label">{{ ucFirst(translate('generic.requestedstartdate')) }}</label>
            <div class="col-sm-9">
                <input type="text" readonly class="form-control-plaintext" id="requestedstartdate" :value="trialRequest.trialstudent.requested_startdate">
            </div>
            <!---->
            <label for="remarks" class="col-sm-3 col-form-label">{{ ucFirst(translate('generic.remarks')) }}</label>
            <div class="col-sm-9" v-html="trialRequest.trialstudent.remarks" />
        </div>
        <div class="row">
            <div class="col-12">
                <label for="status" class="form-label">{{ ucFirst(translate('generic.status')) }}</label>
                <select
                    v-model="trialRequest.trialstudent.trialrequeststatus.id"
                    class="form-select"
                    id="status"
                >
                    <option
                        v-for="status in allStatuses"
                        :key="status.id"
                        :value="status.id"
                    >
                        {{ status.description }}
                    </option>
                </select>
            </div>
        </div>
        <hr>
        <div class="row">
            <div v-if="hasNoGeneratedStudentId" class="col-8">
                <button
                    @click="createStudentFromTrialRequest(trialRequest.trialstudent.id)"
                    class="btn btn-success"
                >
                    <font-awesome-icon icon="user-plus" />
                    {{ ucFirst(translate('generic.creatstudentfromtrialrequest')) }}
                </button>
            </div>
            <div v-else class="col-8">
                <a :href="'/students/' + trialRequest.trialstudent.generated_student_id + '/edit'" class="btn btn-primary">
                    <font-awesome-icon icon="user" />
                    {{ ucFirst(translate('generic.openstudentpage')) }}
                </a>
            </div>
            <div class="col-4">
                <button
                    @click="updateTrialRequest(trialRequest.trialstudent)"
                    class="btn btn-primary float-end"
                >
                    <font-awesome-icon icon="save" />
                    {{ ucFirst(translate('generic.save')) }}
                </button>
            </div>
        </div>
    </panel>
    <panel v-else>
        <template #title>{{ ucFirst(translate('generic.details'))}}</template>
        {{ ucFirst(translate('generic.pickarecord')) }}...
    </panel>
</template>

<script setup>
import useEditTrialRequests from '../../composables/useEditTrialRequests.js';
import useLang from '../../composables/generic/useLang.js';
import Panel from '../Layout/Panel.vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { computed } from 'vue';

const { translate, ucFirst } = useLang();
const {
    allStatuses,
    createStudentFromTrialRequest,
    getFullName,
    trialRequest,
    updateTrialRequest
} = useEditTrialRequests();

const hasNoGeneratedStudentId = computed(() => {
    return trialRequest.value.trialstudent.generated_student_id == null ||
           trialRequest.value.trialstudent.generated_student_id === 0;
});
</script>

<style scoped>
</style>
