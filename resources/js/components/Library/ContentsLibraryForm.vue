<template>
<!-- List current documents for the library -->
<div class="table-responsive">
    <!-- Add a dropdown container for multiselects -->
    <div id="multiselect-dropdown-container" class="multiselect-dropdown-container"></div>

    <table class="table table-sm">
        <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.functions')) }}</th>
                <th>{{ ucFirst(translateChoice('generic.documents', 2)) }}</th>
                <th>{{ ucFirst(translate('generic.type')) }}</th>
                <th>{{ ucFirst(translateChoice('generic.tags', 2)) }}</th>
                <th>{{ ucFirst(translate('generic.download')) }}</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="doc in libraryToManageContents.documents" :key="doc.id">
                <td>
                    <div class="btn-group btn-group-sm" role="group" aria-label="Document actions">
                        <button
                            class="btn btn-danger"
                            @click="removeDocumentFromLibrary(libraryToManageContents.id, doc.id)"
                            v-tooltip="ucFirst(translate('generic.unlink'))"
                        >
                            <font-awesome-icon icon="unlink" />
                        </button>
                        <button
                            v-if="doc.type === 'url'"
                            class="btn btn-primary"
                            @click="startEditingLink(doc)"
                            data-bs-toggle="modal"
                            data-bs-target="#addLinkModal"
                            v-tooltip="ucFirst(translate('generic.edit'))"
                        >
                            <font-awesome-icon icon="pen" />
                        </button>
                    </div>
                </td>
                <td>{{ doc.label }}</td>
                <td>
                    {{ doc.type }}
                    <font-awesome-icon v-if="doc.type === 'url'" icon="link" />
                    <font-awesome-icon v-else icon="file-pdf" />
                </td>
                <td>
                    <DocumentTags :document="doc" />
                </td>
                <td>
                    <a v-if="doc.type === 'url'" class="btn w-100 btn-outline-success btn-sm" :href="doc.url" target="_blank">
                        <font-awesome-icon icon="link" />
                        {{ ucFirst(translate('generic.followlink')) }}
                    </a>
                    <a v-else class="btn w-100 btn-outline-primary btn-sm" :href="`/file/${doc.file_name}`">
                        <font-awesome-icon icon="download" />
                        {{ ucFirst(translate('generic.download')) }}
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- choose an existing document -->
<div>
    <strong>{{ ucFirst(translate("generic.findDocumentToLinkToLibrary")) }}</strong>
    <input type="text" v-model="documentSearchKey" class="form-control" placeholder="Search document">
</div>
<div v-if="documentSearchKey.length > 0">
    <div class="table-responsive" v-if="filteredDocuments.length > 0">
        <table class="table table-sm">
            <tbody>
                <tr v-for="doc in filteredDocuments" :key="doc.id">
                    <td>
                        <button class="btn btn-success btn-sm" @click="addDocumentToLibrary(libraryToManageContents.id, doc.id)">
                            {{ ucFirst(translate('generic.link')) }}
                        </button>
                    </td>
                    <td>{{ doc.label }}</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div v-else>
        {{ ucFirst(translate('generic.nodocumentsfound')) }}
    </div>
</div>

<!-- Add new link button -->
<div class="mt-3">
    <strong>-{{ translate("generic.or") }}-<br>{{ ucFirst(translate('generic.addnewlink')) }}</strong>
    <button
        class="btn btn-outline-primary btn-sm d-block mt-2"
        data-bs-toggle="modal"
        data-bs-target="#addLinkModal"
    >
        <font-awesome-icon icon="link" />
        {{ ucFirst(translate('generic.addlink')) }}
    </button>
</div>

<!-- upload a new document -->
<div class="mt-3">
    <strong>-{{ translate("generic.or") }}-<br>{{ ucFirst(translate('generic.uploaddocandlink')) }}</strong>
    <UploadForm :initiateUploadButtonText="ucFirst(translate('generic.startupload'))" @uploadSuccess="linkToLibrary"/>
</div>

<!-- Add Link Modal -->
<AddLinkModal />

</template>

<script setup>
import { watch, ref, onMounted, computed } from "vue";
import UploadForm from "./UploadForm.vue";
import AddLinkModal from "./AddLinkModal.vue";
import DocumentTags from "./DocumentTags.vue";
import useLibraries from "../../composables/useLibraries.js";
import useDocuments from "../../composables/useDocuments.js";
import useDocumentTags from "../../composables/useDocumentTags.js";
import useLang from "../../composables/generic/useLang.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { libraryToManageContents } = useLibraries();
const {
    addDocumentToLibrary,
    documentSearchKey,
    filteredDocuments,
    getDocuments,
    removeDocumentFromLibrary,
    setCurrentLibrary,
    startEditingLink
} = useDocuments();

const {
    getTags,
    initializeDocumentTags
} = useDocumentTags();

const { translate, translateChoice, ucFirst } = useLang();

// Load document tags when component mounts
onMounted(async () => {
    await getTags();
});

watch(libraryToManageContents, async () => {
    setCurrentLibrary(libraryToManageContents.value.id);
    await getDocuments(libraryToManageContents.value.id);
    // Initialize all document tags
    await initializeDocumentTags(libraryToManageContents.value.documents);
}, { immediate: true });

const linkToLibrary = (docId) => {
    addDocumentToLibrary(libraryToManageContents.value.id, docId);
}

</script>

<style scoped>
.multiselect-dropdown-container {
    position: relative;
    z-index: 1060;
    pointer-events: none; /* Allow clicks to pass through */
}
</style>
