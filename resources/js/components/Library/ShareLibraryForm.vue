<template>
<div>
    <strong>{{ ucFirst(translate("generic.sharing")) }}</strong>
    <div class="d-flex justify-content-between">
        <p>{{ ucFirst(translate("generic.sharewithwholeschool")) }}</p>
        <div class="form-check form-switch">
            <input
                id="shareWholeSchool"
                class="form-check-input"
                type="checkbox"
                v-model="shareWholeSchool"
            >
            <label class="form-check-label" for="shareWholeSchool">
                {{ shareWholeSchool ? translate('generic.yes') : translate('generic.no') }}
            </label>
        </div>
    </div>
    <template v-if="!shareWholeSchool && libraryToManageShares.id">
        <hr>
        <p>{{ ucFirst(translate("generic.share_with")) }}</p>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.courses", 2)) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareCoursesSelected"
                    :options="coursesOptionsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select courses"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedCoursesDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="course in selectedCoursesDisplay" :key="course.id" class="badge bg-primary d-flex align-items-center">
                            {{ course.label }}
                            <span @click="removeSelectedCourse(course.id)" class="cursor-pointer ms-1">
                                <font-awesome-icon icon="times"/>
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translate("generic.coursegroups")) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareCourseGroupsSelected"
                    :options="courseGroupsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select course groups"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedCourseGroupsDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="courseGroup in selectedCourseGroupsDisplay" :key="courseGroup.id" class="badge bg-success d-flex align-items-center">
                            {{ courseGroup.label }}
                            <span @click="removeSelectedCourseGroup(courseGroup.id)" class="cursor-pointer ms-1">
                                <font-awesome-icon icon="times"/>
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.students", 2)) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareStudentsSelected"
                    :options="studentsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select students"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedStudentsDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="student in selectedStudentsDisplay" :key="student.id" class="badge bg-info d-flex align-items-center">
                            {{ student.label }}
                            <span @click="removeSelectedStudent(student.id)" class="cursor-pointer ms-1">
                                <font-awesome-icon icon="times"/>
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.studentgroups")) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareStudentGroupsSelected"
                    :options="studentGroupsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select student groups"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedStudentGroupsDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="studentGroup in selectedStudentGroupsDisplay" :key="studentGroup.id" class="badge bg-warning text-dark d-flex align-items-center">
                            {{ studentGroup.label }}
                            <span @click="removeSelectedStudentGroup(studentGroup.id)" class="cursor-pointer ms-1">
                                <font-awesome-icon icon="times"/>
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </template>
    <hr>
    <div class="d-flex justify-content-end">
        <!-- Save button -->
        <button class="btn btn-primary" @click="saveSharesLibrary">
            {{ ucFirst(translate("generic.save")) }}
        </button>
    </div>
</div>
</template>

<script setup>
import { computed, onMounted, watch } from "vue";
import useLang from "../../composables/generic/useLang.js";
import useBaseData from "../../composables/generic/useBaseData.js";
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';
import useLibraries from "../../composables/useLibraries.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { allCourses, allCourseGroups, allStudentGroups, allStudents, initBaseData } = useBaseData();
const { ucFirst, translate, translateChoice } = useLang();
const {
    saveSharesLibrary,
    libraryToManageShares,
    shareWholeSchool,
    shareCoursesSelected,
    shareCourseGroupsSelected,
    shareStudentGroupsSelected,
    shareStudentsSelected
} = useLibraries();

/**
 * strip the api data to only include the id and name
 */
const coursesOptionsForSelect = computed(() => {
    return allCourses.value.map(course => ({
        id: course.id,
        label: `${course.name} ${course.recurrenceoption}`
    }));
});
const courseGroupsForSelect = computed(() => {
    return allCourseGroups.value.map(courseGroup => ({
        id: courseGroup.id,
        label: courseGroup.name
    }));
});
const studentsForSelect = computed(() => {
    return allStudents.value.map(student => ({
        id: student.id,
        label: student.name
    }));
});
const studentGroupsForSelect = computed(() => {
    return allStudentGroups.value.map(studentGroup => ({
        id: studentGroup.id,
        label: studentGroup.name
    }));
});

// Computed properties to display selected items
const selectedCoursesDisplay = computed(() => {
    return coursesOptionsForSelect.value.filter(course =>
        shareCoursesSelected.value.includes(course.id)
    );
});

const selectedCourseGroupsDisplay = computed(() => {
    return courseGroupsForSelect.value.filter(courseGroup =>
        shareCourseGroupsSelected.value.includes(courseGroup.id)
    );
});

const selectedStudentsDisplay = computed(() => {
    return studentsForSelect.value.filter(student =>
        shareStudentsSelected.value.includes(student.id)
    );
});

const selectedStudentGroupsDisplay = computed(() => {
    return studentGroupsForSelect.value.filter(studentGroup =>
        shareStudentGroupsSelected.value.includes(studentGroup.id)
    );
});

// Methods to remove individual entities from selected lists
const removeSelectedCourse = (courseId) => {
    shareCoursesSelected.value = shareCoursesSelected.value.filter(id => id !== courseId);
};

const removeSelectedCourseGroup = (courseGroupId) => {
    shareCourseGroupsSelected.value = shareCourseGroupsSelected.value.filter(id => id !== courseGroupId);
};

const removeSelectedStudent = (studentId) => {
    console.log("removeSelectedStudent");
    shareStudentsSelected.value = shareStudentsSelected.value.filter(id => id !== studentId);
};

const removeSelectedStudentGroup = (studentGroupId) => {
    shareStudentGroupsSelected.value = shareStudentGroupsSelected.value.filter(id => id !== studentGroupId);
};

onMounted(() => {
    initBaseData({
        courses: true,
        courseGroups: true,
        studentGroups: true,
        students: true
    }).then(() => {
        initLibraryShares();
    });
});
const initLibraryShares = () => {
    shareWholeSchool.value = !!libraryToManageShares.value.share_with_whole_school; // convert initial value to boolean
    shareCoursesSelected.value = libraryToManageShares.value.courses
        ? libraryToManageShares.value.courses.map(course => course.id)
        : [];
    shareCourseGroupsSelected.value = libraryToManageShares.value.coursegroups
        ? libraryToManageShares.value.coursegroups.map(courseGroup => courseGroup.id)
        : [];
    shareStudentsSelected.value = libraryToManageShares.value.students
        ? libraryToManageShares.value.students.map(student => student.id)
        : [];
    shareStudentGroupsSelected.value = libraryToManageShares.value.studentgroups
        ? libraryToManageShares.value.studentgroups.map(studentGroup => studentGroup.id)
        : [];
};

watch(libraryToManageShares, () => {
    initLibraryShares();
});
</script>

<style scoped>
.cursor-pointer {
    cursor: pointer;
}
</style>
