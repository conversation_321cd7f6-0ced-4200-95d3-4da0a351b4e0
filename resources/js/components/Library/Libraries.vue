<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translateChoice('generic.libraries', 2)) }}</h3>
        </template>
        <template #subtitle>
            <button
                class="btn btn-primary"
                @click.prevent="emptyLibrary"
            >
                {{ ucFirst(translate('generic.newlibrary')) }}
            </button>
        </template>
        <div class="row">
            <!-- Edit section - shows first on mobile/tablet, right on desktop -->
            <div class="col-12 col-xxl-6 d-flex flex-column order-1 order-xxl-2">
                <div class="card h-100">
                    <div class="card-body">
                        <EditLibraries />
                    </div>
                </div>
            </div>
            <!-- List section - shows second on mobile/tablet, left on desktop -->
            <div class="col-12 col-xxl-6 d-flex flex-column order-2 order-xxl-1">
                <div class="card h-100">
                    <div class="card-body">
                        <ListLibraries />
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import Panel from "../Layout/Panel.vue";
import ListLibraries from "./ListLibraries.vue";
import EditLibraries from "./EditLibraries.vue";
import useLang from "../../composables/generic/useLang.js";
import useLibraries from "../../composables/useLibraries.js";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, emptyLibrary, getLibraries } = useLibraries();

onMounted(() => {
    getLibraries();
});
</script>

<style scoped>

</style>
