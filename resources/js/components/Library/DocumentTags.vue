<template>
    <div class="tags-container">
        <Multiselect
            v-model="selectedTags"
            :options="tagsOptionsForSelect"
            :searchable="true"
            :close-on-select="false"
            :create-option="true"
            :append-new-option="false"
            :append-to-body="true"
            :disabled="!isThisDropdownOpen && openDropdownId !== null"
            mode="multiple"
            :placeholder="ucFirst(translate('generic.selectortags'))"
            valueProp="id"
            label="label"
            :on-create="handleCreateTag"
            @change="handleTagsChange"
            @open="handleDropdownOpen"
            @close="handleDropdownClose"
            class="tags-multiselect"
        />

        <!-- Display selected tags as badges -->
        <div v-if="displayTags.length > 0" class="mt-2">
            <div class="d-flex flex-wrap gap-1 mt-1">
                <span v-for="tag in displayTags" :key="tag.id" class="badge bg-info text-dark">
                    {{ tag.label }}
                    <button type="button"
                            class="btn-close ms-1"
                            @click="handleRemoveTag(tag.id)"
                            style="font-size: 0.7em;"
                            :title="ucFirst(translate('generic.removetag'))">
                        <font-awesome-icon icon="trash" />
                    </button>
                </span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import useDocumentTags from '../../composables/useDocumentTags.js';
import useLang from '../../composables/generic/useLang.js';
import useToast from '../../composables/generic/useToast.js';

// Props - we need the document object to work with
const props = defineProps({
    document: {
        type: Object,
        required: true
    }
});

const { translate, ucFirst } = useLang();
const { successToast, failToast } = useToast();
const {
    documentTags,
    tagsOptionsForSelect,
    openDropdownId,
    updateDocumentTags,
    removeTagFromDoc,
    createNewTag,
    setDropdownOpen,
    setDropdownClosed,
    isDropdownOpen
} = useDocumentTags();

// Computed property for this specific document's selected tags
const selectedTags = computed({
    get: () => documentTags.value[props.document.id] || [],
    set: (value) => {
        documentTags.value[props.document.id] = value;
    }
});

// Computed property for displaying tags as badges for this document
const displayTags = computed(() => {
    const selectedTagIds = selectedTags.value;
    return tagsOptionsForSelect.value.filter(tag =>
        selectedTagIds.includes(tag.id)
    );
});

// Check if this specific dropdown should be open
const isThisDropdownOpen = computed(() => {
    return isDropdownOpen(props.document.id);
});

const handleDropdownOpen = () => {
    setDropdownOpen(props.document.id);
};

const handleDropdownClose = () => {
    setDropdownClosed(props.document.id);
};

const handleCreateTag = async (option, select$) => {
    try {
        // Debug what we're receiving
        console.log('>>>> handleCreateTag received option:', option);
        
        // Extract the tag name - try different possible structures
        const tagName = option?.label || option?.value || option;
        
        console.log('>>>> Extracted tagName:', tagName);
        
        if (!tagName) {
            console.error('>>>> No tag name found in option:', option);
            failToast('Invalid tag name');
            return false;
        }
        
        // Create the new tag via API
        const newTag = await createNewTag(tagName);
        
        // Add the new tag to the current document's selected tags
        const currentTags = selectedTags.value || [];
        selectedTags.value = [...currentTags, newTag.id];
        
        // Update the document tags
        await updateDocumentTags(props.document.id, selectedTags.value, props.document);
        
        successToast('Tag created and added successfully');
        
        // Return the new tag object for the multiselect
        return {
            id: newTag.id,
            label: newTag.label,
            value: newTag.id
        };
    } catch (error) {
        console.error('>>>> Error in handleCreateTag:', error);
        failToast('Failed to create tag');
        return false; // Prevents the tag from being added to the multiselect
    }
};

const handleTagsChange = () => {
    updateDocumentTags(props.document.id, selectedTags.value, props.document);
};

const handleRemoveTag = (tagId) => {
    removeTagFromDoc(props.document.id, tagId, props.document);
};
</script>

<style scoped>
.tags-container {
    min-width: 250px;
    position: relative;
}

.tags-multiselect {
    --ms-tag-bg: #6c757d;
    --ms-tag-color: #ffffff;
    --ms-tag-radius: 4px;
    --ms-tag-font-size: 0.75rem;
    --ms-tag-line-height: 1.25rem;
    --ms-tag-font-weight: 400;
    --ms-tag-py: 0.125rem;
    --ms-tag-px: 0.5rem;
    --ms-ring-width: 0px;
    --ms-border-color: #ced4da;
    --ms-border-width: 1px;
    --ms-radius: 0.375rem;
    --ms-py: 0.375rem;
    --ms-px: 0.75rem;
    --ms-font-size: 0.875rem;
}

.badge {
    display: inline-flex;
    align-items: center;
}

.btn-close {
    padding: 0;
    margin: 0;
    border: none;
    background: none;
    opacity: 0.8;
}

.btn-close:hover {
    opacity: 1;
}
</style>
