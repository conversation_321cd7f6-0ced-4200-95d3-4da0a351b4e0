<template>
    <div>
        <strong v-if="!currentChangeAction">{{ ucFirst(translate("generic.pleasestartwithactioninlistorcreatenew")) }}</strong>
        <div>
            <EditLibraryForm v-if="currentChangeAction === 'edit'" />
            <ContentsLibraryForm v-if="currentChangeAction === 'manageContents'" />
            <ShareLibraryForm v-if="currentChangeAction === 'manageSharing'" />
        </div>
    </div>
</template>

<script setup>
import useLang from "../../composables/generic/useLang.js";
import useLibraries from "../../composables/useLibraries.js";
import EditLibraryForm from "./EditLibraryForm.vue";
import ShareLibraryForm from "./ShareLibraryForm.vue";
import ContentsLibraryForm from "./ContentsLibraryForm.vue";

const { currentChangeAction } = useLibraries();
const { ucFirst, translate } = useLang();
</script>

<style scoped>

</style>
