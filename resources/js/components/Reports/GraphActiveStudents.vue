<template>
    <canvas ref="chartRef"></canvas>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import Chart from 'chart.js/auto';
import useLang from '../../composables/generic/useLang.js';
import useToast from '../../composables/generic/useToast.js';

const { ucFirst, translate } = useLang();
const { failToast } = useToast();
const props = defineProps({
    data: {
        type: Object,
        required: true
    }
});

// Calculate labels and data from the data object
const chartLabels = computed(() => Object.keys(props.data));
const chartData = computed(() => Object.values(props.data).map(monthData => monthData.length));

const chartRef = ref(null);

onMounted(async () => {
    try {
        const ctx = chartRef.value.getContext('2d');

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartLabels.value,
                datasets: [{
                    label: ucFirst(translate('reporting.number_of_students')),
                    data: chartData.value,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                    ],
                    borderColor: [
                        'rgba(255,99,132,1)',
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                plugins: {
                    title: {
                        display: true,
                        text: ucFirst(translate('reporting.active_students_per_month')),
                        font: {
                            size: 16
                        },
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    } catch (error) {
        failToast(error);
    }
});
</script>
