<template>
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="name" class="form-label">
                    {{ ucFirst(translate('generic.name')) }}
                </label>
                <input class="form-control" v-model="locationToEdit.name" />
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="name" class="form-label">
                    {{ ucFirst(translate('generic.locationicon')) }}
                </label>
                <div
                    v-html="locationToEdit.icon"
                    class="loc-icon-background"
                />
            </div>
        </div>
    </div>
    <button class="btn btn-primary" @click="saveLocationData">
        {{ ucFirst(translate('generic.save')) }}
    </button>
</template>

<script setup>
import useLang from '../../composables/generic/useLang.js';
import useLocation from '../../composables/useLocation.js';

const { ucFirst, translate } = useLang();
const { locationToEdit, saveLocationData } = useLocation();

const props = defineProps({
    location: {
        type: Object,
        required: true
    }
});
</script>

<style scoped>
.loc-icon-background {
    background-color: white;
    padding: 0;
}
</style>
