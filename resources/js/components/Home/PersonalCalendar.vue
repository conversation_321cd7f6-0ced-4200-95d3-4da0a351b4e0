<template>
    <div class="card shadow mb-4 min-with-cards-3" data-testid="personal-timetable">
        <!-- Card Header - Dropdown -->
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold text-primary">
                <i class="fas fa-calendar"></i> {{ucFirst(translateChoice('generic.timetables',1))}}
                <small v-if="filterAppointments">{{translate('generic.for')}} {{tutorName}}</small>
                <small v-else>{{translate('generic.fortheschool')}}</small>
            </h6>
            <VDropdown ref="dropdown">
                <button class="btn btn-success btn-sm">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw"></i>
                </button>
                <template #popper>
                    <div class="bg-white border rounded shadow-sm py-1" style="min-width: 160px;">
                        <button class="btn btn-link px-3 py-2 w-100 text-start text-dark text-decoration-none" @click="setFilterAppointments(true); dropdown.hide()">
                            {{ucFirst(translate('generic.myappointments'))}}
                        </button>
                        <button class="btn btn-link px-3 py-2 w-100 text-start text-dark text-decoration-none" @click="setFilterAppointments(false); dropdown.hide()">
                            {{ucFirst(translate('generic.allappointments'))}}
                        </button>
                    </div>
                </template>
            </VDropdown>
        </div>
        <!-- Card Body -->
        <div class="card-body">
            <div class="chart-area">
                <div class="container">
                    <div class="row" v-for="(appointment, index) in filteredAppointments" :key="index">
                        <div class="col">
                            <span :class="appointment.color">
                                {{displayTime(appointment.datetime)}}-{{displayTime(appointment.enddatetime)}}: {{appointment.courseName}} ({{appointment.name}}-{{appointment.studentName}})
                            </span>
                        </div>
                    </div>
                    <div v-if="filteredAppointments.length === 0">
                        {{ucFirst(translate('generic.nothingscheduled'))}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, ref, watchEffect } from 'vue';
import useLang from '../../composables/generic/useLang.js';
import useDateTime from "../../composables/generic/useDateTime.js";
import useToast from "../../composables/generic/useToast.js";
import useApi from "../../composables/generic/useApi.js";

const { ucFirst, translate, translateChoice } = useLang();
const { displayTime, isFutureDateTime } = useDateTime();
const { failToast } = useToast();
const { apiGet } = useApi();

const filterAppointments = ref(true);
const allAppointments = ref([]);
const now = ref(new Date());
const dropdown = ref(null);

const props = defineProps({
    tutorId: {
        type: Number,
        required: true
    },
    tutorName: {
        type: String,
        required: true
    }
});

onMounted(() => {
    getEventsOfToday();
    setInterval(() => {
        now.value = new Date();
    }, 10000);
});

const getEventsOfToday = async () => {
    try {
        const response = await apiGet('/api/eventsoftoday');
        allAppointments.value = response.data.data;
    } catch (error) {
        failToast(ucFirst(translate('generic.errorfetchingappointmentsoftoday'))
            + `: ${error}`);
    }
};

const setFilterAppointments = (value) => {
    filterAppointments.value = value;
};

const filteredAppointments = computed(() => {
    return filterAppointments.value
        ? allAppointments.value.filter(appointment => {
            return appointment.tutor_id === parseInt(props.tutorId) || appointment.tutor_id === 0
          })
        : allAppointments.value;
});

watchEffect(() => {
    filteredAppointments.value.forEach((appointment) => {
        const isEndDateFuture = isFutureDateTime(appointment.enddatetime);
        const isStartDateFuture = isFutureDateTime(appointment.datetime);

        if (!isEndDateFuture) {
            appointment.color = 'past';
        } else if (isStartDateFuture) {
            appointment.color = 'future';
        } else {
            appointment.color = 'current';
        }
    });
});
</script>

<style scoped lang="scss">
@use '../../../sass/tmpl3/variables' as *;
a:focus {
    outline: none;
}
.future {
    color: $classdark;
}
.past {
    color: $classgreen;
}
.current {
    color: $classred;
}
</style>
