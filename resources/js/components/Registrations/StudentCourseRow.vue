<template>
    <div class="row mb-3">
        <div class="col-1">
            <div class="d-flex align-items-center flex-wrap gap-1">
                <VDropdown v-if="registration.tutoringInStudentGroup && registration.studentgroupname !== ''">
                    <span
                        tabindex="0"
                        v-tooltip="
                            ucFirst(translate('generic.groupcourseextend')) +
                            (registration.studentgroupname === ''
                            ? ''
                            : '. ' + ucFirst(translateChoice('generic.studentgroups', 1)) + ': ' + registration.studentgroupname)
                        "
                        class="me-1"
                    >
                        <i class="fas fa-users"></i>
                    </span>
                    <template #popper>
                        <div class="popover-content p-2">
                            <strong>{{ registration.studentgroupname }}</strong>
                            <div v-html="getStudentgroupPopoverContent()"></div>
                        </div>
                    </template>
                </VDropdown>
                <span v-else
                      class="me-1"
                      v-tooltip="ucFirst(translate('generic.individualcourseextend'))">
                    <i class="fas fa-user"></i>
                </span>
                
                <span v-if="!isActive">
                    <button class="btn btn-xs btn-outline-secondary"
                            type="button"
                            data-bs-toggle="modal"
                            :data-bs-target="'#delRegistrationPopup' + registration.pivot.id"
                            v-tooltip="ucFirst(translate('generic.delete'))">
                        <i class="fas fa-trash"></i>
                    </button>
                </span>

                <span v-if="isActive" class="d-flex gap-1">
                    <a :class="'btn btn-xs btn-outline-secondary ' + classForChecklist"
                            v-tooltip="labelForChecklist"
                            v-if="userisadmin"
                            :href="'/registrationedit/' + registration.pivot.id">
                        <i class="fas fa-edit"></i>
                    </a>
                    <!-- Tutor should be able to check tutoring schedule, so no v-if -->
                    <a :href="'/planning/lesson?studentId=' + registration.pivot.student_id + '&courseId=' + registration.pivot.course_id"
                       class="btn btn-xs btn-outline-secondary"
                       v-tooltip="ucFirst(translate('generic.opentimetable'))"
                    >
                        <i class="fas fa-calendar"></i>
                    </a>
                </span>
                
                <span
                    v-if="isGroupable && isActive"
                    tabindex="0"
                    data-bs-toggle="modal"
                    :data-bs-target="'#add-to-studentgroup_' + registration.id"
                    class="btn btn-xs btn-outline-primary"
                    v-tooltip="ucFirst(translate('generic.addtostudentgroup'))"
                >
                    <i class="fas fa-plus"></i>
                    <i class="fas fa-users"></i>
                </span>
            </div>
        </div>

        <!-- # Appointments -->
        <div class="col-2" v-if="isActive">
            <div class="row">
                <div class="col-6">
                    {{ eventsSummary.future }} / {{ eventsSummary['total-occuring']}} ({{ eventsSummary['total-events'] }})
                    <span v-tooltip="{ content: translate('generic.explainnrofevents'), html: true }"><i class="fa fa-info-circle"></i></span>
                </div>
                <div class="col-6">
                    <span
                        v-tooltip="ucFirst(translate('generic.absentwithoutnotification'))"
                        class="badge bg-danger text-white me-1"
                    >
                        {{ registration?.attendance?.absentNoShow }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.absentwithnotification'))"
                        class="badge bg-warning text-dark me-1"
                    >
                        {{ registration?.attendance?.absentWithNotification }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.absentnotificationtoolate'))"
                        class="badge bg-info text-white me-1"
                    >
                        {{ registration?.attendance?.absentWithNotificationTooLate }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.present'))"
                        class="badge bg-success text-white me-1"
                    >
                        {{ registration?.attendance?.present }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.attendance')) + ' ' + translate('generic.unknown')"
                        class="badge bg-secondary text-white me-1"
                    >
                        {{ registration?.attendance?.unknown }}
                    </span>
                </div>
            </div>
        </div>
        <div class="col-2" v-else>&nbsp;</div>

        <div class="col-2">
            <VueDatepicker
                v-model="startDate"
                v-bind="dpOptions"
                @update:model-value="dirty=true"
                required
            />
            <input type="hidden" :value="dbStartDate" :name="'startdate[' + registration.pivot.id + ']'" />
        </div>

        <!-- End date-->
        <div class="col-2">
            <VueDatepicker
                v-model="endDate"
                v-bind="dpOptions"
                @update:model-value="dirty=true;enddateChanged=true"
            />
            <input type="hidden" :value="dbEndDate" :name="'enddate[' + registration.pivot.id + ']'" />
        </div>

        <!-- optional: signed status -->
        <div class="col-2" v-if="isActive && !isTrialCourse && !isNonIteratingCourse">
            <div class="mb-3">
                <template v-if="signStatus === 'open'">
                    <!-- send sign request component -->
                    <send-request
                            :email="studentEmail"
                            :salutation="studentSalutation"
                            :startDate="format(startDate, dpOptions.format)"
                            :courseName="registration.name"
                            :studentName="student.name"
                            :student-id="student.id"
                            :studentFirstName="student.firstname"
                            :popupTitle="ucFirst(translate('generic.sendsignrequest'))"
                            :closeBtnText="ucFirst(translate('generic.close'))"
                            :registrationId="registration.pivot.id"
                            :schoolname="domain.name"
                            @updateRegistration="refreshMyData"
                    ></send-request>
                </template>
                <template v-else>
                    <preview-sign-request
                            :registration="registration"
                            :student="student"
                    />
                    <span v-if="signStatus === 'requested'" class="push-higher">
                        {{ucFirst(translate('generic.signrequestsend'))}}
                    </span>
                    <span v-else class="push-higher">
                        {{ucFirst(translate('generic.signed'))}}
                    </span>
                </template>
            </div>
        </div>
        <div v-else class="col-2">&nbsp;</div>

        <!-- Course name and recurrence -->
        <div class="col-2">
            <a
                v-tooltip="ucFirst(translate('generic.clicktoopencourseeditpage'))"
                :href="`/courses/${registration.id}/edit`"
            >
                {{registration.name}},
                {{ getGroupSizeLabel(registration.group_size_min, registration.group_size_max) }},
                {{registration.recurrenceoption.description}}
            </a>
            <div v-if="dirty" class="alert alert-warning">
                {{ucFirst(translate('generic.pleasesavechanges'))}}
                <div v-if="enddateChanged">
                    {{ ucFirst(translate('generic.futureappointmentswillbedeleted')) }}
                </div>
            </div>
        </div>

        <modal v-if="!isActive"
               :popup-title  = "ucFirst(translate('generic.areyousure'))"
               :closetext    = "ucFirst(translate('generic.close'))"
               :modal-id     = "'delRegistrationPopup' + registration.pivot.id">
            <p>
                {{ucFirst(translate('generic.thiscannotbeundone'))}}
            </p>
            <p>
                {{translate('generic.explainnotthesameasenddate')}}
            </p>
            <template v-slot:okbutton>
              <button type="button"
                    class="btn btn-danger"
                    data-bs-dismiss="modal"
                    @click.prevent="deleteRegistration(registration.pivot.id)">
                {{ ucFirst(translate('generic.deletethisregistration')) }}
              </button>
            </template>
        </modal>
        <modal
            v-if="isGroupable"
            :popup-title = "ucFirst(translate('generic.possiblestudentgroups'))"
            :closetext   = "ucFirst(translate('generic.close'))"
            :modal-id    = "'add-to-studentgroup_' + registration.id"
        >
            <h3>{{ ucFirst(translate('generic.possiblestudentgroups')) }}</h3>
            <ul class="list-group">
                <li
                    v-for="group in potentialGroupsForCourse"
                    :key="group.id"
                    class="list-group-item list-group-item-action point"
                    @click.prevent="addToGroup(group.id)"
                    data-bs-dismiss="modal"
                >
                    {{ group.lastname }}
                </li>
            </ul>
        </modal>
    </div>

</template>

<script setup>
import { computed, onBeforeMount, onMounted, ref, watch, watchEffect } from 'vue';
import Modal from '../Layout/Modal.vue';
import PreviewSignRequest from './PreviewSignRequest.vue';
import SendRequest from './SendRequest.vue';
import usePlanning from "../../composables/usePlanning.js";
import useLang from "../../composables/generic/useLang.js";
import useToast from "../../composables/generic/useToast.js";
import useDatePicker from "../../composables/generic/useDatePicker.js";
import useApi from "../../composables/generic/useApi.js";
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { parse, format } from "date-fns";

const { getSummaryOfRegEventsForSY } = usePlanning();
const { ucFirst, translate, translateChoice } = useLang();
const { failToast, successToast } = useToast();
const { apiGet, apiPost, apiDel } = useApi();

// Get date picker options from the composable
const { dpOptions } = useDatePicker({ value: true }); // Date-only picker

const emit = defineEmits(['registrationDeleted', 'refreshMyData']);

const props = defineProps({
    userisadmin: {
        type: Boolean,
        default: false
    },
    registration: {
        type: Object,
        required: true
    },
    student: {
        type: Object,
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    domain: {
        type: Object,
        required: true
    },
    getRegMetaData: {
        type: Boolean,
        default: true
    }
});

const startDate = props.registration.pivot.start_date
    ? ref(parse(props.registration.pivot.start_date, "yyyy-MM-dd", new Date()))
    : ref(null);
const dbStartDate = ref("");
const endDate = props.registration.pivot.end_date
    ? ref(parse(props.registration.pivot.end_date, "yyyy-MM-dd", new Date()))
    : ref(null);
const dbEndDate = ref("");
const dirty = ref(false);
const enddateChanged = ref(false);
const potentialGroupsForCourse = ref([]);
const eventsSummary = ref([]);

/**
 * set dbDate fields (string) to be the db version of their counterpart (date object) at all times.
 */
watchEffect(() => {
    if (startDate.value) {
        dbStartDate.value = format(startDate.value, "yyyy-MM-dd");
    }
    if (endDate.value) {
        dbEndDate.value = format(endDate.value, "yyyy-MM-dd");
    }
});

onMounted(async () => {
    if (props.getRegMetaData) {
        eventsSummary.value = await getSummaryOfRegEventsForSY(props.registration.pivot.id, props.registration.actualSchoolYear.id);
    }
});

onBeforeMount(() => {
    // don't check (extra request) if it's not going to be in a group anyway
    // - already in a student group or
    // - has a current schedule
    if (!props.registration.tutoringInStudentGroup && props.registration.nrofappointments === 0) {
        getGroupsForCourse(props.registration.course.id);
    }
});

/**
 * get state of checklist, return correct label
 * Options: a registration may have no checklist, an incomplete checklist or only completed checklists
 * @returns {string}
 */
const labelForChecklist = computed(() => {
    if (props.registration.checklists.length === 0) {
        return ucFirst(translate('generic.openregistration')) +
            '. ' + ucFirst(translate('generic.coursehasnochecklist'));
    }
    return props.registration.allchecklistscomplete
        ? ucFirst(translate('generic.openregistration')) +
            '. ' + ucFirst(translate('generic.coursehascompletedchecklist'))
        : ucFirst(translate('generic.openregistration')) +
            '. ' + ucFirst(translate('generic.coursehasuncompletechecklist'));
});

/**
 * get state of checklist, return correct class (color)
 * possible outcome: incomplete, complete or nochecklist
 * @returns {string}
 */
const classForChecklist = computed(() => {
    if (props.registration.checklists.length === 0) return 'nochecklist';
    return props.registration.allchecklistscomplete ? 'complete' : 'incomplete';
});

const isTrialCourse = computed(() => props.registration.is_trial_course === 1);

const isNonIteratingCourse = computed(() => props.registration.recurrenceoption.ends_after_nr_of_occurrences === 1);

/**
 * compress isSigned and isRequested into 1 status field
 * @return string, one of 'signed'|'requested'|'open'
 */
const signStatus = computed(() => {
    return isSigned.value
        ? 'signed'
        : isRequested.value
            ? 'requested'
            : 'open';
});

const isSigned = computed(() => props.registration.pivot.signed === 1);

const isRequested = computed(() => typeof props.registration.pivot.sign_request_send === 'undefined'
    ? false
    : props.registration.pivot.sign_request_send === 1);

/**
 * find email for finance - if not found, the first email
 * @returns {string|*}
 */
const studentEmail = computed(() => {
    const emailContacts = props.student.contacts.filter(contact => contact.contacttype === 'email');
    if (emailContacts.length === 0) return '';
    if (emailContacts.length === 1) {
        return emailContacts[0].value;
    } else {
        const financeContacts = emailContacts.filter(contact => contact.apply_for_finance === 1);
        if (financeContacts.length > 0) {
            return financeContacts[0].value;
        } else {
            return emailContacts[0].value;
        }
    }
});

/**
 * find salutation for finance - if not found, the studentname
 * @returns {string|*}
 */
const studentSalutation = computed(() => {
    let retString = '';
    const emailContacts = props.student.contacts.filter(contact => contact.contacttype === 'email');
    if (emailContacts.length === 0) return props.student.firstname;
    if (emailContacts.length === 1) {
        retString = emailContacts[0].use_salutation;
    } else {
        const financeContacts = emailContacts.filter(contact => contact.apply_for_finance === 1);
        if (financeContacts.length > 0) {
            // if we find more than 1, use the first
            retString = financeContacts[0].use_salutation;
        } else {
            // return the first we did find
            retString = emailContacts[0].use_salutation;
        }
    }
    // if no salutation was filled in on the found entry, use the student's name
    return retString !== '' && retString != null ? retString : props.student.firstname;
});

/**
 * This registration can be taught in a group if:
 * 1 - not already in a student group
 * 2 - not already in a schedule (no current planning events)
 * 3 - course is also being taught in a group
 * 4 - if it's a trial course: does it lead to a course that is being taught in a group?
 * @returns {boolean}
 */
const isGroupable = computed(() => !props.registration.tutoringInStudentGroup &&
    props.registration.nrofappointments === 0 &&
    potentialGroupsForCourse.value.length > 0);

const getGroupSizeLabel = (minSize, maxSize) => {
    if (minSize === 1 && maxSize === 1) {
        return ucFirst(translate('generic.individualcourse'));
    } else if (maxSize === 2) {
        return ucFirst(translate('generic.duocourse'));
    } else {
        return ucFirst(translate('generic.groupcourse'));
    }
};

/**
 * markup for the list of students in the popover (st-group)
 * @returns {string}
 */
const getStudentgroupPopoverContent = () => {
    const studentListArr = props.registration?.studentgroupstudents?.map(std => {
        const flag = std.pivot.as_trial_student > 0
            ? "<i class='fa fa-flag text-info'></i>"
            : '';
        if (std.id === props.student.id) {
            return `<li><strong>${ std.name }</strong> ${ flag }</li>`;
        } else {
            return `<li><a href='/students/${ std.id }/edit'>${ std.name }</a> ${ flag }</li>`;
        }
    });
    const studenList = studentListArr?.join('');
    return '<ul>' + studenList + '</ul>' +
        "<i class='fa fa-flag text-info'></i> = " + ucFirst(translate('generic.triallesson')) +
        "<br><a href='/studentgroups/" + props.registration.studentgroupid + "/edit' " +
        "class='btn btn-sm btn-success'><i class='fas fa-edit me-2'></i>" +
        ucFirst(translate('generic.openstudentgroup')) + "</a>";
};

const deleteRegistration = async (id) => {
    try {
        await apiDel('/api/studentcourseregistration/' + id);
        // tell parent component to remove the registration as well
        emit('registrationDeleted', { regid: id });
        // if this is the last registration: show delete student button
        const courseRows = document.querySelectorAll('.courseRow');
        if (courseRows.length === 0) {
            const deleteBtn = document.getElementById('studentDeleteBtn');
            if (deleteBtn) {
                deleteBtn.style.display = 'block';
            }
        }
    } catch (error) {
        failToast(`Error deleting course registration: ${error}`);
    }
};

const refreshMyData = () => {
    emit('refreshMyData');
};

const getGroupsForCourse = async (courseId) => {
    try {
        const response = await apiGet(`/api/getstudentgroupsforcourse/${courseId}`);
        potentialGroupsForCourse.value = response.data;
    } catch (error) {
        failToast(`Error getting student groups: ${error}`);
    }
};

/**
 * add this student to the chosen group
 * @param groupId
 */
const addToGroup = async (groupId) => {
    const data = {
        stgid: groupId,
        stid: props.student.id,
        associatedCourse: props.registration.id
    };
    try {
        await apiPost('/api/addStudentToGroup', data);
        successToast('Student added to student group');
        refreshMyData();
    } catch (error) {
        failToast(`Error adding student to student group: ${error}`);
    }
};

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});

</script>

<style scoped lang="scss">
@use '../../../sass/tmpl3/variables' as *;
.incomplete {
    color: $classred;
}
.complete {
    color: $classgreen;
    display: inline-block;
}
.nochecklist {
    color: $classbgblue;
}
.separator {
    border-left: sold 1px $classdark;
}

/** Custom extra small button size */
.btn-xs {
    margin-top: 5px;
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.2rem;
}

/** Positioning fields in row */
.mr-05 {
    margin-right: 2.5px;
}
button, a.btn {
    position: relative;
    top: -3px;
}
.middletext {
    position: relative;
    top: -6px;
}
.push-higher {
    position: relative;
    top: -0.3rem;
}
/** END Positioning fields in row */

</style>
