<template>
    <span>
        <button class="btn btn-sm btn-success push-higher"
                @click.prevent="noop"
                data-bs-toggle="modal"
                :data-bs-target="'#' + modalId"
                v-tooltip="myTitle"
        >
            <i class="fas fa-eye"></i>
        </button>
        <modal
            :modal-id     = "modalId"
            :popup-title  = "myTitle"
            :closetext    = "ucFirst(translate('generic.close'))"
            size          = "large"
        >

            <h4>{{ucFirst(translate('generic.student'))}} {{ucFirst(translate('generic.registration'))}}</h4>
            <div class="row">
                <label class="col-2">{{ucFirst(translate('generic.name'))}}</label>
                <span class="col-10">{{student.name}}</span>
            </div>
            <div class="row">
                <label class="col-2">{{ucFirst(translate('generic.address'))}}</label>
                <span class="col-10">{{student.address}}</span>
            </div>
            <div class="row">
                <span class="offset-2 col-10">{{student.zipcode}} {{student.city}}</span>
            </div>
            <div class="row">
                <label class="col-2">{{ucFirst(translate('generic.email'))}}</label>
                <span class="col-10">{{studentEmail}}</span>
            </div>

            <h4>{{ucFirst(translateChoice('generic.courses', 1))}}</h4>
            <div class="row">
                <label class="col-2">{{ucFirst(translate('generic.name'))}}</label>
                <span class="col-10">{{registration.name}} {{registration.course.recurrenceoption.description}}</span>
            </div>
            <div class="row">
                <label class="col-2">{{ucFirst(translate('generic.price'))}}</label>
                <span class="col-10">
                    {{ registrationPrice }} / {{ translate('priceinterval.' + registration.course.price_is_per) }}
                </span>
            </div>
            <div class="row">
                <label class="col-2">{{ucFirst(translate('generic.registrationdate'))}}</label>
                <span class="col-10">{{startDate}}</span>
            </div>

            <div>
                <h4>
                    {{ucFirst(translate('generic.permissionautobanktransfer'))}}:
                    <span v-if="permissionAutoBankTransfer" class="text-secondary">{{ ucFirst(translate('generic.yes')) }}</span>
                    <span v-else class="text-secondary">{{ucFirst(translate('generic.no'))}}</span>
                </h4>
                <template v-if="permissionAutoBankTransfer">
                    <div class="row">
                        <label class="col-3">{{ucFirst(translate('generic.bankaccountname'))}}</label>
                        <span class="col-9">{{student.bankaccount_name}}</span>
                    </div>
                    <div class="row" v-if="permissionAutoBankTransfer">
                        <label class="col-3">{{ucFirst(translate('generic.bankaccountnumber'))}}</label>
                        <span class="col-9">{{student.bankaccount_number}}</span>
                    </div>
                </template>
            </div>

            <h4>{{ucFirst(translate('generic.status'))}}</h4>
            <div class="row">
                <div class="col">
                    {{ signStatus }}
                </div>
            </div>
        </modal>
    </span>
</template>

<script setup>
import { computed } from 'vue';
import Modal from '../Layout/Modal.vue';
import useLang from '../../composables/generic/useLang.js';
import useDatePicker from '../../composables/generic/useDatePicker.js';
import moment from 'moment';
import useUtils from "../../composables/generic/useUtils.js";

const { translate, translateChoice, ucFirst } = useLang();
const { dpOptions: dpOptionsDate, convertVueFormatToMomentFormat } = useDatePicker(true);
const { uniqueId } = useUtils();

const props = defineProps({
    registration: {
        type: Object,
        required: true
    },
    student: {
        type: Object,
        required: true
    },
    title: {
        type: String,
        default: ''
    }
});

// Generate a random ID for the modal
const modalId = '_' + uniqueId();

const permissionAutoBankTransfer = computed(() => {
    return props.student.permission_auto_banktransfer === true ||
        props.student.permission_auto_banktransfer === 1 ||
        props.student.permission_auto_banktransfer === '1' ||
        props.student.permission_auto_banktransfer === 'Ja' ||
        props.student.permission_auto_banktransfer === 'Yes';
});

const signStatus = computed(() => {
    let status = ucFirst(translate('generic.signrequestnotsend'));
    const signed =
        (props.registration.signed != null && props.registration.signed === 1) ||
        (props.registration.pivot != null && props.registration.pivot.signed != null && props.registration.pivot.signed === 1);
    const SignRequestSend =
        (props.registration.sign_request_send != null && props.registration.sign_request_send === 1) ||
        (props.registration.pivot != null && props.registration.pivot.sign_request_send != null && props.registration.pivot.sign_request_send === 1);
    status = SignRequestSend ? ucFirst(translate('generic.notsignedbutrequestsent')) : status;
    status = signed ? ucFirst(translate('generic.signed')) : status;
    return status;
});

const myTitle = computed(() => {
    return props.title === ''
        ? ucFirst(translate('generic.viewsignrequest'))
        : props.title;
});

const studentEmail = computed(() => {
    // find email for finance - if not found, the first email
    const emailContacts = props.student.contacts.filter(contact => contact.contacttype === 'email');
    if (emailContacts.length === 0) return '';
    if (emailContacts.length === 1) {
        return emailContacts[0].value;
    } else {
        const financeContacts = emailContacts.filter(contact => contact.apply_for_finance === 1);
        if (financeContacts.length > 0) {
            // if we find more than 1, use the first
            return financeContacts[0].value;
        } else {
            // return the first we did find
            return emailContacts[0].value;
        }
    }
});

const startDate = computed(() => {
    // in some cases start_date will be at the root level of registration, other cases it's below 'pivot'
    if (props.registration.pivot == null) {
        return moment(props.registration.start_date, 'YYYY-MM-DD').format(convertVueFormatToMomentFormat(dpOptionsDate.value.format));
    } else {
        return moment(props.registration.pivot.start_date, 'YYYY-MM-DD').format(convertVueFormatToMomentFormat(dpOptionsDate.value.format));
    }
});

/**
 * This function calculates the resulting price for the invoice, taking into account:
 * - the student's age (do they need to pay tax or not)
 * - whether the registration has an override for tax and/or course price (incidental)
 *
 * corner case: if the registration has an incidental tax-rate but the student is subadult i.e.
 * doesn't have to pay tax; in this case the incidental tax-rate is ignored
 *
 * @returns string formatted string including currency symbol
 */
const registrationPrice = computed(() => {
    let multiplicationFactor = 1;
    let priceExTax = props.registration.course.price_invoice;

    if (props.student.isAdult) {
        if (props.registration.incidental_tax_rate != null && props.registration.incidental_tax_rate !== '') {
            multiplicationFactor = (props.registration.incidental_tax_rate / 100) + 1;
        }
        if (props.registration.incidental_price_ex_tax != null && props.registration.incidental_price_ex_tax !== '') {
            priceExTax = props.registration.incidental_price_ex_tax;
        }
    } else {
        if (props.registration.incidental_price_ex_tax != null && props.registration.incidental_price_ex_tax !== '') {
            priceExTax = props.registration.incidental_price_ex_tax;
        } else {
            priceExTax = props.registration.course.price_ex_tax_sub_adult;
        }
    }
    const formatter = new Intl.NumberFormat('nl-NL', {
        style: 'currency',
        currency: 'EUR'
    });

    const appliedTaxRateForAdult =
        (props.registration.incidental_tax_rate != null && props.registration.incidental_tax_rate !== '')
            ? props.registration.incidental_tax_rate
            : props.registration.tax_rate;
    const result = formatter.format(priceExTax * multiplicationFactor);

    return props.student.isAdult
        ? `${result} incl. ${appliedTaxRateForAdult}% ${translate('generic.tax')}`
        : result;
});

// Expose noop function for template
const noop = () => {};
</script>

<style scoped>
.push-higher {
    position: relative;
    top: -0.3rem;
}
</style>
