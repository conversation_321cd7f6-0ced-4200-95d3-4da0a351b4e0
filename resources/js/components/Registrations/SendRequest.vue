<template>
    <div>
        <button class="btn btn-sm btn-primary mb-2"
                @click.prevent="noop"
                data-bs-toggle="modal"
                :data-bs-target="'#_' + registrationId">
            {{ buttonText }}
        </button>
        <modal :popup-title="popupTitle" :closetext="closeBtnText" size="extralarge"
                   :modalId="'_' + registrationId">

            <div class="row">
                <div class="col-3"><label>{{ ucFirst(translate('generic.courseregistrationfor')) }}:</label></div>
                <div class="col-9">
                    <span id="srCoursename">{{ courseName }}</span>
                </div>
            </div>
            <div class="row">
                <div class="col-3"><label>{{ ucFirst(translate('generic.startdate')) }}:</label></div>
                <div class="col-9"><span id="srStartdate">{{ startDate }}</span></div>
            </div>
            <div class="row">
                <div class="col-3"><label>{{ ucFirst(translate('generic.student')) }}:</label></div>
                <div class="col-9"><span>{{ studentName }}</span></div>
            </div>

            <hr/>
            <div class="row">
                <div class="col-3">
                    <label for="targetemail">
                        {{ ucFirst(translate('generic.sendrequesttoemail')) }}:
                    </label>
                </div>
                <div class="col-9">
                    <input name="targetemail" id="targetemail" class="form-control" v-model="targetEmail">
                </div>
            </div>
            <hr/>
            <div class="row">
                <div class="col-3">
                    {{ ucFirst(translate('generic.templates')) }}<br>
                    <em class="text-success">1: {{ ucFirst(translate('generic.pleasechoosea')) }}
                        {{ translate('generic.template') }}</em>
                </div>
                <div class="col-9">
                    <list-templates
                        for-target="b"
                        @templateChosen="copyTemplate"
                    ></list-templates>
                </div>
            </div>
            <hr v-if="showRawTemplate"/>
            <!-- default text -->
            <div v-if="showRawTemplate" class="row">
                <div class="col-9 offset-3">
                    <div ref="defaultText" class="panel panel-danger raw-template-text" id="defaultText"/>
                </div>
            </div>
            <hr/>
            <div class="row mb-2">
                <div class="col-9 offset-3 text-center">
                    <em class="text-success">{{ ucFirst(translate('generic.pleasemakecorrections')) }}</em>
                </div>
            </div>
            <div class="row">
                <div class="col-3">
                    <em class="text-success mr-1">2:</em>
                    <button :disabled="replacementText.length > 0 || !showRawTemplate"
                            class="btn btn-primary btn-sm"
                            @click.prevent="copyDefaultText">
                        {{ translate("generic.copy") }} {{ translate("generic.templatetext") }}
                    </button>
                    <div class="mt-3">
                        <em class="text-success">
                            {{ translate('generic.orjusttypetekst') }}
                            <i class="fa fa-arrow-right"></i>
                        </em>
                    </div>
                </div>
                <div class="col-9">
                    <ckeditor
                        v-if="ClassicEditor && editorConfigSimple"
                        v-model="replacementText"
                        :editor="ClassicEditor"
                        :config="editorConfigSimple"
                    />
                </div>
            </div>

            <input type="hidden" id="regid" :value="registrationId">
            <template v-slot:okbutton>
                <button
                    type="button"
                    class="btn btn-primary"
                    @click.prevent="sendSignRequest"
                    data-bs-dismiss="modal"
                    :disabled="replacementText.length < 25"
                >
                    {{ translate('generic.sendsignrequest') }}
                </button>
            </template>
        </modal>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import Modal from '../Layout/Modal.vue';
import ListTemplates from '../Email/ListTemplates.vue';
import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ClassicEditor } from 'ckeditor5';
import 'ckeditor5/ckeditor5.css';
import useConfigItems from '../../composables/generic/useConfigItems.js';
import useApi from '../../composables/generic/useApi.js';
import useLang from "../../composables/generic/useLang.js";
import useUtils from "../../composables/generic/useUtils.js";
import useToast from "../../composables/generic/useToast.js";

const { translate, ucFirst } = useLang();
const { uniqueId } = useUtils();
const { apiGet, apiPost } = useApi();
const { successToast, failToast } = useToast();
const { editorConfigSimple, isLayoutReady } = useConfigItems();

const props = defineProps({
    email: String,
    salutation: String,
    startDate: {
        type: String,
        default: () => '2017-02-03'
    },
    courseName: String,
    studentName: String,
    studentId: Number,
    studentFirstName: { 
        type: String, 
        required: true 
    },
    popupTitle: String,
    closeBtnText: String,
    openButtonText: { 
        type: String, 
        default: '' 
    },
    registrationId: { 
        type: Number, 
        required: true 
    },
    schoolname: { 
        type: String, 
        required: true 
    }
});

const emit = defineEmits(['updateRegistration']);

// Reactive data
const targetEmail = ref('');
const replacementText = ref('');
const showRawTemplate = ref(false);
const templateVariables = ref([]);
const defaultText = ref(null);

// Constants
const MAX_IMG_SIZE = 500;
const DEFAULT_IMG_SIZE = 100;

const getTemplateVariables = async () => {
    try {
        const response = await apiGet(`/api/templateVariables/${props.registrationId}`);
        templateVariables.value = response.data;
    } catch (error) {
        failToast(
            translate('generic.couldnotgettemplatevariables') + ': ' + error,
            translate('generic.error')
        );
    }
};

const sendSignRequest = async () => {
    const data = {
        regid: props.registrationId,
        targetemail: targetEmail.value,
        replacementtext: finalText.value
    };

    try {
        const response = await apiPost('/api/registration/requestsignature', data);
        if (response.data.success != null) {
            // ask parent to update this registrations status
            emit('updateRegistration', { registrationId: props.registrationId });
            successToast(translate('generic.emailsend'), translate('generic.success'));
        } else {
            // notify user that mail send has failed
            failToast(translate('generic.sendingemailfailed'), translate('generic.error'));
        }
    } catch (error) {
        failToast(translate('generic.sendingemailfailed') + ': ' + error, translate('generic.error'));
    }
};

/**
 * get template content and paste in "default text" field
 * ready to be copied into rich text editor
 * @param e
 */
const copyTemplate = async (e) => {
    if (e.selectedTemplateId > 0) {
        try {
            const response = await apiGet(`/api/mailTemplateContent/${e.selectedTemplateId}`);
            showRawTemplate.value = response.data.content.length > 0;
            await nextTick();
            defaultText.value.innerHTML = response.data.content;
        } catch (error) {
            failToast(
                ucFirst(translate('generic.retrievingtemplatefailed')) + ': ' + error,
                translate('generic.error')
            );
        }
    } else {
        defaultText.value.innerHTML = '';
        showRawTemplate.value = false;
    }
};

/**
 * copy the template text, replace the variables with their data counterpart
 * and paste it in the editor
 */
const copyDefaultText = async () => {
    replacementText.value = defaultText.value.innerHTML;
    replaceVariablesInTemplateText();
    await nextTick();
    showRawTemplate.value = false;
};

/**
 * find variables in template text and replace them by the actual data
 */
const replaceVariablesInTemplateText = () => {
    // find variables in template text
    const variables = [];
    const regex = /%[a-z0-9|'=]+%/gm;
    let m;
    while ((m = regex.exec(replacementText.value)) !== null) {
        m.forEach((match, groupIndex) => {
            variables.push(match);
        });
    }
    const distinctVariables = [...new Set(variables)];
    // the variables include the delimiter %....%
    let newText = replacementText.value;
    distinctVariables.forEach(variable => {
        if (templateVariables.value.variables.includes(variable)) {
            // separate exceptions from streamline
            // if a variable has parameters (schoollogo|width=150) the switch will fail,
            // so we use the first part of the variable to switch upon, it should be unique and constant
            const parts = variable.split('|');
            const switcher = parts.length > 1 ? parts[0] + '%' : parts[0];
            let imgWidth = DEFAULT_IMG_SIZE; 
            let widthParamIndex; 
            let imageLink;
            switch (switcher) {
            case '%classyactivationlink%':
                // not implemented yet
                newText = newText.replaceAll(
                    variable,
                    ucFirst(translate('generic.notavailableyet')) + `: ${variable}`
                );
                break;
            case '%schoollogo%':
                // valid parameter: width (not mandatory) - ignore other
                // not found will return -1
                widthParamIndex = parts.findIndex((elm) => elm.substr(0, 5) === 'width');
                if (widthParamIndex !== -1) {
                    const paramWidth = parts[widthParamIndex].substr(0, -1);
                    const parameterParts = paramWidth.split('=');
                    if (
                        parameterParts.length > 1 &&
                            Number.isInteger(parameterParts[1].trim()) &&
                            parseInt(parameterParts[1].trim()) <= MAX_IMG_SIZE
                    ) {
                        imgWidth = parameterParts[1].trim();
                    }
                }
                imageLink = "<a href='#'><img src='" +
                        templateVariables.value.data[variable] +
                        "' width='" + imgWidth + "' alt='school logo'/></a>";
                newText = newText.replaceAll(variable, imageLink);
                break;
            default:
                newText = newText.replaceAll(variable, templateVariables.value.data[variable]);
                break;
            }
        } else {
            // notify the user if we couldn't translate a variable
            failToast(
                ucFirst(
                    translate('generic.couldntreplacevariableintemplate',
                        { theVariable: variable })
                ),
                translate('generic.error')
            );
        }
    });
    replacementText.value = newText;
};

const modalId = computed(() => {
    return '_' + uniqueId();
});

const buttonText = computed(() => {
    return props.openButtonText === ''
        ? ucFirst(translate('generic.requestsignatureemail'))
        : props.openButtonText;
});

const finalText = computed(() => {
    return replacementText.value === '' ? defaultText.value?.innerHTML || '' : replacementText.value;
});

onMounted(async () => {
    targetEmail.value = props.email;
    await getTemplateVariables();
    isLayoutReady.value = true;
});

// Expose noop function for template
const noop = () => {};
</script>

<style scoped>
#defaultText {
    border: solid 1px black;
    color: #6A6E8F;
}

.raw-template-text {
    padding: .5em;
    min-height: 3rem;
}

/* The link button popup (showing input fields) displays behind the modal */
/* So we only need this if ckeditor is embedded in a modal */
:root {
    --ck-z-panel: calc(var(--ck-z-default, 100) + 9999);
}
</style>
