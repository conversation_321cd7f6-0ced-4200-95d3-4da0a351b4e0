<template>
    <panel :busy="busy">
        <template #title>
            <font-awesome-icon icon="edit" />
            {{ ucFirst(translate('generic.overviewregistrationschecklists')) }}
        </template>
        <template #subtitle>
            <button class="btn btn-sm btn-success" @click="switchSortField">
                <font-awesome-icon icon="sort" />
                {{ sortByLabel }}
            </button>
        </template>
        <table class="table table-striped table-sm">
            <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.name')) }}</th>
                <th>{{ ucFirst(translate('generic.checklist')) }}</th>
                <th>{{ ucFirst(translate('generic.status')) }}</th>
            </tr>
            </thead>
            <tbody v-for="registration in regsNotCompleted" :key="registration.checklistid">
                <tr>
                    <td>
                        <a :href="`/students/${registration.student_id}/edit`">
                            {{ registration.name }} {{ registration.city }}
                            ({{ registration.course }})
                        </a>
                    </td>
                    <td>{{ registration.checklistname }}</td>
                    <td>
                        <a class="btn btn-danger no_underline no-margin-bottom"
                           :href="`/registrationedit/${registration.id}`">
                            <font-awesome-icon icon="check" />
                            {{ ucFirst(translate('generic.checklistincomplete')) }}
                        </a>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <span class="text-sm"><strong>TODO:</strong>&nbsp;{{ registration.incompleteItems.join(', ') }}</span>
                    </td>
                </tr>
            </tbody>
            <tbody>
            <tr v-for="registration in regsCompleted" :key="registration.checklistid">
                <td>
                    <a :href="`/students/${registration.student_id}/edit`">
                        {{ registration.name }} {{ registration.city }}
                    </a>
                </td>
                <td>{{ registration.checklistname }}</td>
                <td>
                    <div class="alert alert-success alert-small">
                        <font-awesome-icon icon="check" />
                        {{ ucFirst(translate('generic.checklistcompleted')) }}
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </panel>
</template>

<script setup>
import Panel from '../Layout/Panel.vue';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import useLang from '../../composables/generic/useLang.js';
import useChecklists from '../../composables/useChecklists.js';
import { onMounted } from 'vue';

const { ucFirst, translate } = useLang();
const { 
    busy, 
    getData, 
    regsCompleted, 
    regsNotCompleted, 
    sortByLabel, 
    switchSortField 
} = useChecklists();

onMounted(() => {
    getData();
});
</script>

<style scoped>
.text-sm {
    font-size: smaller;
}
</style>
