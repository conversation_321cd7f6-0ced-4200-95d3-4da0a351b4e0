<template>
    <panel :busy="busy">
        <template #title>
            <i class="fa fa-edit"></i>
            {{ ucFirst(translate('generic.registrationdata')) }}
        </template>
        <template v-if="registration">
            <table class="table">
                <tbody>
                <tr>
                    <th>
                        {{ ucFirst(translate('generic.name')) }}
                    </th>
                    <td colspan="3">
                        <a :href="'/students/' + registration.student.id + '/edit'">
                            {{ registration.student.name }} ({{ registration.student.age }})
                        </a>
                    </td>
                </tr>

                <tr>
                    <th>{{ ucFirst(translate('generic.course')) }}</th>
                    <td colspan="3">
                        <a :href="'/courses/' + registration.course.id + '/edit'">
                            <span v-if="registration.course.is_trial_course">{{ ucFirst(translate('generic.triallesson')) }}:</span>
                            {{ registration.course.name }} - {{ registration.course.recurrenceoption.description }}
                        </a>
                    </td>
                </tr>
                <tr>
                    <th :class="{'text-muted': overridePrice}">
                        {{ ucFirst(translate('generic.priceExTax')) }}
                    </th>
                    <td :class="{'text-muted': overridePrice}">
                        {{ priceExTax }} {{ translate('generic.per') }} {{ translate('priceinterval.' + registration.course.price_is_per) }}
                    </td>
                    <td colspan="2">
                        <div class="row">
                            <div class="col-3 pt-1">
                                <div class="form-check form-switch">
                                    <input
                                        id="override-price"
                                        class="form-check-input"
                                        type="checkbox"
                                        v-model="overridePrice"
                                    >
                                    <label class="form-check-label" for="override-price"></label>
                                </div>
                            </div>
                            <div class="col-9">{{translate('generic.overrideprice')}}</div>
                        </div>
                    </td>
                </tr>
                <tr v-if="overridePrice">
                    <th>{{ ucFirst(translate('generic.priceExTax')) }}</th>
                    <td>
                        <input
                            class="form-control"
                            type="text"
                            v-model="registration.incidental_price_ex_tax"
                        >
                    </td>
                    <td>
                        {{ translate('generic.per') }} {{ translate('priceinterval.' + registration.course.price_is_per) }}
                    </td>
                    <td>
                        <button
                            class="btn btn-success"
                            :disabled="incidentalPriceInvalid"
                            @click="saveIncidentalPrice"
                        >{{ translate('generic.save') }}</button>
                    </td>
                </tr>
                <tr>
                    <th :class="{'text-muted': overrideTaxRate}">
                        {{ ucFirst(translate('generic.taxrate')) }}
                    </th>
                    <td v-if="registration.student.isAdult" :class="{'text-muted': overrideTaxRate}">
                        {{ registration.domain.course_tax_rate }}% ({{ translate('generic.adult') }})
                    </td>
                    <td v-else :class="{'text-muted': overrideTaxRate}">
                        0% ({{ translate('generic.not') }} {{ translate('generic.adult') }})
                    </td>
                    <td colspan="2">
                        <div class="row">
                            <div class="col-3 pt-1">
                                <div class="form-check form-switch">
                                    <input
                                        id="override-tax-rate"
                                        class="form-check-input"
                                        type="checkbox"
                                        v-model="overrideTaxRate"
                                    >
                                    <label class="form-check-label" for="override-tax-rate"></label>
                                </div>
                            </div>
                            <div class="col-9">{{translate('generic.overridetaxrate')}}</div>
                        </div>
                    </td>
                </tr>
                <tr v-if="overrideTaxRate">
                    <th>
                        {{ ucFirst(translate('generic.taxrate')) }}
                    </th>
                    <td>
                        <input
                            class="form-control"
                            type="number" step="0.1"
                            v-model="registration.incidental_tax_rate"
                        >
                    </td>
                    <td>&nbsp;</td>
                    <td>
                        <button
                            class="btn btn-success"
                            :disabled="incidentalPriceInvalid"
                            @click="saveIncidentalTaxRate"
                        >{{ translate('generic.save') }}</button>
                    </td>
                </tr>
                <tr>
                    <th>{{ ucFirst(translate('generic.registrationdate')) }}</th>
                    <td colspan="3">{{ startDate }}</td>
                </tr>
                <tr v-if="!endDateUnsetOrFuture">
                    <th>{{ ucFirst(translate('generic.unregisterdate')) }}</th>
                    <td colspan="3">{{ endDate }}</td>
                </tr>
                <tr>
                    <th>{{ ucFirst(translate('generic.signstatus')) }}</th>
                    <td colspan="3">
                        <div v-if="registration.sign_request_send !== 1 && registration.signed !== 1"
                             class="row"
                        >
                            <div class="col-6">
                                {{ ucFirst(translate('generic.notsignednorequestsent')) }}
                            </div>
                            <div class="col-6">
                                <send-request
                                    v-if="registration.signed !== 1"
                                    :email="registration.finEmail"
                                    :salutation="registration.finSalutation"
                                    :start-date="startDate"
                                    :course-name="registration.course.name"
                                    :student-name="registration.student.name"
                                    :student-first-name="registration.student.firstname"
                                    :close-btn-text="ucFirst(translate('generic.close'))"
                                    :popup-title="ucFirst(translate('generic.sendsignrequest'))"
                                    :registration-id="registration.id"
                                    :schoolname="registration.domain.name"
                                    @updateRegistration="getRegistration"
                                />
                            </div>
                        </div>

                        <div v-else-if="registration.sign_request_send === 1 && registration.signed === null"
                             class="row"
                        >
                            <div class="col-4">
                                {{ ucFirst(translate('generic.notsignedbutrequestsent')) }}
                            </div>
                            <div class="col-1 pt-1">
                                <preview-sign-request
                                    :registration="registration"
                                    :student="registration.student"
                                />
                            </div>
                            <div class="col-7">
                                <send-request
                                    v-if="registration.signed !== 1"
                                    :email="registration.finEmail"
                                    :salutation="registration.finSalutation"
                                    :start-date="startDate"
                                    :course-name="registration.course.name"
                                    :student-name="registration.student.name"
                                    :student-first-name="registration.student.firstname"
                                    :close-btn-text="ucFirst(translate('generic.close'))"
                                    :popup-title="ucFirst(translate('generic.sendsignrequest'))"
                                    :open-button-text="ucFirst(translate('generic.resendsignrequest'))"
                                    :registration-id="registration.id"
                                    :schoolname="registration.domain.name"
                                />
                                <label class="signRequestLabelAfter alert alert-success"
                                       :data-regid="registration.id">
                                    {{ translate('generic.signrequestsend') }}
                                </label>
                            </div>
                        </div>

                        <div v-else class="row">
                            <div class="col-1 pt-1">
                                <preview-sign-request
                                    :registration="registration"
                                    :student="registration.student"
                                />
                            </div>
                            <div class="col-11">
                                {{ ucFirst(translate('generic.signed')) }}
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </template>
    </panel>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import SendRequest from './SendRequest.vue';
import PreviewSignRequest from './PreviewSignRequest.vue';
import useLang from '../../composables/generic/useLang.js';
import useApi from '../../composables/generic/useApi.js';
import useToast from '../../composables/generic/useToast.js';
import useDatePicker from '../../composables/generic/useDatePicker.js';
import moment from 'moment';

const { translate, ucFirst } = useLang();
const { apiGet, apiPut } = useApi();
const { successToast, failToast } = useToast();
const { dpOptions: dpOptionsDate, convertVueFormatToMomentFormat } = useDatePicker(true);

const props = defineProps({
    registrationId: {
        type: Number,
        required: true
    }
});

const registration = ref(null);
const busy = ref(false);
const overridePrice = ref(false);
const overrideTaxRate = ref(false);

const getRegistration = async () => {
    busy.value = true;
    try {
        const response = await apiGet(`/api/registration/${props.registrationId}`);
        registration.value = response.data.data;
        overridePrice.value = registration.value.incidental_price_ex_tax != null &&
            registration.value.incidental_price_ex_tax !== '';
        overrideTaxRate.value = registration.value.incidental_tax_rate != null &&
            registration.value.incidental_tax_rate !== '';
    } catch (error) {
        failToast(
            `${ucFirst(translate('generic.failedtogetdataforregistration'))} ${props.registrationId}: ${error}`,
            'error'
        );
    } finally {
        busy.value = false;
    }
};

const saveIncidentalPrice = async () => {
    const data = {
        regId: props.registrationId,
        price: registration.value.incidental_price_ex_tax
    };
    try {
        await apiPut('/api/price_override', data);
        successToast(ucFirst(translate('generic.pricesavedsuccessfull')));
        if (registration.value.incidental_price_ex_tax == null || registration.value.incidental_price_ex_tax === '') {
            overridePrice.value = false;
        }
    } catch (error) {
        failToast(ucFirst(translate('generic.couldnotsavenewprice')) + ': ' + error);
    }
};

const saveIncidentalTaxRate = async () => {
    const data = {
        regId: props.registrationId,
        taxrate: registration.value.incidental_tax_rate
    };
    try {
        await apiPut('/api/tax_rate_override', data);
        successToast(ucFirst(translate('generic.taxratesavedsuccessfull')));
        if (registration.value.incidental_tax_rate == null || registration.value.incidental_tax_rate === '') {
            overrideTaxRate.value = false;
        }
    } catch (error) {
        failToast(ucFirst(translate('generic.couldnotsavenewtaxrate')) + ': ' + error);
    }
};

const incidentalPriceInvalid = computed(() => {
    const regex = /^\d{1,6}((,|.)\d{1,2})?$/gm;
    return registration.value?.incidental_price_ex_tax != null &&
        registration.value?.incidental_price_ex_tax !== '' &&
        !registration.value?.incidental_price_ex_tax.match(regex);
});

const priceExTax = computed(() => {
    const price = registration.value?.student.isAdult
        ? registration.value?.course.price_ex_tax
        : registration.value?.course.price_ex_tax_sub_adult;
    const formatter = new Intl.NumberFormat('nl-NL', {
        style: 'currency',
        currency: 'EUR'
    });
    return formatter.format(price);
});

const startDate = computed(() => {
    return moment(registration.value?.start_date, 'YYYY-MM-DD').format(convertVueFormatToMomentFormat(dpOptionsDate.value.format));
});

const endDate = computed(() => {
    return moment(registration.value?.end_date, 'YYYY-MM-DD').format(convertVueFormatToMomentFormat(dpOptionsDate.value.format));
});

const endDateUnsetOrFuture = computed(() => {
    if (registration.value?.end_date !== null && registration.value?.end_date !== '') {
        const now = moment();
        const diff = now.diff(moment(registration.value.end_date, 'YYYY-MM-DD'), 'minute');
        return (diff < 0);
    }
    return true;
});

watch(overridePrice, async (newValue) => {
    if (!newValue && 
        registration.value?.incidental_price_ex_tax != null && 
        registration.value?.incidental_price_ex_tax !== '') {
        try {
            await apiPut('/api/reset_incidental_price', { regId: props.registrationId });
            successToast(ucFirst(translate('generic.priceresetsuccessfull')));
        } catch (error) {
            failToast(ucFirst(translate('generic.couldnotresetprice')) + ': ' + error);
        }
    }
});

watch(overrideTaxRate, async (newValue) => {
    if (!newValue && 
        registration.value?.incidental_tax_rate != null && 
        registration.value?.incidental_tax_rate !== '') {
        try {
            await apiPut('/api/reset_incidental_tax_rate', { regId: props.registrationId });
            successToast(ucFirst(translate('generic.taxrateresetsuccessfull')));
        } catch (error) {
            failToast(ucFirst(translate('generic.couldnotresettaxrate')) + ': ' + error);
        }
    }
});

onMounted(() => {
    getRegistration();
});
</script>

<style scoped>
.signRequestLabelAfter {
    display: none;
}

label {
    font-weight: bolder;
}

.form-check-input {
    cursor: pointer;
}

.form-switch .form-check-input {
    width: 2.5em;
    height: 1.25em;
}
</style>
