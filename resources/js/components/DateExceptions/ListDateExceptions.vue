<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-edit"></i>
            {{ ucFirst(translateChoice('generic.dateexceptions', 2)) }}
            <small class="text-muted ms-2">({{ filteredDateExceptions ? filteredDateExceptions.length : 0 }})</small>
        </template>
        <template v-slot:subtitle>
            <div class="input-group">
                <div class="d-flex align-items-center">
                    <div class="me-2" v-html="ucFirst(translate('generic.showpastexceptions'))" />
                    <div class="form-check form-switch me-2">
                        <input
                            class="form-check-input"
                            type="checkbox"
                            role="switch"
                            id="showPastExceptionsSwitch"
                            v-model="showPastExceptions"
                        >
                        <label class="form-check-label" for="showPastExceptionsSwitch">
                            {{ showPastExceptionsLabel }}
                        </label>
                    </div>
                </div>

                <select class="form-select me-2" v-model="chosenYearId">
                    <option
                        v-for="schoolyear in allSchoolYears"
                        :key="schoolyear.id"
                        :value="schoolyear.id"
                    >
                        {{ schoolyear.label }}
                    </option>
                </select>
                <select class="form-select me-2" v-model="tutorId">
                    <option :value="0">{{ ucFirst(translate('generic.showall')) }}</option>
                    <option :value="-1">{{ ucFirst(translate('generic.wholeschool')) }}</option>
                    <option v-for="tutor in allTutors" :key="tutor.id" :value="tutor.id">{{ tutor.name }}</option>
                </select>
                <button
                    class="btn btn-success btn-sm"
                    @click="dateExceptionIdToEdit = 0"
                    data-bs-toggle="modal"
                    data-bs-target="#editdateexception"
                    data-testid="new-date-exception-button"
                >
                    {{ ucFirst(translate('generic.new')) }}
                </button>
            </div>
        </template>
        <table class="table">
            <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.functions')) }}</th>
                <th>{{ ucFirst(translate('generic.reason')) }}</th>
                <th>{{ ucFirst(translate('generic.concerns')) }}</th>
                <th>{{ ucFirst(translate('generic.from')) }}</th>
                <th>{{ ucFirst(translate('generic.untilincluding')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.roomlocation')) }}</th>
                <th>
                    {{ ucFirst(translate('generic.color')) }}
                    <span v-tooltip="{ content: translate('generic.explaincalendarcolor'), html: true }">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
                <th>
                    {{ ucFirst(translate('generic.detailurl')) }}
                    <span v-tooltip="{ content: translate('generic.explaindetailurl'), html: true }">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
                <th class="text-center">
                    {{ ucFirst(translateChoice('generic.alerts', 2)) }}
                    <span v-tooltip="{ content: translate('generic.explainignoreforalerts'), html: true }">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
                <th class="text-center">
                    {{ ucFirst(translateChoice('generic.conflicts', 2)) }}
                    <span v-tooltip="{content: translate('generic.explainConflictsDEEvents'), html: true }">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="dateException in filteredDateExceptions" :key="dateException.id">
                <td>
                    <button
                        class="btn btn-success btn-sm"
                        @click="dateExceptionIdToEdit = dateException.id"
                        data-bs-toggle="modal"
                        data-bs-target="#editdateexception"
                    >
                        <i class="fa fa-edit"></i>
                    </button>
                    <button
                        class="btn btn-danger btn-sm"
                        @click="dateExceptionToDelete = dateException.id"
                        data-bs-toggle="modal"
                        data-bs-target="#del_areyousure"
                    >
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td>{{ dateException.reason }}</td>
                <td>
                    <template v-if="dateException.tutors?.length !== 0">
                        <span v-for="tutor in dateException.tutors" class="me-1" :key="tutor.id">
                            <span v-if="tutor.pivot.confirmed" v-tooltip="translate('generic.confirmed')">
                                <i class="fa fa-check-circle text-success"></i>
                            </span>
                            <span v-else v-tooltip="translate('generic.notconfirmed')">
                                <i class="fa fa-question-circle text-secondary"></i>
                            </span>
                            {{ tutor.name }}
                        </span>
                    </template>
                    <span v-else>{{ ucFirst(translate('generic.wholeschool')) }}</span>
                </td>
                <td>
                    {{
                        dateException.isWholeDay
                            ? displayDate(dateException.datetime_start)
                            : displayDateTime(dateException.datetime_start)
                    }}
                </td>
                <td>
                    {{
                        dateException.isWholeDay
                            ? displayDate(dateException.datetime_end)
                            : displayDateTime(dateException.datetime_end)
                    }}
                </td>
                <td class="text-center">
                    {{ dateException.location ? dateException.location.name : "-" }}
                </td>
                <td>
                    <span
                        v-if="!dateException.plan_blocking"
                        class="badge p-2"
                        :style="{ backgroundColor: dateException.calendar_color }"
                    >Cal</span>
                    <span v-else>-</span>
                </td>
                <td>
                    <span v-if="dateException.detail_url && !dateException.plan_blocking">
                        <a :href="dateException.detail_url" target="_blank">
                            <i class="fa fa-external-link-alt"></i>
                        </a>
                    </span>
                    <span v-else>-</span>
                </td>
                <td v-if="dateException.exclude_from_alerts" class="text-center">
                    <strong class="text-danger">{{ translate('generic.no') }}</strong>
                </td>
                <td v-else class="text-center">
                    {{ translate('generic.yes') }}
                </td>
                <td class="text-center">
                    <span
                        class="badge p-2"
                        style="cursor: pointer; color:white; position: relative; z-index: 1041"
                        :style="{ backgroundColor: showConflictsForDateException(dateException.id).code }"
                        v-html="showConflictsForDateException(dateException.id).result"
                        @click="(e) => handleConflictClick(lessonConflicts, dateException.id, e)"
                    />
                </td>
            </tr>
            </tbody>
        </table>

        <!-- confirm delete course -->
        <are-you-sure
            :button-text="ucFirst(translate('generic.deletedateexception'))"
            @confirmclicked="delDateException"
            modal-id="del_areyousure"
        ></are-you-sure>

        <edit-date-exception
            modal-id="editdateexception"
            :date-exception-id="dateExceptionIdToEdit"
            :schoolyear-id="chosenYearId"
            @triggerUpdateDEList="getDateExceptionsForList"
        ></edit-date-exception>

        <conflicts-side-panel
            :is-open="isSidePanelOpen"
            :conflicts="selectedConflicts"
            :dateException="selectedDateException"
            @close="closeSidePanel"
        ></conflicts-side-panel>
    </panel>
</template>

<script setup>
import { computed, nextTick, onMounted, ref, watchEffect } from 'vue';
import Panel from '../Layout/Panel.vue';
import AreYouSure from '../Layout/AreYouSure.vue';
import EditDateException from './EditDateException.vue';
import ConflictsSidePanel from './ConflictsSidePanel.vue';
import useBaseData from '../../composables/generic/useBaseData.js';
import useToast from '../../composables/generic/useToast.js';
import useLang from '../../composables/generic/useLang.js';
import useDateTime from '../../composables/generic/useDateTime.js';
import useDateExceptions from "../../composables/useDateExceptions.js";
import useConflictsSidePanel from "../../composables/useConflictsSidePanel.js";

const { allTutors, allSchoolYears, initBaseData } = useBaseData();
const {
    chosenYearId,
    dateExceptionIdToEdit,
    dateExceptionToDelete,
    getDateExceptionsForList,
    deleteDateException,
    filteredDateExceptions,
    lessonConflicts,
    showConflictsForDateException,
    showPastExceptions,
    tutorId,
} = useDateExceptions();

const {
    handleConflictClick,
    isSidePanelOpen,
    selectedConflicts,
    selectedDateException,
    closeSidePanel
} = useConflictsSidePanel();

const { failToast, successToast } = useToast();
const { displayDate, displayDateTime } = useDateTime();
const { ucFirst, translate, translateChoice } = useLang();

const busy = ref(false);

// isAdmin is deprecated,
// left for callers to remove, but it is being ignored
defineProps({
    isAdmin: Boolean
});

onMounted(async () => {
    await updateData();
    // initial schoolyear id = the first one in the list
    chosenYearId.value = allSchoolYears.value.length > 0 ? allSchoolYears.value[0].id : 0;
    // await getDateExceptionsForList(); // this wil go automatically after chosenYearId is set because of the watch
    // await getLessonConflicts();
    // check if we should open a date exception for edit
    // => url contains valid date exception id, if link is clicked from calendar
    const params = (new URL(document.location)).searchParams;
    const deid = params.get('deid');
    if (deid != null) {
        // open date exception for edit
        dateExceptionIdToEdit.value = parseInt(deid);
        // Bootstrap 5 modal trigger
        const modal = new bootstrap.Modal(document.getElementById('editdateexception'));
        modal.show();
    }
});

const delDateException = async () => {
    try {
        await deleteDateException(dateExceptionToDelete.value);
        await updateData();
        successToast(
            ucFirst(translate('generic.deletesuccessful')),
            ucFirst(translate('generic.success'))
        );
        dateExceptionToDelete.value = 0;
    } catch (error) {
        failToast(
            error.message,
            ucFirst(translate('generic.error'))
        );
    }
};

// Watch for changes in any of the filter values and update data accordingly
watchEffect(async () => {
    // This will run whenever showPastExceptions, chosenYearId, or tutorId changes
    showPastExceptions.value;
    chosenYearId.value;
    tutorId.value;

    await getDateExceptionsForList();
});

const updateData = async () => {
    await initBaseData({
        locations: true,
        tutors: true,
        dateExceptions: true,
        schoolYears: true
    }, true);
};

const showPastExceptionsLabel = computed(() => {
    return showPastExceptions.value
        ? ucFirst(translate('generic.yes'))
        : ucFirst(translate('generic.no'));
});
</script>
