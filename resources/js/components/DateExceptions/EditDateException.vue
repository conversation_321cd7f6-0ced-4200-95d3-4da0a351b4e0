<template>
    <modal :popup-title="popupTitle"
           :closetext="ucFirst(translate('generic.cancel'))"
           :modal-id="modalId"
           size="large"
    >
        <!-- ROW 1-->
        <div class="form-group row">
            <label for="reason" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.subject')) }}</label>
            <div class="col-sm-4">
                <input class="form-control" id="reason" type="text" data-testid="reason-input"
                       v-model="dateException.reason">
            </div>
            <label for="allDay" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.allday')) }}?</label>
            <div class="col-sm-4 mt-2">
                <div class="form-check form-switch">
                    <input
                        id="allDay"
                        class="form-check-input"
                        type="checkbox"
                        v-model="dateException.isWholeDay"
                    >
                    <label class="form-check-label" for="allDay">
                        {{ isAllDayLabel }}
                    </label>
                </div>
            </div>
        </div>
        <!-- ROW 2 -->
        <div :class="[
                'form-group',
                'row',
                { 'warn-incomplete': daySpanIncomplete }
            ]"
        >
            <label for="from" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.from')) }}</label>
            <div class="col-sm-4">
                <VueDatepicker
                    v-model="startDateTime"
                    v-bind="dpOptions"
                />
            </div>
            <label for="until" class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.untilincluding')) }}
            </label>
            <div class="col-sm-4">
                <VueDatepicker
                    v-model="endDateTime"
                    v-bind="dpOptions"
                />
            </div>`
        </div>
        <!-- ROW 3 -->
        <div class="form-group row">
            <label class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.timespan')) }}</label>
            <div class="col-sm-4">
                <input class="form-control-plaintext" :value="timespan" readonly>
            </div>
            <label class="col-sm-2 col-form-label reposition-up1">
                {{ ucFirst(translate('generic.booklocation')) }}
            </label>
            <div class="col-sm-4">
                <select class="form-control" v-model="dateException.location_id">
                    <option value="0">{{ ucFirst(translate('generic.none')) }}</option>
                    <option v-for="location in allLocations" :key="location.id" :value="location.id">
                        {{ location.name }}
                    </option>
                </select>
            </div>
        </div>
        <hr v-if="!dateException.plan_blocking">
        <!-- ROW 4 -->
        <div class="form-group row" v-if="!dateException.plan_blocking">
            <label class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.hexcolor')) }}
                <span v-tooltip="{ content: translate('generic.explaincalendarcolor'), html: true }">
                    <font-awesome-icon icon="question-circle"/>
                </span>
            </label>
            <div class="col-md-4">
                <div class="input-group mb-3">
                    <input class="input-group-text form-control add-width add-cursor"
                           type="color" v-model="dateException.calendar_color">
                    <input type="text" class="form-control" :value="dateException.calendar_color" readonly>
                </div>
            </div>
            <label class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.detailurl')) }}
                <span v-tooltip="{ content: translate('generic.explaindetailurl'), html: true }">
                    <font-awesome-icon icon="question-circle"/>
                </span>
            </label>
            <div class="col-md-4">
                <div class="input-group mb-3">
                    <input class="form-control" v-model="dateException.detail_url">
                </div>
            </div>
        </div>
        <hr>
        <!-- ROW 5 -->
        <div class="form-group row">
            <div class="col">
                <label class="form-label">
                    {{ ucFirst(translate('generic.wordpresscode')) }}
                    <span v-tooltip="{ content: translate('generic.explainwordpresscode'), html: true }">
                        <font-awesome-icon icon="question-circle"/>
                    </span>
                </label>
                : <span class="text-muted">{{ dateException.id }}</span>
            </div>
        </div>
        <!-- ROW 6 -->
        <div class="form-group row mt-3">
            <label for="from" class="col-sm-4 col-form-label">
                {{ ucFirst(translate('generic.ignoreforalerts')) }}?
            </label>
            <div class="col-sm-2 mt-2">
                <div class="form-check form-switch">
                    <input
                        id="ignoreForAlerts"
                        class="form-check-input"
                        type="checkbox"
                        v-model="dateException.exclude_from_alerts"
                    >
                    <label class="form-check-label" for="ignoreForAlerts">
                        {{ dateException.exclude_from_alerts ? ucFirst(translate('generic.yes')) : ucFirst(translate('generic.no')) }}
                        <span v-tooltip="{ content: translate('generic.explainignoreforalerts'), html: true }"
                              class="ms-2">
                            <font-awesome-icon icon="question-circle"/>
                        </span>
                    </label>
                </div>
            </div>
            <label for="from" class="col-sm-3 col-form-label">
                {{ ucFirst(translate('generic.planblocking')) }}?
            </label>
            <div class="col-sm-3 mt-2">
                <div class="form-check form-switch">
                    <input
                        id="planBlocking"
                        class="form-check-input"
                        type="checkbox"
                        v-model="dateException.plan_blocking"
                    >
                    <label class="form-check-label" for="planBlocking">
                        {{ dateException.plan_blocking ? ucFirst(translate('generic.yes')) : ucFirst(translate('generic.no')) }}
                        <span v-tooltip="{ content: translate('generic.explainplanblocking'), html: true }"
                              class="ms-2">
                            <font-awesome-icon icon="question-circle"/>
                        </span>
                    </label>
                </div>
            </div>
        </div>
        <hr>
        <!-- ROW 7 -->
        <div class="form-group row mt-3">
            <label for="from" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.wholeschool')) }}?</label>
            <div class="col-sm-4 mt-2">
                <div class="form-check form-switch">
                    <input
                        id="shareWholeSchool"
                        class="form-check-input"
                        type="checkbox"
                        v-model="shareWholeSchool"
                    >
                    <label class="form-check-label" for="shareWholeSchool">
                        {{ shareWholeSchool ? ucFirst(translate('generic.yes')) : ucFirst(translate('generic.no')) }}
                    </label>
                </div>
            </div>
            <template v-if="!shareWholeSchool">
                <div class="form-check col-sm-6 mt-2">
                    <div class="row extra-margin-left">
                        <div class="col-12" v-for="tutor in allTutors" :key="tutor.id">
                            <input
                                type="checkbox"
                                class="form-check-input selectableTutor"
                                :value="tutor.id"
                                v-model="selectedTutors"
                            >{{ tutor.name }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mt-2">
                            <div class="btn-group" role="group" aria-label="select all">
                                <button class="btn btn-outline-success" @click="toggleSelectAllTutors(true)">
                                    Select all
                                </button>
                                <button class="btn btn-outline-success" @click="toggleSelectAllTutors(false)">
                                    Select none
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <hr>
        <!-- -->
        <div class="form-group row mt-3">
            <label for="from" class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.isrecurringappointment')) }}?</label>
            <div class="col-sm-4 mt-2">
                <div class="form-check form-switch">
                    <input
                        id="isRecurring"
                        class="form-check-input"
                        type="checkbox"
                        v-model="isRecurring"
                    >
                    <label class="form-check-label" for="isRecurring">
                        {{ isRecurring ? ucFirst(translate('generic.yes')) : ucFirst(translate('generic.no')) }}
                    </label>
                </div>
            </div>
            <div v-if="isRecurring" class="col-sm-6 mt-2">
                <div class="form-group">
                    <label>{{ translate('generic.startingfrom') }}: {{ dtStartFormatted }}</label>
                    <span v-if="dateException.datetime_start === ''">
                        <em>
                            {{ translate('generic.fillstartdattimeabove') }}
                        </em>
                    </span>
                </div>
                <div class="form-group">
                    <label><strong>{{ ucFirst(translate('generic.repeat')) }}</strong></label><br>
                    <input type="radio" v-model="whichRecurrence" value="0">&nbsp;{{
                        translate('generic.everyday')
                    }}<br>
                    <input type="radio" v-model="whichRecurrence" value="1">&nbsp;{{
                        translate('generic.everyweek')
                    }}<br>
                    <input type="radio" v-model="whichRecurrence" value="2">&nbsp;{{
                        translate('generic.otherweek')
                    }}<br>
                </div>
                <div class="form-group">
                    <label>{{ translate('generic.andendsafter') }} {{ translate('generic.date') }}:</label><br>
                    <small>{{ translate('generic.emptyisendofschoolyear') }}</small>
                    <VueDatepicker
                        v-model="recurringEndAfterDate"
                        :format="dateFormat"
                        :locale="lang"
                        :enable-time-picker="false"
                    />
                </div>
            </div>
        </div>

        <template #popupmessage v-if="errorMessage !== ''">
            <span class="text-danger" v-html="errorMessage"></span>
        </template>
        <template #okbutton>
            <button type="button"
                    class="btn btn-primary"
                    data-bs-dismiss="modal"
                    :disabled="!isSavable"
                    @click.prevent="saveDateException"
                    data-testid="date-exception-save-button"
            >
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import Modal from '../Layout/Modal.vue';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import moment from 'moment';
import { computed, onMounted, ref, watch, watchEffect } from 'vue';
import { parse, compareAsc, isAfter } from 'date-fns';
import useToast from '../../composables/generic/useToast.js';
import useLang from '../../composables/generic/useLang.js';
import useBaseData from '../../composables/generic/useBaseData.js';
import useApi from '../../composables/generic/useApi.js';

const { successToast, failToast } = useToast();
const { translate, translateChoice, ucFirst, lang } = useLang();
const { allTutors, allLocations, initBaseData } = useBaseData();
const { apiGet, apiPost, apiPut } = useApi();
const emits = defineEmits(['triggerUpdateDEList']);
const props = defineProps({
    dateExceptionId: {
        type: Number,
        required: true
    },
    modalId: {
        type: String,
        required: true
    },
    schoolyearId: {
        type: Number,
        required: true
    }
});

const busy = ref(false);
const dateException = ref({
    id: 0,
    reason: '',
    exclude_from_alerts: false,
    plan_blocking: true, // defaults to true!
    ignore_for_alerts: false,
    isWholeDay: false,
    calendar_color: '',
    datetime_start: '',
    datetime_end: '',
    schoolyear_id: props.schoolyearId,
    tutors: []
});
const errorMessage = ref('');
const selectedTutors = ref([]);
const shareWholeSchool = ref(false);
const isRecurring = ref(false);
const whichRecurrence = ref(1);
const recurringEndAfterDate = ref('');
import useDatePicker from '../../composables/generic/useDatePicker.js';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
// make sure we keep reactivity by wrapping the attribute in a computed var.
const isWholeDay = computed(() => dateException.value.isWholeDay);
// Get date picker options from the composable
const { dpOptions } = useDatePicker(isWholeDay);
const startDateTime = ref('');
const endDateTime = ref('');

onMounted(async () => {
    await initBaseData({
        locations: true,
        tutors: true
    });
});

/**
 * this will initialize the model variables into Date(time) objects
 */
watchEffect(() => {
    startDateTime.value = parse(dateException.value.datetime_start, 'yyyy-MM-dd HH:mm:ss', new Date());
    endDateTime.value = parse(dateException.value.datetime_end, 'yyyy-MM-dd HH:mm:ss', new Date());
});

/**
 * (de-)select all tutors
 * the list of tutors will always be empty if we select all on or all off
 * the only difference is the 'shareWholeSchool' parameter
 * @param selectOn boolean
 */
const toggleSelectAllTutors = (selectOn = true) => {
    selectedTutors.value = [];
    shareWholeSchool.value = selectOn;
};
const saveDateException = async () => {
    // upsert...
    const method = dateException.value.id > 0 ? 'put' : 'post';
    // only if 'shareWholeSchool' is false do we look at the selected tutors
    // there maybe old data in there. the 'shareWholeSchool' parameter overrides this
    dateException.value.tutors = shareWholeSchool.value ? [] : selectedTutors.value;
    const data = {
        ...dateException.value,
        repeatAppointment: {
            recurrenceOption: isRecurring.value ? whichRecurrence.value : -1,
            repeatEndsAfterDate: recurringEndAfterDate.value
        }
    };
    try {
        let response;
        if (method === 'put') {
            response = await apiPut('/api/dateexception', data);
        } else {
            response = await apiPost('/api/dateexception', data);
        }
        successToast(response.data.message, translate('generic.success'));
        emits('triggerUpdateDEList');
    } catch (error) {
        failToast(error.message, translate('generic.error'));
    }
};

watch(() => props.dateExceptionId, async () => {
    // new DE or update existing one?
    // existing needs to be retrieved through api call
    if (props.dateExceptionId > 0) {
        busy.value = true;
        try {
            const response = await apiGet(`/api/dateexceptions/${ props.dateExceptionId }`);
            dateException.value = response.data.data;
            // Make boolean attributes actually boolean instead of '1' and '0'.
            // The backend should revert this during save
            dateException.value.exclude_from_alerts = dateException.value.exclude_from_alerts === 1;
            dateException.value.plan_blocking = dateException.value.plan_blocking === 1;
            dateException.value.ignore_for_alerts = dateException.value.ignore_for_alerts === 1;
        } catch (error) {
            failToast(error.message, translate('generic.error'));
        } finally {
            busy.value = false;
        }
    } else {
        // reset data fields
        dateException.value = {
            id: 0,
            reason: '',
            exclude_from_alerts: false,
            plan_blocking: true, // defaults to true!
            ignore_for_alerts: false,
            calendar_color: '',
            isWholeDay: false,
            datetime_start: '',
            datetime_end: '',
            schoolyear_id: props.schoolyearId,
            tutors: []
        };
    }
    if (dateException.value.tutors.length === 0) {
        // no tutors selected and shareWholeSchool is true
        toggleSelectAllTutors(true);
    } else {
        shareWholeSchool.value = false;
        dateException.value.tutors.forEach((tutor) => {
            selectedTutors.value.push(tutor.id);
        });
    }
    // reset recurrence fields
    recurringEndAfterDate.value = '';
    isRecurring.value = false;
    whichRecurrence.value = 1;
});

/**
 * initialize with school year id of the parent component (through property)
 */
watch(() => props.schoolyearId, async () => {
    dateException.value.schoolyear_id = props.schoolyearId;
});

watch(() => dateException.value.isWholeDay, (newValue) => {
    if (newValue) {
        // whole day, make time of DE 00:00:00 - 23:59:59
        dateException.value.datetime_start = dateException.value.datetime_start.substring(0, 10) + ' 00:00:00';
        dateException.value.datetime_end = dateException.value.datetime_end.substring(0, 10) + ' 23:59:59';
    }
});

/**
 * Check if a date exception is theoretically savable
 * This does not check if the date exception is valid in the context of the school year
 * checks:
 * - is the end date after the start date
 * - is the end date in the future
 * - if the DE is recurring:
 * -- is the repeat end date after the end date of the DE
 * @returns {boolean}
 * @type {ComputedRef<unknown>}
 */
const isSavable = computed(() => {
    let hasError = false;
    let localErrorMessage = '';

    if (isRecurring.value) {
        if (dateException.value.datetime_end.length >= 10 && recurringEndAfterDate.value) {
            try {
                const endDate = parse(dateException.value.datetime_end, 'yyyy-MM-dd HH:mm:ss', new Date());

                // Parse recurringEndAfterDate based on its type
                let recurringEndDate;
                if (recurringEndAfterDate.value instanceof Date) {
                    recurringEndDate = recurringEndAfterDate.value;
                } else {
                    // Assume it's in the format matching dateFormat
                    recurringEndDate = parse(recurringEndAfterDate.value, dateFormat.value, new Date());
                }

                // Check if end date is after recurring end date
                if (compareAsc(endDate, recurringEndDate) > 0) {
                    hasError = true;
                    localErrorMessage = ucFirst(translate('generic.repeatendisbeforeappointmentend'));
                }
            } catch (error) {
                console.error('Date comparison error:', error);
            }
        }
    }

    if (
        dateException.value.datetime_start.length >= 10 &&
        dateException.value.datetime_end.length >= 10
    ) {
        try {
            const startDate = parse(dateException.value.datetime_start, 'yyyy-MM-dd HH:mm:ss', new Date());
            const endDate = parse(dateException.value.datetime_end, 'yyyy-MM-dd HH:mm:ss', new Date());

            // Check if start date is after end date
            if (compareAsc(startDate, endDate) > 0) {
                hasError = true;
                localErrorMessage = ucFirst(translate('generic.endisbeforestart'));
            }
        } catch (error) {
            console.error('Date comparison error:', error);
        }
    }

    // The end date should be a future date
    if (dateException.value.datetime_end.length >= 10) {
        try {
            const endDate = parse(dateException.value.datetime_end, 'yyyy-MM-dd HH:mm:ss', new Date());
            const now = new Date();

            // Check if end date is in the past
            if (!isAfter(endDate, now)) {
                hasError = true;
                localErrorMessage = ucFirst(translate('generic.endisnotinfuture'));
            }
        } catch (error) {
            console.error('Date comparison error:', error);
        }
    }

    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    errorMessage.value = localErrorMessage;

    return dateException.value.reason.length > 1 &&
        dateException.value.datetime_start.length >= 10 &&
        dateException.value.datetime_end.length >= 10 &&
        !hasError;
});

const isAllDayLabel = computed(() => {
    return dateException.value.isWholeDay
        ? ucFirst(translate('generic.yes'))
        : ucFirst(translate('generic.no'));
});

const popupTitle = computed(() => {
    return dateException.value.id > 0
        ? ucFirst(translate('generic.editdateexception'))
        : ucFirst(translate('generic.newdateexception'));
});

const timespan = computed(() => {
    const start = moment(dateException.value.datetime_start);
    const end = moment(dateException.value.datetime_end);
    if (dateException.value.isWholeDay) {
        // always return a whole number of days
        // add 1 to compensate for 23:59:59 to count as a whole day
        const nrOfDays = end.diff(start, 'days') + 1;
        if (isNaN(nrOfDays)) return '';
        return nrOfDays + ' ' + translateChoice('generic.days', nrOfDays);
    } else {
        // returns a number of minutes
        const nrOfMinutes = end.diff(start, 'minutes');
        const nrOfHours = end.diff(start, 'hours');
        const H2M = 60 * nrOfHours;
        const minutesLeft = nrOfMinutes - H2M;
        // assemble the resulting string
        const timeArray = [];
        if (nrOfHours > 0) {
            timeArray.push(nrOfHours + ' ' + translateChoice('generic.hours', nrOfHours));
        }
        if (minutesLeft > 0) {
            timeArray.push(minutesLeft + ' ' + translateChoice('generic.minutes', minutesLeft));
        }
        return timeArray.join(' ' + translate('generic.and') + ' ');
    }
});

const daySpanIncomplete = computed(() => {
    return ((dateException.value.isWholeDay) && timespan.value === '');
});
</script>

<style scoped lang="scss">
@use "../../../sass/tmpl3/variables" as *;

.static {
    background-color: white;
    color: black;
}

.warn-incomplete {
    background-color: $classred;
    color: white;
}

.extra-margin-left {
    margin-left: 1rem;
}

.reposition-up1 {
    top: -10px
}

.add-width {
    width: 3.5rem;
}

.add-cursor {
    cursor: pointer;
}
</style>
