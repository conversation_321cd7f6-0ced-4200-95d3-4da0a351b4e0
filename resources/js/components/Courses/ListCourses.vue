<template>
    <panel
        :busy="busy"
    >
        <template #title>
            <font-awesome-icon icon="graduation-cap" />
            {{ ucFirst(translateChoice('generic.courses', 2)) }}
        </template>
        <template #subtitle>
            <a href='/courses/create' class="btn btn-sm btn-success">
                {{ ucFirst(translate('generic.newcourse')) }}
            </a>
        </template>

        <!-- based on coursegroups -->
        <table class="tableFixHead">
            <thead>
            <tr>
                <th>
                    {{ ucFirst(translate('generic.name')) }} {{ translate('generic.coursegroup') }}
                </th>
                <th>
                    <div class="row">
                        <div class="offset-sm-6 col-3 text-center bg-danger text-light">
                            {{ ucFirst(translate('generic.price')) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-2">
                            {{ ucFirst(translate('generic.coursename'))}} <span class="muted">({{ translate('generic.wordpresscode') }})</span>
                        </div>
                        <div class="col-1 text-center">
                            {{ ucFirst(translateChoice('generic.groups', 1)) }}
                        </div>
                        <div class="col-3">
                            {{ ucFirst(translateChoice('generic.recurrenceoptions',1)) }}
                        </div>
                        <div class="col-1 text-end lb-red">
                            {{ ucFirst(translate('generic.priceExTaxSubAdult', {adultthreshold: domain.adultThreshold})) }}
                        </div>
                        <div class="col-1 text-end">
                            {{ ucFirst(translate('generic.priceExTax')) }}
                        </div>
                        <div class="col-1 text-end rb-red">
                            {{ ucFirst(translate('generic.priceinvoice')) }}
                        </div>
                        <div class="col-1">
                            {{ translate('generic.agegroup') }}
                        </div>
                        <div class="col-1">
                            {{ translate('generic.nrofstudents') }}
                        </div>
                        <div class="col-1">
                            {{ translate('generic.nrofactivestudents') }}
                        </div>
                    </div>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="courseGroup in filteredCourseGroups" :key="courseGroup.id" class="courseGroupSeparator">
                <td>{{ courseGroup.name }}</td>
                <td>
                    <div
                        v-for="(course, index) in courseGroup.courses"
                        :key="course.id"
                        class="row courseRow"
                        @click="navigateToCourse(course.id)"
                    >
                        <div class="col-2">
                            {{ course.name }} <span class="muted">({{ course.id }})</span>
                        </div>
                        <div class="col-1 text-center">
                            {{ course.groupSize }}
                        </div>
                        <div class="col-3">
                            {{ course.recurrenceoption.description }}
                        </div>
                        <div class="col-1 text-end lb-red">
                            {{ course.price_ex_tax_sub_adult }} / {{ translate(`generic.${course.price_is_per === 'time' ? 'times' : course.price_is_per}`) }}
                        </div>
                        <div class="col-1 text-end">
                            {{ (course.price_invoice / 1.21).toFixed(2) }} / {{ translate(`generic.${course.price_is_per === 'time' ? 'times' : course.price_is_per}`) }}
                        </div>
                        <div class="col-1 text-end rb-red">
                             {{ course.price_invoice }} / {{ translate(`generic.${course.price_is_per === 'time' ? 'times' : course.price_is_per}`) }}
                        </div>
                        <div class="col-1 text-center">
                            {{ course.target_age_min }}-{{ course.target_age_max }}
                        </div>
                        <div class="col-1 text-center">
                            {{ course.students.length }}
                        </div>
                        <div class="col-1">
                            <VDropdown v-if="course.current_students.length > 0" @click.stop>
                                <button class="btn btn-sm btn-outline-secondary ms-1 nr-of-students-button">
                                    {{ course.current_students.length }}
                                    <font-awesome-icon icon="info-circle" class="ms-1"/>
                                </button>
                                <template #popper>
                                    <div class="popover-content p-2">
                                        <strong>{{ ucFirst(translateChoice('generic.students', 2)) }}</strong>
                                        <div v-html="listStudents(course.current_students)"></div>
                                    </div>
                                </template>
                            </VDropdown>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </panel>
</template>

<script setup>
import { onMounted, ref } from "vue";
import Panel from "../Layout/Panel.vue";
import useLang from "../../composables/generic/useLang.js";
import useBaseData from "../../composables/generic/useBaseData.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate, translateChoice } = useLang();

const busy = ref(false);
const { initBaseData, filteredCourseGroups, domain } = useBaseData();

onMounted(async () => {
    busy.value = true;
    await initBaseData({
        courseGroups: true,
        domain: true,   // for domain.adultThreshold in the template
        courses: true   // for trial course relation
    });
    busy.value = false;
});

const navigateToCourse = (courseId) => {
    window.location = `/courses/${courseId}/edit`;
}

/**
 * for the popover: list students in group
 * @param students
 * @returns {string}
 */
const listStudents = (students) => {
    let retString = '<ul class="list-group list-group-flush">';
    students.forEach(student => {
        retString += `<li class="list-group-item"><a href="/students/${student.id}/edit">${student.name}</a></li>`
    });
    return `${retString}</ul>`;
}
</script>

<style scoped lang="scss">
@use '../../../sass/tmpl3/variables' as *;
/* for grouping the same course name, skip the table TD border*/
.empty {
    border-top: none;
}
.courseGroupSeparator {
    border-bottom: solid 1px $gray-700;
}
table.tableFixHead {
    border-collapse: collapse;
    width: 100%;
    overflow: auto;
    height: 100px;
    & thead {
        position: sticky;
        top: 3.2rem;
        z-index: 1;
        & th {
            background: $gray-100;
            border-bottom: solid $gray-500 2px;
            color: black;
            position: sticky;
            top: 0;
            z-index: 1;
        }
    }
}
.muted {
    //color: $gray-500;
    color: var(--gray-500)
}
.lb-red {
    border-left: solid var(--class-red) 1px;
}
.rb-red {
    border-right: solid var(--class-red) 1px;
}
.courseRow {
    cursor: pointer;
    &:hover {
        background-color: var(--class-lightest-blue);
        color: black;
        & .muted {
            color: white;
        }
        & .nr-of-students-button {
            background-color: var(--class-lightest-blue);
            color: black;
        }
    }
}
</style>
