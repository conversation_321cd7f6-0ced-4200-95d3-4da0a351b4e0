<template>
    <panel :busy="busy">
        <template #title>
            <font-awesome-icon icon="graduation-cap" />
            {{ucFirst(translate('generic.triallessons'))}} {{ translate('generic.followuprelations') }}
        </template>

        <div class="row">
            <span
                class="col"
                v-for="course in nonTrialCourses"
                :key="'nontrialcourse_' + course.id"
            >
                <span class="badge rounded-pill bg-primary bigger"
                      draggable="true" @dragstart="drag"
                      :data-course-id="course.id"
                >
                    {{ course.name }}
                </span>
            </span>
        </div>
        <div class="row">
            <div class="col text-center">
                <small>{{ ucFirst(translate('generic.explaintrialcourserelation'))}}</small>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-1">
                <strong>{{ucFirst(translate('generic.edit'))}}</strong>
            </div>
            <div class="col-md-4">
                <strong>{{ucFirst(translate('generic.name'))}}</strong>
            </div>
            <div class="col-md-7">
                <strong>{{ucFirst(translate('generic.istrialcoursefor'))}}</strong>
                <small class="ms-2">({{translate('generic.clicktodelete')}})</small>
            </div>
        </div>
        <!-- List trial courses and their relations with other courses -->
        <div
            class="row mt-2"
            v-for="course in trialCourses"
            :key="course.id"
        >
            <div class="col-md-1">
                <button
                    class="btn btn-sm btn-primary"
                    v-tooltip="translate('generic.edit')"
                    @click="navigateToCourse(course.id)"
                >
                    <font-awesome-icon icon="edit" />
                </button>
            </div>
            <div class="col-md-4">
                <div class="btn btn-primary w-100"
                     @drop="drop"
                     @dragover="allowDrop"
                     @dragleave="removeStyle"
                     :data-trialcourse-id="course.id"
                >
                    {{ course.name }}
                </div>
            </div>
            <div class="col-md-7">
                <div v-if="courseRelations[course.id] != null">
                    <span
                        v-for="assocCourseId in courseRelations[course.id]"
                        :key="course.id + '_' + assocCourseId"
                    >
                        <span class="mb-1 me-1 delete-icon" @click="unlinkFromTrialCourse(course.id, assocCourseId)">
                            {{ showCourseName(assocCourseId) }}
                            <font-awesome-icon icon="trash" />
                        </span>
                    </span>
                </div>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref } from "vue";
import Panel from "../Layout/Panel.vue";
import useLang from "../../composables/generic/useLang.js";
import useToast from "../../composables/generic/useToast.js";
import useBaseData from "../../composables/generic/useBaseData.js";
import useApi from "../../composables/generic/useApi.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate } = useLang();
const { filteredCoursesNoRental, initBaseData } = useBaseData();
const { failToast, successToast } = useToast();
const { apiGet, apiPost, apiDel } = useApi();
const busy = ref(false);
const courseRelations = ref([]);

onMounted(async () => {
    await initBaseData({courses: true});
    getCourseTrialcourseRelations();
});

const trialCourses = computed(() => {
    return filteredCoursesNoRental.value.filter(course => course.is_trial_course);
});

const nonTrialCourses = computed(() => {
    const courses = filteredCoursesNoRental.value.filter(course => !course.is_trial_course);
    // only unique names (not variants, being other timespan or frequency)
    return courses.filter((course, index) => {
        return index === 0 ? true : course.name !== courses[index-1].name;
    });
});

const getCourseTrialcourseRelations = async () => {
    try {
        const response = await apiGet("/api/coursetrialcourse");
        courseRelations.value = response.data;
    } catch(err) {
        failToast(ucFirst(translate('generic.errorfetchingtrialcourserelations')));
    }
};

const showCourseName = (courseId) => {
    const course = filteredCoursesNoRental.value.find(c => c.id === courseId);
    if (course != null) {
        return course.name;
    } else {
        return ucFirst(translate('generic.unknown'));
    }
}

const navigateToCourse = (courseId) => {
    window.location = `/courses/${courseId}/edit`;
}

const drag = (ev) => {
    ev.dataTransfer.setData("text", ev.target.getAttribute('data-course-id'));
}

const drop = async (ev) => {
    ev.preventDefault();
    removeStyle(ev);
    let trialCourse = ev.target.getAttribute('data-trialcourse-id');
    let courseId = ev.dataTransfer.getData("text");
    try {
        await apiPost('/api/trialcourserelation', {trialCourse, courseId});
        getCourseTrialcourseRelations();
        successToast(ucFirst(translate("generic.courseRelationAdded")));
    } catch(err) {
        failToast(ucFirst(translate("generic.errorsavingcourserelation")));
    }
}

const allowDrop = (ev) => {
    ev.preventDefault();
    ev.target.classList.remove('btn-primary');
    ev.target.classList.add('btn-danger');
}

const removeStyle = (ev) => {
    ev.target.classList.remove('btn-danger');
    ev.target.classList.add('btn-primary');
}

const unlinkFromTrialCourse = async (trialCourseId, courseId) => {
    try {
        await apiDel('/api/trialcourserelation', {data: {courseId, trialCourseId}});
        getCourseTrialcourseRelations();
        successToast(ucFirst(translate("generic.courserelationremoved")));
    } catch(err) {
        failToast(ucFirst(translate("generic.errorsavingcourserelation")));
    }
}
</script>

<style scoped>
.delete-icon {
    display: inline-block;
    border: solid 1px black;
    padding-left: 0.4rem;
    padding-right: 0.4rem;
    cursor: url('/images/icons8-remove-32.png'), auto;
}
.bigger {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}
</style>
