<template>
    <panel>
        <template v-slot:title>
            <i class="fas fa-users"></i>
            {{ucFirst(translateChoice('generic.studentgroups',2))}}
        </template>
        <template v-slot:subtitle>
            <a :href="`/studentgroups/create?course_id=${courseId}`"
               class="btn btn-success btn-sm">
                {{ucFirst(translate('generic.newstudentgroup'))}}
            </a>
        </template>

        <div v-if="studentGroups.length > 0">
            <div class="row">
                <div class="col-md-1"><h5>{{ucFirst(translate('generic.functions'))}}</h5></div>
                <div class="col-md-2"><h5>{{ucFirst(translate('generic.name'))}}</h5></div>
                <div class="col-md-9"><h5>{{ucFirst(translate('generic.numberofstudents'))}}</h5></div>
            </div>
            <div class="row" v-for="studentGroup in studentGroups" :key="studentGroup.id">
                <div class="col-md-1">
                    <a :href="'/studentgroups/' + studentGroup.id + '/edit'"
                       v-tooltip="translate('generic.edit')"
                       data-toggle="tooltip"
                       class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i>
                    </a>

                    <!-- planning link -->
                    <a v-if="userIsAdmin && studentGroup.students.length > 0"
                       :href="'/timetableedit/' + studentGroup.pivot.id"
                       class="edittimetable btn btn-sm btn-outline-secondary"
                       v-tooltip="ucFirst(translate('generic.opentimetable'))">
                        <span class='fas fa-calendar'></span>
                    </a>
                </div>
                <div class="col-md-2">{{ studentGroup.lastname }}</div>
                <div class="col-md-9">{{ studentGroup.students.length }}</div>
            </div>
        </div>
        <div v-else class="row">
            <div class="col">{{ucFirst(translate('generic.nostudentgroupsfound'))}}</div>
        </div>

    </panel>
</template>

<script>
import Panel from '../Layout/Panel.vue';
import useApi from "../../composables/generic/useApi.js";
import useLang from "../../composables/generic/useLang.js";

const { translate, translateChoice, ucFirst } = useLang();
const { apiGet } = useApi();

export default {
    name: 'StudentGroups',
    components: { Panel },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            studentGroups: []
        };
    },
    props: {
        courseId: {
            type: Number,
            required: true
        },
        userIsAdmin: {
            type: Boolean,
            default: false
        }
    },
    mounted () {
        this.fetchStudentGroups();
    },
    methods: {
        fetchStudentGroups () {
            apiGet('/api/getstudentgroupsofcourse/' + this.courseId)
                .then(result => {
                    this.studentGroups = result.data;
                })
                .catch(err => {
                    this.failNoty('Error fetching studentgroups for this course', ucFirst(translate('generic.error')));
                    console.log(err);
                });
        }
    }
};
</script>

<style scoped>

</style>
