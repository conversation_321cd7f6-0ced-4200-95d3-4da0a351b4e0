<template>
<div>
    <div class="d-flex justify-content-between align-items-center">
        <strong>{{ ucFirst(translateChoice('generic.recurrenceoptions', 2)) }}</strong>
        <div class="d-flex align-items-center">
            <input
                type="text"
                class="form-control mx-3"
                :placeholder="translate('generic.search')"
                v-model="searchRecurrenceOptions"
            >
            <div class="mx-3">
                {{ filteredRecurrenceOptions.length }}&nbsp;/&nbsp;{{ recurrenceOptions.length }}
            </div>
        </div>
    </div>
    <div class="table-container">
        <table class="table mt-2">
            <thead>
            <tr>
                <th>{{ ucFirst(translate("generic.functions")) }}</th>
                <th>{{ ucFirst(translate("generic.description")) }}</th>
            </tr>
            </thead>
            <tbody class="scrollable-body">
            <tr
                v-for="(recurrenceOption, index) in filteredRecurrenceOptions"
                :key="index"
            >
                <td>
                    <div class="btn-group" role="group" aria-label="functions">
                        <button
                            class="btn btn-primary btn-sm"
                            v-tooltip="translate('generic.edit')"
                            @click.prevent="recurrenceOptionToEdit = recurrenceOption"
                        >
                            <font-awesome-icon icon="edit" />
                        </button>
                        <button
                            class="btn btn-danger btn-sm"
                            data-bs-toggle="modal"
                            data-bs-target="#del_areyousure"
                            v-tooltip="recurrenceOption.courses.length > 0 ? translate('generic.cantdelete') : translate('generic.delete')"
                            @click="recurrenceOptionIdToDelete = recurrenceOption.id"
                            :disabled="recurrenceOption.courses.length > 0"
                        >
                            <font-awesome-icon icon="trash" />
                        </button>
                    </div>
                </td>
                <td>
                    {{ recurrenceOption.description }}
                    <button
                        v-if="recurrenceOption.courses.length > 0"
                        class="btn btn-link btn-sm p-0 ms-2 text-primary"
                        data-bs-toggle="popover"
                        data-bs-trigger="focus"
                        :data-bs-content="getCourseNames(recurrenceOption.courses)"
                        data-bs-html="true"
                    >
                        <font-awesome-icon icon="question-circle" />
                    </button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <are-you-sure-4
        :button-text="ucFirst(translate('generic.deleterecurrenceoption'))"
        @confirmclicked="removeRecurrenceOption()"
        modal-id="del_areyousure"
    ></are-you-sure-4>
</div>
</template>

<script setup>
import { computed, onMounted } from "vue";
import useLang from "../../composables/generic/useLang.js";
import useRecurrenceOptions from "../../composables/useRecurrenceOptions.js";
import AreYouSure from '../Layout/AreYouSure.vue';

import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate, translateChoice } = useLang();
const {
    recurrenceOptions,
    recurrenceOptionIdToDelete,
    recurrenceOptionToEdit,
    removeRecurrenceOption,
    searchRecurrenceOptions
} = useRecurrenceOptions();

const filteredRecurrenceOptions = computed(() => {
    return recurrenceOptions.value.filter((recurrenceOption) => {
        return recurrenceOption.description.toLowerCase().includes(searchRecurrenceOptions.value.toLowerCase());
    });
});

const getCourseNames = (courses) => {
    const names = courses.map(course =>
        `<a href="/courses/${course.id}/edit">${course.name}</a>`
    ).join('<br>');

    return `<strong>${ucFirst(translateChoice('generic.courses', courses.length))}</strong><br><hr>${names}`;
};

onMounted(() => {
    // Initialize popovers with Bootstrap 5
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

</script>

<style scoped>
.table-container {
    position: relative;
    height: calc(100vh - 25rem);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
    display: block;
    overflow-y: auto;
    max-height: 100%;
}

thead {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1;
    display: table;
    width: 100%;
    table-layout: fixed;
}

.scrollable-body {
    display: block;
    overflow-y: auto;
    height: calc(100% - 2.5rem);
}

.scrollable-body tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

/* Zorg ervoor dat de kolommen uitgelijnd blijven */
th, td {
    min-width: 9.375rem;
}

th:first-child, td:first-child {
    width: 8rem;
}
</style>
