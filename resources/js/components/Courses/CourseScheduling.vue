<template>
    <Panel>
        <template #title>
            <i class="fas fa-calendar"></i>
            <span v-if="lessonTable.length > 0" class="ms-2">
                {{ucFirst(translate('generic.courseisscheduledon'))}}
            </span>
            <span v-else class="ms-2">
                {{ucFirst(translate('generic.coursehasnotbeenscheduled'))}}
            </span>
        </template>
        <div v-if="orderedLessonTable.length > 0">
            <div class="row" v-for="(lesson, index) in lessonTable" :key="index">
                <div class="col">
                    {{ucFirst(lesson.dow_name)}} {{translate('generic.at')}} {{lesson.time}} ({{lesson.nrOfEvents}}&nbsp;
                    {{translateChoice('generic.futureappointments', lesson.nrOfEvents)}})
                </div>
            </div>
        </div>
        <div v-else>&nbsp;</div>
    </Panel>
</template>

<script setup>
import { computed } from "vue";
import Panel from "../Layout/Panel.vue";
import useCourse from "../../composables/useCourse.js";
import useLang from "../../composables/generic/useLang.js";

const { lessonTable } = useCourse();
const { translate, translateChoice, ucFirst } = useLang();

/**
 * Sort by dow and then by time
 * @type {ComputedRef<[]>}
 */
const orderedLessonTable = computed(() => {
    return lessonTable.value.sort((a, b) => {
        if (a.dow < b.dow) {
            return -1;
        }
        if (a.dow > b.dow) {
            return 1;
        }
        // dow is equal
        if (a.time < b.time) {
            return -1;
        }
        if (a.time > b.time) {
            return 1;
        }
        // dow & time are equal
        return 0;
    });
});
</script>

<style scoped>

</style>
