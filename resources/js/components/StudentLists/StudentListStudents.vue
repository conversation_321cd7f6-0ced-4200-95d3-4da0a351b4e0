<template>
    <panel-4 :busy="busy">
        <template #title>
            <i class="fas fa-users"></i>
            {{ ucFirst(translateChoice('generic.students', 2)) }}
        </template>

        <div class="mb-3">
            <div class="btn-group">
                <button @click="addAllActiveRegistration" class="btn btn-primary btn-sm me-2">
                    {{ translate('generic.addallstudentswithactivecourse') }}
                </button>
                <button
                    v-if="studentlistToEdit.students && studentlistToEdit.students.length > 0"
                    @click="confirmRemoveAll"
                    class="btn btn-danger btn-sm me-2"
                >
                    {{ translate('generic.delallstudents') }}
                </button>
                <button
                    v-if="studentlistToEdit.students && studentlistToEdit.students.length > 0"
                    @click="removeSelectedStudentsFromList"
                    class="btn btn-danger btn-sm"
                    :disabled="selectedStudents.length === 0"
                >
                    {{ translate('generic.delselectedstudents') }}
                </button>
            </div>
        </div>

        <div v-if="!studentlistToEdit.students || studentlistToEdit.students.length === 0" class="alert alert-info">
            {{ ucFirst(translate('generic.nostudentsfound')) }}
        </div>

        <div v-else class="student-badges">
            <span
                v-for="student in studentlistToEdit.students"
                :key="student.id"
                :class="[selectedStudents.includes(student.id) ? 'badge-active' : '', 'badge bg-secondary', 'me-2', 'mb-2']"
            >
                <input
                    type="checkbox"
                    :checked="selectedStudents.includes(student.id)"
                    @click="toggleStudentSelection(student.id)"
                />
                <a
                    :href="'/students/' + student.id + '/edit'"
                    class="text-white"
                    v-html="student.name"
                ></a>
            </span>
        </div>

        <are-you-sure-4
            :button-text="ucFirst(translate('generic.delallstudents'))"
            modal-id="confirm-delete-all-students"
            @confirmclicked="removeAllStudentsFromList"
        >
            {{ ucFirst(translate('generic.areyousuredeleteallstudents')) }}
        </are-you-sure-4>
    </panel-4>
</template>

<script setup>
import Panel from '../Layout/Panel.vue';
import AreYouSure from '../Layout/AreYouSure.vue';
import useLang from '../../composables/generic/useLang.js';
import useStudentLists from '../../composables/useStudentLists.js';

const { ucFirst, translate, translateChoice } = useLang();
const {
    busy,
    studentlistToEdit,
    selectedStudents,
    addAllActiveRegistration,
    removeAllStudentsFromList,
    removeSelectedStudentsFromList,
    toggleStudentSelection
} = useStudentLists();

const confirmRemoveAll = () => {
    const modal = document.getElementById('confirm-delete-all-students');
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
};
</script>

<style scoped>
.student-badges {
    display: flex;
    flex-wrap: wrap;
}

.badge {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
}

.badge-active {
    background-color: #007bff !important;
}

.badge input[type="checkbox"] {
    margin-right: 5px;
    position: relative;
    top: 2px;
}

.badge a {
    color: white;
    text-decoration: none;
}
</style>
