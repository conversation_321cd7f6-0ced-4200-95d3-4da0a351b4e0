<template>
    <panel>
        <template #title>
            {{ ucFirst(translate('generic.unreadmessages')) }}<br/>
        </template>
        <template #subtitle>
            <!-- nog even niet
            <button
                class="btn btn-primary"
                @click="createNewMessage"
            >
                {{ucFirst(translate('generic.newmessage'))}}
            </button>-->
        </template>
        <table class="table" v-if="unreadMessages.length > 0">
            <thead>
            <tr>
                <!-- nog even niet
                <th class="text-center">{{ ucFirst(translate('generic.delete')) }}</th>
                -->
                <th class="text-center">{{ ucFirst(translate('generic.open')) }}</th>
                <th>{{ ucFirst(translate('generic.createdat'))}}</th>
                <th>{{ ucFirst(translate('generic.subject'))}}</th>
                <th>{{ ucFirst(translate('generic.recipient'))}}</th>
                <th>{{ ucFirst(translateChoice('generic.messages', 1))}}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(message, index) in unreadMessages" :key="index">
                <td class="text-center">
                    <button class="btn btn-primary btn-sm" @click.prevent="openDetail(message)">
                        <i class="fa fa-eye"></i>
                    </button>
                </td>
                <td>{{ displayDateTime(message.created_at) }}</td>
                <td>{{ message.subject }}</td>
                <td>{{ message.to_label }}</td>
                <td class="ellipsis">{{ message.body }}</td>
            </tr>
            </tbody>
        </table>
        <div v-else>
            {{ ucFirst(translate('generic.nomessagesfound'))}}
        </div>
    </panel>
    <panel>
        <template #title>
            {{ ucFirst(translate('generic.readmessages')) }}<br/>
        </template>
        <table class="table" v-if="readMessages.length > 0">
            <thead>
            <tr>
                <th class="text-center">{{ ucFirst(translate('generic.open')) }}</th>
                <th>{{ ucFirst(translate('generic.createdat'))}}</th>
                <th>{{ ucFirst(translate('generic.readat'))}}</th>
                <th>{{ ucFirst(translate('generic.subject'))}}</th>
                <th>{{ ucFirst(translate('generic.recipient'))}}</th>
                <th>{{ ucFirst(translateChoice('generic.messages', 1))}}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(message, index) in readMessages" :key="index">
                <td class="text-center">
                    <button class="btn btn-primary btn-sm" @click.prevent="openDetail(message)">
                        <i class="fa fa-eye"></i>
                    </button>
                </td>
                <td>{{ displayDateTime(message.created_at) }}</td>
                <td>{{ displayDateTime(message.read_at) }}</td>
                <td>{{ message.subject }}</td>
                <td>{{ message.to_label }}</td>
                <td class="ellipsis">{{ message.body }}</td>
            </tr>
            </tbody>
        </table>
        <div v-else>
            {{ ucFirst(translate('generic.nomessagesfound'))}}
        </div>
    </panel>
    <!-- not yet
    <are-you-sure
        :button-text    = "ucFirst(translate('generic.deletemessage'))"
        modal-id        = "confirm-delete-message"
        @confirmclicked = "deleteTutor(messageToDelete)"
    ></are-you-sure>
    -->
    <modal
        modal-id="detailview"
        :closetext="ucFirst(translate('generic.close'))"
        :popup-title="detailMessage != null ? detailMessage.subject : ''"
        size="large"
    >
        <div class="row">
            <div class="col">
                <div>{{ ucFirst(translate('generic.fromemail')) }}: {{ detailMessage.from_label }}</div>
                <div>{{ ucFirst(translate('generic.to')) }}: {{ detailMessage.to_label }}</div>
            </div>
            <div class="col">
                <div>
                    {{ ucFirst(translate('generic.createdat')) }}: {{ displayDateTime(detailMessage.created_at) }}
                </div>
                <div v-if="detailMessage.read_at">
                    {{ ucFirst(translate('generic.readat')) }}: {{ displayDateTime(detailMessage.read_at) }}
                </div>
            </div>
        </div>
        <hr>
        <div>
            {{ detailMessage.body }}
        </div>
    </modal>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import Panel from '../Layout/Panel.vue';
import Modal from '../Layout/Modal.vue';
import AreYouSure from '../Layout/AreYouSure.vue';
import useLang from '../../composables/generic/useLang.js';
import useDateTime from '../../composables/generic/useDateTime.js';
import useApi from '../../composables/generic/useApi.js';
import useToast from '../../composables/generic/useToast.js';

const { ucFirst, translate, translateChoice } = useLang();
const { displayDateTime } = useDateTime();
const { apiGet } = useApi();
const { failToast } = useToast();

const messages = ref([]);
const detailMessage = ref({
    from_label: '',
    to_label: '',
    created_at: '',
    subject: '',
    body: ''
});

const unreadMessages = computed(() => {
    return messages.value.filter(row => row?.read_at == null || row?.read_at === '');
});

const readMessages = computed(() => {
    return messages.value.filter(row => row?.read_at != null && row?.read_at.length > 0);
});

const getAllMessages = async () => {
    try {
        const resp = await apiGet('/api/adminmessages');
        messages.value = resp.data;
    } catch (error) {
        failToast(error.message);
    }
};

const openDetail = (message) => {
    detailMessage.value = message;
    const modalElement = document.getElementById('detailview');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    }
};

onMounted(async () => {
    await getAllMessages();
});
</script>

<style scoped>
.ellipsis {
    display: block;
    width: 25rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
