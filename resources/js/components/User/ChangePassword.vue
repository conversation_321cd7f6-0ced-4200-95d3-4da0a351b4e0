<template>
    <div>
        <strong>{{ ucFirst(translate('generic.password')) }}</strong>
        <div class="row">
            <div class="col-12 col-xl-12">
                <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#changePassword">
                    {{ucFirst(translate('generic.changepassword'))}}
                </button>
            </div>
        </div>
        <Modal
            modal-id="changePassword"
            :popup-title="ucFirst(translate('generic.changepassword'))"
            :closetext="ucFirst(translate('generic.close'))"
        >
            <EnterPasswordChange />
            <template #okbutton>
                <button
                    class="btn btn-danger"
                    @click="changePassword"
                    :disabled="passwordErrors.length > 0"
                    data-bs-dismiss="modal"
                >
                    {{ucFirst(translate('generic.changepassword'))}}
                </button>
            </template>
        </Modal>
    </div>
</template>

<script setup>
import useLang from '../../composables/generic/useLang.js';
import usePassword from '../../composables/usePassword.js';
import Modal from '../Layout/Modal.vue';
import EnterPasswordChange from './EnterPasswordChange.vue';
const { ucFirst, translate } = useLang();
const { changePassword, passwordErrors } = usePassword();
</script>
