<template>
    <div>
        <div class="row" v-if="profile">
            <div class="col-12 col-xl-12">
                <div class="row">
                    <div class="col-7 col-xl-7">
                        <div class="mb-3">
                            <img v-if="profile.avatar" :src="profile.avatar" height="100px"/>
                            <img v-else src="/images/no-profile.png" height="100px"/>
                        </div>
                    </div>
                    <div class="col-5 col-xl-5">
                        <language-switch />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" v-if="profile">
            <div class="col-12 col-xl-12 mt-2">
                <div class="mb-3">
                    <label for="avatar" class="form-label">{{ ucFirst(translate('generic.weburlofyourprofilephoto')) }}</label>
                    <input class="form-control" placeholder="Avatar URL" v-model="profile.avatar" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import useLang from '../../composables/generic/useLang.js';
import useProfile from '../../composables/useProfile.js';
import LanguageSwitch from './LanguageSwitch.vue';

const { ucFirst, translate } = useLang();
const { profile } = useProfile();
</script>
