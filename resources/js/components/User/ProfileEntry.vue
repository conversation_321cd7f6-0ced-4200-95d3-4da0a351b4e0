<template>
    <div>
        <div class="row">
            <div class="col-12 col-xl-12">
                <edit-avatar />
            </div>
        </div>
        <div class="row" v-if="profile">
            <div class="col-12 col-xl-6">
                <div class="mb-3">
                    <label for="name" class="form-label">{{ ucFirst(translate('generic.name')) }}</label>
                    <input class="form-control" id="name" v-model="profile.name" />
                </div>
            </div>
            <div class="col-12 col-xl-6">
                <div class="mb-3">
                    <label for="email" class="form-label">{{ ucFirst(translate('generic.email')) }}</label>
                    <input class="form-control" id="email" v-model="profile.email" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-xl-12">
                <button class="btn btn-primary" @click.prevent="saveProfile" :disabled="!saveable">Save</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import useLang from '../../composables/generic/useLang.js';
import useProfile from '../../composables/useProfile.js';
import EditAvatar from './EditAvatar.vue';

const { ucFirst,translate } = useLang();
const { profile, saveProfile, saveable } = useProfile();

</script>
