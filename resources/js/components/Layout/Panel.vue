<template>
    <div :class="'card shadow my-2 d-flex flex-column ' + extraClass" :data-testid="panelTestId" style="flex: 1 1 auto;">
        <!-- Card Header -->
        <div
            class="card-header py-3 d-flex flex-row align-items-center justify-content-between"
            aria-expanded="true"
        >
            <h6 :class="['m-0', 'font-weight-bold', `text-${status}`]">
                <slot name="title">Please add a title</slot>
            </h6>
            <span class="float-end">
                <slot name="subtitle" class="me-1"></slot>
                <button
                    v-if="collapsible"
                    class="btn btn-primary ms-2 px-3 collapse-button"
                    type="button"
                    :data-bs-toggle="'collapse'"
                    :data-bs-target="'#' + uid"
                    :aria-expanded="!isCollapsed"
                    :aria-controls="uid"
                >
                    <span v-if="isCollapsed" class="iconfont"> &#8711; </span>
                    <span v-else class="iconfont"> &#8710; </span>
                </button>
            </span>
        </div>
        <!-- Card Body - Bootstrap 5 collapse -->
        <div :id="uid" :class="['collapse flex-grow-1', { show: !collapse }]">
            <div class="card-body h-100">
                <spinner-svg v-if="busy" size="50px"/>
                <slot v-else>Please add some body content</slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import SpinnerSvg from './SpinnerSvg.vue';
import useUtils from "../../composables/generic/useUtils.js";

const { uniqueId } = useUtils();

const props = defineProps({
    busy: {
        type: Boolean,
        default: false
    },
    collapsible: {
        type: Boolean,
        default: false
    },
    collapse: {
        type: Boolean,
        default: false
    },
    extraClass: {
        type: String,
        default: ''
    },
    panelTestId: {
        type: String,
        default: 'panel'
    },
    status: {
        type: String,
        default: 'primary',
        validator: (value) => ['primary', 'success', 'danger'].includes(value)
    }
});

const uid = uniqueId();
const isCollapsed = ref(false);

onMounted(() => {
    // Set initial collapsed state
    isCollapsed.value = props.collapse;
    
    // Get the collapse element
    const collapseElement = document.getElementById(uid);
    
    if (collapseElement) {
        // Listen for Bootstrap collapse events
        collapseElement.addEventListener('shown.bs.collapse', () => {
            isCollapsed.value = false;
        });
        
        collapseElement.addEventListener('hidden.bs.collapse', () => {
            isCollapsed.value = true;
        });
    }
});

onUnmounted(() => {
    // Clean up event listeners
    const collapseElement = document.getElementById(uid);
    if (collapseElement) {
        collapseElement.removeEventListener('shown.bs.collapse', () => {
            isCollapsed.value = false;
        });
        collapseElement.removeEventListener('hidden.bs.collapse', () => {
            isCollapsed.value = true;
        });
    }
});
</script>

<style scoped lang="scss">
.card-header .form-check {
    margin-bottom: 0;
}

.point {
    cursor: pointer;
}

.collapse-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
        background-color: var(--bs-primary);
        color: white !important;
    }

    &.iconfont {
        width: 3rem;
        font-size: 1rem;
        color: var(--bs-primary);

        &:hover {
            color: white;
        }
    }
    
    .iconfont {
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }
}
</style>
