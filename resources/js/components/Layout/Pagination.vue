<template>
  <div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center flex-nowrap">
      <label class="form-label me-2 mb-0 text-nowrap">
          {{ translate('pagination.itemsperpage') }}:
      </label>
      <select
        class="form-select form-select-sm"
        style="width: 80px; min-width: 80px;"
        :value="perPage"
        @change="$emit('per-page-changed', parseInt($event.target.value))"
      >
        <option value="10">10</option>
        <option value="25">25</option>
        <option value="50">50</option>
        <option value="100">100</option>
      </select>
    </div>

    <div v-if="paginationData && paginationData.last_page > 1">
      <nav aria-label="Pagination">
        <ul class="pagination pagination-sm mb-0">
          <!-- Previous Page Link -->
          <li class="page-item" :class="{ disabled: currentPage === 1 }">
            <button
              class="page-link"
              @click="changePage(currentPage - 1)"
              :disabled="currentPage === 1"
            >
              <span aria-hidden="true">&laquo;</span>
            </button>
          </li>

          <!-- Page Number Links -->
          <li
            v-for="page in visiblePages"
            :key="page"
            class="page-item"
            :class="{ active: page === currentPage }"
          >
            <button
              class="page-link"
              @click="changePage(page)"
              :class="{ 'bg-primary text-white border-primary': page === currentPage }"
            >
              {{ page }}
            </button>
          </li>

          <!-- Next Page Link -->
          <li class="page-item" :class="{ disabled: currentPage === lastPage }">
            <button
              class="page-link"
              @click="changePage(currentPage + 1)"
              :disabled="currentPage === lastPage"
            >
              <span aria-hidden="true">&raquo;</span>
            </button>
          </li>
        </ul>
      </nav>
    </div>

    <div class="text-muted small text-nowrap">
      {{ translate('pagination.showingitemtoitemoftotal', { from, to, total }) }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import useLang from "../../composables/generic/useLang.js";

const { translate } = useLang();

const props = defineProps({
  paginationData: {
    type: Object,
    default: () => ({})
  },
  perPage: {
    type: Number,
    default: 25
  }
});

const emit = defineEmits(['page-changed', 'per-page-changed']);

// Computed properties with safe defaults
const currentPage = computed(() => props.paginationData?.current_page || 1);
const lastPage = computed(() => props.paginationData?.last_page || 1);
const total = computed(() => props.paginationData?.total || 0);
const from = computed(() => props.paginationData?.from || 0);
const to = computed(() => props.paginationData?.to || 0);

const visiblePages = computed(() => {
  if (!props.paginationData || !props.paginationData.last_page) {
    return [1];
  }

  const current = currentPage.value;
  const last = lastPage.value;
  const delta = 2; // Number of pages to show on each side of current page

  let start = Math.max(1, current - delta);
  let end = Math.min(last, current + delta);

  // Adjust if we're near the beginning or end
  if (current <= delta + 1) {
    end = Math.min(last, 2 * delta + 1);
  }
  if (current >= last - delta) {
    start = Math.max(1, last - 2 * delta);
  }

  const pages = [];
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
});

const changePage = (page) => {
  if (page >= 1 && page <= lastPage.value && page !== currentPage.value) {
    emit('page-changed', page);
  }
};
</script>

<style scoped>
.page-link {
  cursor: pointer;
}

.page-item.disabled .page-link {
  cursor: not-allowed;
}
</style>
