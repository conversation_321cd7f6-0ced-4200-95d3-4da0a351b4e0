<template>
    <div class="side-panel-wrapper">
        <div v-if="isOpen" class="side-panel-overlay" @click="$emit('close')"></div>
        <div class="side-panel" :class="{ 'side-panel-open': isOpen }">
            <div class="side-panel-content">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><slot name="title"></slot></h5>
                    <button type="button" class="close" @click="$emit('close')">&times;</button>
                </div>
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
defineProps({
    isOpen: {
        type: Boolean,
        default: false
    }
});

defineEmits(['close']);
</script>

<style scoped>
.side-panel-wrapper {
    position: relative;
    z-index: 1050;
}

.side-panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.side-panel {
    position: fixed;
    top: 0;
    right: -400px;
    height: 100vh;
    width: 400px;
    background-color: #fff;
    box-shadow: -2px 0 5px rgba(0,0,0,0.2);
    transition: right 0.3s ease;
    z-index: 2;
}

.side-panel-open {
    right: 0;
}

.side-panel-content {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}
</style> 