<template>
    <panel panel-test-id="student-card-generic">
        <template #title>
            <i class="fas fa-user"></i>&nbsp;{{ ucFirst(translate('generic.genericdata')) }}
        </template>

        <template v-if="studentId > 0" #subtitle>
            <span
                class="mx-2"
                v-tooltip="ucFirst(translate('generic.youneedthisidtodelete'))"
            >
                ID: {{ studentId }}
                <i class="fa fa-question-circle"></i>
            </span>
            <quick-jump-student
                :exclude-id="parseInt(studentId)"
                :popup-title="ucFirst(translate('generic.search')) + ' ' + translateChoice('generic.students',1)"
                display-fields="name"
                api-get-string="/api/students?onlystudents=true"
                @record-chosen="jumpToStudent"
            />
        </template>
        <template v-else v-slot:subtitle>
            <new-student-by-copy></new-student-by-copy>
        </template>

        <div class="d-flex flex-row">
            <div class="flex-fill me-2 mb-3">
                <label for="firstname" class="form-label">{{ ucFirst(translate('generic.firstname')) }}</label>
                <input id="firstname"
                       v-model="dataFirstname"
                       @input="dirty=true"
                       type="text"
                       name="firstname"
                       class="form-control">
            </div>
            <div class="flex-fill me-2 mb-3">
                <label for="preposition" class="form-label">{{ ucFirst(translate('generic.preposition')) }}</label>
                <input id="preposition"
                       v-model="dataPreposition"
                       @input="dirty=true"
                       type="text"
                       name="preposition"
                       class="form-control">
            </div>
            <div class="flex-fill me-2 mb-3">
                <label for="lastname" class="form-label">{{ ucFirst(translate('generic.lastname')) }} (*)</label>
                <input id="lastname"
                       v-model="dataLastname"
                       @input="dirty=true"
                       type="text"
                       name="lastname"
                       required
                       class="form-control">
            </div>
        </div>

        <div class="d-flex flex-row">
            <div class="flex-fill me-2 mb-3">
                <label for="name" class="form-label">{{ ucFirst(translate('generic.name')) }}</label>
                <input id="name"
                       :value="fullname"
                       type="text"
                       name="name"
                       readonly="readonly"
                       class="form-control"
                       disabled="disabled">
            </div>
            <div class="flex-fill me-2 mb-3">
                <label for="address" class="form-label">{{ ucFirst(translate('generic.address')) }}</label>
                <input id="address"
                       v-model="dataAddress"
                       @change="dirty=true"
                       type="text"
                       name="address"
                       class="form-control">
            </div>
            <div class="flex-fill me-2 mb-3">
                <label for="zipcode" class="form-label">{{ ucFirst(translate('generic.zipcode')) }}</label>
                <input id="zipcode"
                       v-model="dataZipcode"
                       @change="dirty=true"
                       type="text"
                       name="zipcode"
                       class="form-control">
            </div>
            <div class="flex-fill me-2 mb-3">
                <label for="city" class="form-label">{{ ucFirst(translate('generic.city')) }}</label>
                <input id="city"
                       v-model="dataCity"
                       @change="dirty=true"
                       type="text"
                       name="city"
                       class="form-control">
            </div>
        </div>
        <div class="d-flex flex-row">
            <div class="flex-fill me-2 mb-3">
                <label for="date_of_birth" class="form-label">{{ ucFirst(translate('generic.birthdate')) }} (*)</label>
                <VueDatepicker
                    v-model="dob"
                    v-bind="dpOptions"
                    required
                    @update:model-value="dirty=true"
                    data-test-id="date_of_birth"
                />
                <input type="hidden" :value="dbDateOfBirth" name="date_of_birth" />
            </div>
            <div class="flex-fill me-2 mb-3">
                <label for="age" class="form-label">{{ ucFirst(translate('generic.age')) }}</label>
                <input id="age"
                       :value="displayAge(dob)"
                       type="text"
                       name="age"
                       readonly="readonly"
                       disabled="disabled"
                       class="form-control">
            </div>

            <!-- accesstoken field has no name, should not be in the post (will be ignored anyway) -->
            <div v-if="!isNewAction" class="flex-fill me-2 mb-3">
                <label for="accesstoken" class="form-label">{{ ucFirst(translate('generic.accesstoken')) }}</label>
                <input id="accesstoken"
                       :value="accessToken"
                       type="text"
                       readonly="readonly"
                       disabled="disabled"
                       class="form-control"
                >
            </div>

            <div v-if="!isNewAction" class="flex-fill me-2 mb-3">
                <label class="form-label">{{ ucFirst(translate('generic.permissionsocialshare')) }}</label>
                <input
                    class="form-control"
                    :value="agreeSocialShare === 1
                        ? ucFirst(translate('generic.yes'))
                        : ucFirst(translate('generic.no'))"
                    readonly="readonly"
                    disabled="disabled"
                >
            </div>

            <div v-if="!isNewAction" class="flex-fill me-2 mb-3">
                <div class="row">
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <label
                                class="form-check-label"
                                for="has_access"
                                v-tooltip="{ content: ucFirst(translate('generic.explainhasaccess')), html: true }"
                            >
                                {{
                                    StudentHasAccess ? ucFirst(translate('generic.hasaccess')) : ucFirst(translate('generic.hasnoaccess'))
                                }}
                                <font-awesome-icon icon="question-circle" class="ms-1"/>
                            </label>
                            <input
                                id="has_access"
                                name="has_access"
                                class="form-check-input ms-1 mt-3"
                                type="checkbox"
                                v-model="StudentHasAccess"
                                @change="dirty=true"
                            >
                        </div>
                    </div>
                    <div class="col-6">
                        <label class="form-label">{{ ucFirst(translate('generic.preferredschedule')) }}</label>
                        <div class="mb-3">
                            <a class="btn btn-primary form-control" :href="'/students/preferredschedule/' + studentId">
                                {{ ucFirst(translate('generic.openpage')) }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-row" v-if="dirty">
            <div class="alert alert-warning">
                {{ ucFirst(translate('generic.pleasesavechanges')) }}
            </div>
        </div>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref, watch, watchEffect } from 'vue';
import Panel from '../Layout/Panel.vue';
import QuickJumpStudent from '../Layout/QuickJump.vue';
import NewStudentByCopy from './NewStudentByCopy.vue';
import useLang from '../../composables/generic/useLang.js';
import useToast from '../../composables/generic/useToast.js';
import useDateTime from '../../composables/generic/useDateTime.js';
import useDatePicker from '../../composables/generic/useDatePicker.js';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { format, parse } from "date-fns";

const { displayAge } = useDateTime();

const props = defineProps({
    studentId: {
        type: Number,
        default: 0
    },
    firstname: {
        type: String,
        default: ''
    },
    preposition: {
        type: String,
        default: ''
    },
    lastname: {
        type: String,
        default: ''
    },
    address: {
        type: String,
        default: ''
    },
    zipcode: {
        type: String,
        default: ''
    },
    city: {
        type: String,
        default: ''
    },
    dateOfBirth: {
        type: String,
        default: ''
    },
    accessToken: {
        type: String,
        default: ''
    },
    hasAccess: {
        type: String,
        default: ''
    },
    agreeSocialShare: {
        type: Number,
        default: 0
    }
});

const { ucFirst, translate, translateChoice } = useLang();
const { successToast, failToast } = useToast();

// Get date picker options (language is handled internally by the composable)
const { dpOptions } = useDatePicker({ value: true });
const dob = ref(null);
const dbDateOfBirth = ref("");

const dirty = ref(false);
const StudentHasAccess = ref(props.hasAccess !== '0');
const dataFirstname = ref(props.firstname);
const dataLastname = ref(props.lastname);
const dataPreposition = ref(props.preposition);
const dataAddress = ref(props.address);
const dataZipcode = ref(props.zipcode);
const dataCity = ref(props.city);

onMounted(() => {
    StudentHasAccess.value = props.hasAccess !== '0';
    dataFirstname.value = props.firstname;
    dataLastname.value = props.lastname;
    dataPreposition.value = props.preposition;
    dataAddress.value = props.address;
    dataZipcode.value = props.zipcode;
    dataCity.value = props.city;
    dob.value = parse(props.dateOfBirth, "yyyy-MM-dd", new Date())// turn db formatted into a date object
});

watchEffect(() => {
    // set dbDateOfBirth (string) to be the db version of dob (date object) at all times.
    if (dob.value) {
        dbDateOfBirth.value = format(dob.value, "yyyy-MM-dd");
    }
});


/**
 * basically: concatenate firstname, preposition and lastname
 * this will be saved in the backend so we need to do this only once
 * @type {ComputedRef<string>}
 */
const fullname = computed(() => {
    const concatenated = isStudentGroup.value
        ? dataLastname.value
        : dataFirstname.value + ' ' +
        dataPreposition.value + ' ' +
        dataLastname.value;
    // if no preposition, there will be two consecutive spaces
    // if all fields empty remove the only space left (trim)
    return (concatenated.replace('  ', ' ')).trim();
});

/**
 * determine if the student is actually a studentgroup
 * @type {ComputedRef<boolean>}
 */
const isStudentGroup = computed(() => {
    return props.firstname === '-' && props.dateOfBirth === '1800-01-01';
});

/**
 * see if the component is used for a 'new' action instead of an update
 * @type {ComputedRef<boolean>}
 */
const isNewAction = computed(() => {
    return props.studentId === 0;
});

const jumpToStudent = (studentId) => {
    window.location.href = `/students/${ studentId }/edit`;
};

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});
</script>

<style scoped lang="scss">

</style>
