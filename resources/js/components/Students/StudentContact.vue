<template>
    <Panel :busy="busy" panel-test-id="student-card-contact">
        <template v-slot:title>
            <i class="fas fa-address-card"></i>&nbsp;{{ ucFirst(translate('generic.contactdata')) }}
        </template>
        <template v-slot:subtitle>
            <button
                type="button"
                v-tooltip="ucFirst(translate('generic.newcontactitem'))"
                class="btn btn-sm btn-success"
                @click.prevent="addContactItemRow"
            >
                {{ ucFirst(translate('generic.new')) }}
            </button>
        </template>

        <div class="table-responsive">
            <table class="table">
                <colgroup>
                    <col style="width:100px"/>
                    <col style="width:150px"/>
                    <col style="width:250px"/>
                    <col style="width:250px"/>
                    <col/>
                </colgroup>
                <thead>
                <tr>
                    <td colspan="4" :class="{'alert alert-warning': dirty}">
                            <span v-if="dirty">
                                {{ ucFirst(translate('generic.pleasesavechanges')) }}
                            </span>&nbsp;
                    </td>
                    <td colspan="3" class="bl br">
                        {{ ucFirst(translate('generic.usefor')) }}
                        <span
                            v-tooltip="{ content: ucFirst(translate('generic.explainusefor')), html: true }"
                        >
                                <font-awesome-icon icon="question-circle"/>
                            </span>
                    </td>
                    <td colspan="2">&nbsp;</td>
                </tr>
                <tr>
                    <th>{{ ucFirst(translate('generic.functions')) }}</th>
                    <th>{{ ucFirst(translate('generic.type')) }}</th>
                    <th>{{ ucFirst(translate('generic.label')) }} {{
                            translate('generic.explainstudentcontactlabel')
                        }}
                    </th>
                    <th>{{ ucFirst(translate('generic.value')) }}</th>
                    <th class="bl">{{ ucFirst(translate('generic.useforfinance')) }}</th>
                    <th>{{ ucFirst(translate('generic.useforplanning')) }}</th>
                    <th class="br">{{ ucFirst(translate('generic.useforpromotions')) }}</th>
                    <th>{{ ucFirst(translate('generic.usesalutation')) }}</th>
                    <th>{{ ucFirst(translateChoice('generic.links', 1)) }}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(contact, key) in contacts" :key="contact.id">
                    <td>
                        <div class="form-control-static">
                                <span
                                    v-tooltip="ucFirst(translate('generic.delete'))"
                                    class="btn btn-sm btn-danger"
                                    @click.prevent="deleteRow(contact.id)"
                                >
                                    <span class="fas fa-trash"></span>
                                </span>
                        </div>
                    </td>
                    <td>
                        <select
                            class="form-select"
                            v-model="contact.contacttype"
                            @change="dirty = true"
                            :name="'contacttype_' + contact.id"
                        >
                            <option value="telephone">telefoon</option>
                            <option value="email" selected="selected">e-mail</option>
                        </select>
                    </td>
                    <td>
                        <input
                            type="text"
                            class="form-control"
                            v-model="contact.label"
                            @keydown="dirty = true"
                            :name="'contactlabel_' + contact.id"
                        />
                    </td>
                    <td>
                        <div class="input-group mb-3" v-if="contact.contacttype === 'telephone'">
                            <input
                                :id="'contactValue_' + key"
                                class="form-control"
                                v-model="contact.value"
                                @keydown="dirty = true"
                                :name="'contactvalue_' + contact.id"
                            />
                            <button
                                class="btn btn-outline-secondary"
                                type="button"
                                @click.prevent="formatIfPhonenumber(key)"
                                v-tooltip="ucFirst(translate('generic.formatphonenumber'))"
                            >
                                <i class="fas fa-phone"></i>
                            </button>
                        </div>
                        <input
                            v-else
                            :id="'contactValue_' + key"
                            class="form-control"
                            v-model="contact.value"
                            @keydown="dirty = true"
                            :name="'contactvalue_' + contact.id"
                        />
                    </td>
                    <td v-if="contact.id && !contact.id.toString().includes('new')" class="bl">
                        <div class="form-check">
                            <input
                                type="checkbox"
                                class="form-check-input form-check-input-medium"
                                value="1"
                                @change="saveApplyForValue('finance', key)"
                                v-model="contact.apply_for_finance"
                            />
                        </div>
                    </td>
                    <td v-if="contact.id && !contact.id.toString().includes('new')">
                        <div class="form-check" v-if="!contact.id.toString().includes('new')">
                            <input
                                type="checkbox"
                                class="form-check-input"
                                value="1"
                                @change="saveApplyForValue('planning', key)"
                                v-model="contact.apply_for_planning"
                            />
                        </div>
                    </td>
                    <td v-if="contact.id && !contact.id.toString().includes('new')" class="br">
                        <div class="form-check" v-if="!contact.id.toString().includes('new')">
                            <input
                                type="checkbox"
                                class="form-check-input"
                                value="1"
                                @change="saveApplyForValue('promotions', key)"
                                v-model="contact.apply_for_promotions"
                            />
                        </div>
                    </td>
                    <td colspan="3" v-else>
                        <em>{{ ucFirst(translate('generic.pleasesavefirst')) }}</em>
                    </td>
                    <td>
                        <input
                            type="text"
                            class="form-control"
                            v-model="contact.use_salutation"
                            @keydown="dirty = true"
                            :name="'contactsalutation_' + contact.id"
                        />
                    </td>
                    <td>
                        <div class="form-control-static">
                            <a v-if="contact.contacttype === 'telephone'" :href="'tel:' + contact.value">
                                {{ contact.value }}
                            </a>
                            <a v-else :href="'mailto:' + contact.value" target="_blank">
                                {{ contact.value }}
                            </a>
                        </div>
                    </td>
                </tr>
                <tr v-if="contacts.length === 0">
                    <td colspan="9">{{ ucFirst(translate('generic.nocontactsfound')) }}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </Panel>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import useLang from "../../composables/generic/useLang.js";
import useApi from "../../composables/generic/useApi.js";
import useToast from "../../composables/generic/useToast.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { translate, translateChoice, ucFirst } = useLang();
const { apiGet, apiDel, apiPut } = useApi();
const { successToast, failToast } = useToast();

// Props
const props = defineProps({
    studentId: {
        type: String,
        required: true
    }
});

// Reactive data
const dirty = ref(false);
const busy = ref(false);
const contacts = ref([]);

// Watch dirty flag and propagate to window
watch(dirty, (newValue) => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = newValue;
});

// Methods
const getStudentContacts = async () => {
    try {
        busy.value = true;
        const response = await apiGet(`/api/studentcontacts/${ props.studentId }`);
        // contacts.value = response.data.data;
        // Convert the apply_for fields to boolean
        contacts.value = response.data.data.map(contact => ({
            ...contact,
            apply_for_finance: Boolean(Number(contact.apply_for_finance)),
            apply_for_planning: Boolean(Number(contact.apply_for_planning)),
            apply_for_promotions: Boolean(Number(contact.apply_for_promotions))
        }));

    } catch (error) {
        failToast(`Error retrieving student contacts: ${ error }`);
    } finally {
        busy.value = false;
    }
};

const addContactItemRow = () => {
    contacts.value.push({
        apply_for_finance: 0,
        apply_for_planning: 0,
        apply_for_promotions: 0,
        contacttype: 'email',
        label: '',
        // make unique ID so user can insert multiple new fields
        id: 'new_' + contacts.value.length,
        student_id: props.studentId,
        use_salutation: '',
        value: ''
    });
};

/**
 * Delete a contact row
 * @param {string|number} id
 */
const deleteRow = async (id) => {
    try {
        await apiDel(`/api/studentcontact/${ id }`);

        // now remove from local data
        const index = contacts.value.findIndex(row => row.id === id);
        if (index > -1) {
            contacts.value.splice(index, 1);
        }

        // notify user
        successToast(ucFirst(translate('generic.studentcontactdeleted')));
    } catch (error) {
        failToast(ucFirst(translate('generic.studentcontactdeletefailed')) + `: ${ error }`);
    }
};

/**
 * save a changed 'apply-for' value to the database
 * @param {string} applyFor
 * @param {number} contactArrayKey
 */
const saveApplyForValue = async (applyFor, contactArrayKey) => {
    const data = {
        field: `apply_for_${ applyFor }`,
        // convert to int
        newValue: contacts.value[contactArrayKey][`apply_for_${ applyFor }`] ? '1' : '0'
    };

    try {
        await apiPut(`/api/updateContactApplyFor/${ contacts.value[contactArrayKey].id }`, data);
        successToast(ucFirst(translate('generic.useforfieldupdated')));
    } catch (error) {
        failToast(`Could not update field: ${ error }`);
    }
};

/**
 * if we may assume the entry is a phone number, format it to digits with spaces: dd_dd_dd_dd_dd
 * @param {number} index
 */
const formatIfPhonenumber = (index) => {
    if ((contacts.value[index].contacttype != null) && (contacts.value[index].contacttype === 'telephone')) {
        // seems to be meant as a telephone number
        // if only contains digits and not already spaces - if user knows better, don't overrule
        let inputVal = contacts.value[index].value;
        const regexOnlyDigits = /\d*/g;
        const regexEveryTwoDigits = /(\d\d)(?=\d)/g;
        const subst = '$1 ';
        inputVal = inputVal.replaceAll(' ', '');
        if (regexOnlyDigits.exec(inputVal)) {
            contacts.value[index].value = inputVal.replace(regexEveryTwoDigits, subst);
        } else {
            console.log("cant reformat, it's not a sequence of purely digits");
        }
    }
};

// Lifecycle
onMounted(() => {
    getStudentContacts();
});
</script>

<style scoped>
.bl {
    border-left: 1px solid #DEE2E6;
}

.br {
    border-right: 1px solid #DEE2E6;
}
/* Custom size for checkboxes */
.form-check {
    padding-left: 1.8rem; /* Slightly increased padding (default is 1.5rem) */
    display: flex;
    justify-content: center;
    margin-bottom: 0;
}

.form-check .form-check-input {
    width: 1.4rem;
    height: 1.4rem;
    margin-left: -1.8rem;
    cursor: pointer;
}

.form-check .form-check-input:hover {
    box-shadow: 0 0 0 0.15rem rgba(13,110,253,.25);
}

</style>
