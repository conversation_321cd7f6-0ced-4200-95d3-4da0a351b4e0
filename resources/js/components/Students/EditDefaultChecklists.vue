<template>
    <div>
        <strong>{{ formLabel }}</strong><br><hr>
        <!-- if not initial state (edit or new) -->
        <template v-if="formLabel.length < 10">
            <div class="list-container">
                <div class="mb-3">
                    <label for="name" class="form-label">{{ ucFirst(translate('generic.name')) }}</label>
                    <input
                        type="text"
                        class="form-control"
                        id="name"
                        v-model="defaultChecklistToEdit.name"
                    >
                </div>
                <div class="mb-3">
                    <label for="auto_add" class="form-label">
                        {{ ucFirst(translate('generic.autoapply')) }}
                        <span v-tooltip="{ content: translate('generic.explainautoapply'), html: true }">
                            <i class="fa fa-question-circle text-secondary"></i>
                        </span>
                    </label>
                    <div class="form-check form-switch">
                        <input
                            id="auto-add"
                            class="form-check-input"
                            type="checkbox"
                            v-model="defaultChecklistToEdit.auto_add"
                        >
                        <label class="form-check-label" for="auto-add">
                            {{ defaultChecklistToEdit.auto_add ? translate('generic.yes') : translate('generic.no') }}
                        </label>
                    </div>
                </div>
                <!-- max 12 items in a list -->
                <div v-for="n in 12" :key="n" class="mb-3">
                    <label :for="'item' + n" class="form-label">{{ ucFirst(translateChoice('generic.items', 1)) + " " + n }}</label>
                    <input
                        type="text"
                        class="form-control"
                        :id="'item' + n"
                        v-model="defaultChecklistToEdit['item' + n]"
                    >
                </div>
            </div>
            <hr>
            <div class="d-flex justify-content-between">
                <button
                    class="btn btn-primary"
                    @click="doSave"
                >
                    {{ ucFirst(translate('generic.save')) }}
                </button>
            </div>
        </template>
    </div>
</template>

<script setup>
import { computed } from "vue";
import useLang from "../../composables/generic/useLang.js";
import useDefaultChecklists from "../../composables/useDefaultChecklists.js";
import useToast from "../../composables/generic/useToast.js";

const { ucFirst, translate, translateChoice } = useLang();
const { failToast, successToast } = useToast();
const { defaultChecklistToEdit, saveDefaultChecklist } = useDefaultChecklists();

/**
 * Determine the label for the form
 * @type {ComputedRef<any|string>}
 */
const formLabel = computed(() => {
    // check if this is an initial state, having no object at all (<> empty)
    if (!defaultChecklistToEdit?.value && !defaultChecklistToEdit.value?.name && !defaultChecklistToEdit.value?.auto_add ) {
        return translate('generic.pleasechooseadefaultchecklistorcreateanewone');
    }
    return defaultChecklistToEdit.value?.id ? ucFirst(translate('generic.edit')) : ucFirst(translate('generic.new'));
});

const doSave = async () => {
    try {
        await saveDefaultChecklist();
        successToast(ucFirst(translateChoice('generic.defaultchecklists', 1)) + ' ' + translate('generic.saved'));
    } catch (error) {
        failToast(error);
    }
}
</script>

<style scoped>
.list-container {
    max-height: 40vh;
    overflow-y: auto;
}

.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.form-check-input, .form-check-label {
    cursor: pointer;
}
</style>
