<template>
    <modal
        :modal-id           = "modalId"
        :popup-title        = "ucFirst(translate('generic.emailstudentswithaccess'))"
        :closetext          = "ucFirst(translate('generic.cancel'))"
        size                = "large"
    >
        <template #okbutton>
            <button
                class="btn btn-primary"
                @click="sendmailmulti"
                :disabled="!enableEmailSending"
            >
                <font-awesome-icon icon="fa-solid fa-paper-plane" />
                {{ ucFirst(translate('generic.send')) }}
            </button>
        </template>
        <div class="row">
            <div class="col-md-2"><label>{{ ucFirst(translate('generic.to')) }}</label></div>
            <div class="col-md-10">
                <span>
                    {{students.length}} {{ translateChoice('generic.students', students.length) }}
                </span>
                <button
                    class="btn btn-sm btn-primary ms-2"
                    @click="toggleShowRecipients"
                >
                    <font-awesome-icon icon="fa-solid fa-eye" />
                    {{ ucFirst(translate('generic.showrecipients')) }}
                </button>

                <Transition name="fade">
                    <div v-if="showRecipients" class="mt-2 mb-2">
                        <span class="me-2" v-for="student in students">
                            [<a :href="`/students/${student.id}/edit`">{{student.name}}</a>]
                        </span>
                    </div>
                </Transition>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-2">
                <label for="frommulti" class="form-label">{{ ucFirst(translate('generic.from')) }}</label>
            </div>
            <div class="col-md-10">
                <input id="frommulti" name="from" class="form-control" v-model="from" />
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-2">
                <label for="subject" class="form-label">{{ ucFirst(translate('generic.subject')) }}</label>
            </div>
            <div class="col-md-10">
                <input type="text" class="form-control" id="subject" v-model="subject" />
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-md-2">
                <label for="templatemulti" class="form-label">
                    {{ucFirst(translate('generic.templates'))}}
                </label>
            </div>
            <div class="col-md-10">
                <list-templates for-target="c" @templateChosen="templateChosen"></list-templates>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
            <ckeditor
                v-if="ClassicEditor && editorConfigSimple"
                :editor="ClassicEditor"
                v-model="mailBody"
                :config="editorConfigSimple"
            />
        </div>
    </div>
    </modal>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import Modal from "../Layout/Modal.vue";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ClassicEditor } from 'ckeditor5';
import 'ckeditor5/ckeditor5.css';
import useConfigItems from '../../composables/generic/useConfigItems.js';
import useLang from '../../composables/generic/useLang.js';
import useEmailStudentsWithAccess from '../../composables/useEmailStudentsWithAccess.js';
import useBaseData from '../../composables/generic/useBaseData.js';
import ListTemplates from "../Email/ListTemplates.vue";

const { from, getStudentsWithAccess, mailBody, sendmailmulti, students, subject } = useEmailStudentsWithAccess();
const { editorConfigSimple } = useConfigItems();
const { domain, initBaseData } = useBaseData();
const { ucFirst, translate, translateChoice } = useLang();
const showRecipients = ref(false);

defineProps({
    modalId: {
        type: String,
        required: true
    }
});

onMounted(async () => {
    await initBaseData({ domain: true });
    await getStudentsWithAccess();
    subject.value = ucFirst(translate('generic.preferredtimeforlessonat', {schoolname: domain.value?.schoolName}));
    from.value = domain.value.strAddress?.email || '';
});

const templateChosen = (ev) => {
    if (ev.selectedTemplateId === -1) {
        mailBody.value = '';
    } else {
        mailBody.value = ev.selectedTemplate?.content || '';
    }
};

const toggleShowRecipients = () => {
    showRecipients.value = !showRecipients.value;
};

const enableEmailSending = computed(() => {
    return mailBody.value.length > 20 && subject.value.length > 5 && from.value.length > 5;
});

</script>

<style scoped>
:deep(.ck-editor__editable_inline) {
    min-height: 400px;
    max-height: 400px;
}

/* The link button popup (showing input fields) displays behind the modal */
/* So we only need this if ckeditor is embedded in a modal */
:root {
    --ck-z-panel: calc(var(--ck-z-default, 100) + 9999);
}
</style>
