<template>
<div>
    <div
        v-if="defaultChecklists.length > 0"
        class="table-container"
    >
        <table class="table mt-2">
            <thead>
            <tr>
                <th>{{ ucFirst(translate("generic.functions")) }}</th>
                <th>{{ ucFirst(translate("generic.name")) }}</th>
                <th>{{ ucFirst(translate("generic.autoapply")) }}</th>
                <th>{{ ucFirst(translate("generic.nrofitems")) }}</th>
            </tr>
            </thead>
            <tbody class="scrollable-body">
            <tr
                v-for="(defaultChecklist, index) in defaultChecklists"
                :key="index"
            >
                <td>
                    <div class="btn-group" role="group" aria-label="functions">
                        <button
                            class="btn btn-primary btn-sm mr-1"
                            v-tooltip="translate('generic.edit')"
                            @click="defaultChecklistToEdit = defaultChecklist"
                        >
                            <font-awesome-icon icon="edit" />
                        </button>
                        <button
                            class="btn btn-danger btn-sm"
                            data-bs-toggle="modal"
                            data-bs-target="#del_areyousure"
                            v-tooltip="translate('generic.delete')"
                            @click="defaultChecklistIdToDelete = defaultChecklist.id"
                        >
                            <font-awesome-icon icon="trash" />
                        </button>
                    </div>
                </td>
                <td>
                    {{ defaultChecklist.name }}
                </td>
                <td>
                    {{ defaultChecklist.auto_add ? translate('generic.yes') : translate('generic.no') }}
                </td>
                <td>
                    {{  getNrOfItemsFilled(defaultChecklist) }}
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div v-else>
        <p>{{ translate('generic.nodefaultchcklists') }}</p>
    </div>
    <are-you-sure-4
        :button-text="ucFirst(translate('generic.deletedefaultchecklist'))"
        modal-id="del_areyousure"
        @confirmclicked="removeDefaultChecklist"
    />
</div>
</template>

<script setup>
import useLang from "../../composables/generic/useLang.js";
import useDefaultChecklists from "../../composables/useDefaultChecklists.js";
import AreYouSure from '../Layout/AreYouSure.vue';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate } = useLang();
const {
    defaultChecklists,
    defaultChecklistToEdit,
    defaultChecklistIdToDelete,
    getNrOfItemsFilled,
    removeDefaultChecklist
} = useDefaultChecklists();

</script>

<style scoped>
.table-container {
    position: relative;
    height: calc(100vh - 25rem);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
    display: block;
    overflow-y: auto;
    max-height: 100%;
}

thead {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 1;
    display: table;
    width: 100%;
    table-layout: fixed;
}

.scrollable-body {
    display: block;
    overflow-y: auto;
    height: calc(100% - 2.5rem);
}

.scrollable-body tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

/* Ensure columns stay aligned */
th, td {
    min-width: 9.375rem;
}

th:first-child, td:first-child {
    width: 8rem;
}
</style>
