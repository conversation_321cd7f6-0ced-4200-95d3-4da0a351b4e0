<template>
    <panel :busy="busy" extraClass="min-with-cards-2" panel-test-id="class-search-students">
        <template v-slot:title>
            <i class="fas fa-search"></i>
            {{ucFirst(translate('generic.directsearch'))}}
            {{translateChoice('generic.students',2)}} / {{translateChoice('generic.studentgroups',2)}}
        </template>
        <template v-slot:subtitle>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="onlyactive" v-model="onlyactive">
                <label class="form-check-label" for="onlyactive">{{ucFirst(translate('generic.onlyactive'))}}</label>
            </div>
        </template>
        <div class="mb-3">
            <div class="input-group searchbox mb-3">
                <span class="input-group-text" id="search-addon">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" :placeholder="translate('generic.studentname')"
                       aria-label="Searchbox" aria-describedby="search-addon"
                       v-model="studentSearchkey">
            </div>
        </div>
        <div class="table-fix-head">
            <table class="table table-striped table-sm">
                <thead class="table-light">
                <tr>
                    <th>{{ucFirst(translate('generic.name'))}}</th>
                    <th>
                        {{ucFirst(translate('generic.age'))}}
                        <div class="text-info text-truncate">(# {{translateChoice('generic.students', 2)}})</div>
                    </th>
                    <th>{{ucFirst(translateChoice('generic.courses', 2))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="student in filteredStudents" :key="student.id">
                    <!-- als het type is: studentgroup -->
                    <td>
                        <a v-if="student.learnertype === 'studentgroup'"
                           :href="'/studentgroups/' + student.id + '/edit'">
                            {{student.name}}
                        </a>
                        <a v-else :href="'/students/' + student.id + '/edit'">
                            {{student.name}}
                        </a>
                    </td>

                    <!-- als het type is: studentgroup -->
                    <!-- aantal leerlingen in de groep -->
                    <td v-if="student.learnertype === 'studentgroup'">
                        <span class="text-info">({{student.participants.length}})</span>
                    </td>
                    <td v-else>
                        {{student.age}}
                    </td>
                    <td style="white-space: nowrap; text-overflow:ellipsis; overflow: hidden;max-width: 450px">
                        {{student.mycourses}}
                    </td>
                </tr>
                <tr v-if="filteredStudents.length === 0">
                    <td colspan="3">{{ucFirst(translate('generic.nostudentsfound'))}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </panel>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import useLang from "../../composables/generic/useLang.js";
import useApi from "../../composables/generic/useApi.js";
import useToast from "../../composables/generic/useToast.js";

const { translate, translateChoice, ucFirst } = useLang();
const { apiGet } = useApi();
const { failToast } = useToast();

const busy = ref(false);
const studentSearchkey = ref('');
const onlyactive = ref(true);
const students = ref([]);

onMounted(() => {
    getStudents();
});

const getStudents = async () => {
    busy.value = true;
    try {
        const response = await apiGet('/api/students');
        students.value = response.data.data;
    } catch (err) {
        failToast(
            ucFirst(translate('generic.errorloadingstudents')) + ' ' + err
        );
    } finally {
        busy.value = false;
    }
};

const filteredStudents = computed(() => {
    if (onlyactive.value) {
        return students.value.filter(
            student =>
                student.name.toLowerCase().includes(studentSearchkey.value.toLowerCase()) &&
                    student.mycoursesarray.length > 0
        );
    } else {
        return students.value.filter(
            student => student.name.toLowerCase().includes(studentSearchkey.value.toLowerCase())
        );
    }
});
</script>

<style scoped lang="scss">
@use '../../../sass/tmpl3/variables' as *;
.table-fix-head {
    overflow-y: auto;
    height: 20rem;
    & thead th {
        position: sticky;
        top: -3px;
    }
}
</style>
