<template>
    <panel v-if="tutor">
        <template v-slot:title>
            {{ ucFirst(translate('generic.availability')) }}
        </template>
        <schedule-board
            v-if="tutor.id > 0"
            :tutor-id="tutor.id"
            maxnrofhours="4"
            :addtimeslicebtntitle="ucFirst(translate('generic.addtimeslice'))"
        ></schedule-board>
        <div v-else>
            {{ ucFirst(translate('generic.pleasesavefirst'))}}
        </div>
    </panel>
</template>

<script setup>
import useLang from '../../composables/generic/useLang.js';
import useEditTutor from '../../composables/useEditTutor.js';
import Panel from '../Layout/Panel.vue';
import ScheduleBoard from './ScheduleBoard.vue';

const { tutor } = useEditTutor();
const { ucFirst, translate } = useLang();
</script>

<style scoped>

</style>
