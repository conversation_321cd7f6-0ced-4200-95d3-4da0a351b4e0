<template>
    <div>
        <div class="row">
            <!-- canvas -->
            <div class="col-md-12">
                <canvas id="scheduleboard">
                    {{ translate('generic.canvasnotsupported') }}
                </canvas>
            </div>
        </div>
        <div class="row my-1">
            <div class="col-md-12">
                <button
                    class="btn btn-primary"
                    type="button"
                    data-bs-toggle="collapse"
                    data-bs-target="#detailsections"
                >
                    <font-awesome-icon icon="edit" />
                    {{ ucFirst(translate('generic.edit')) }}
                </button>
            </div>
        </div>
        <div class="collapse" id="detailsections">
            <div class="row">
                <!-- controls -->
                <div class="col-md-8">
                    <div class="row" v-for="(timeslice, index) in timeSlices" :key="index">
                        <div class="col-md-12">
                            <button @click.prevent="fillpopupfields(timeslice, 'edit')"
                                    data-bs-toggle="modal" data-bs-target="#entertimeslicepopup"
                                    class="btn btn-primary btn-sm"
                            >
                                <font-awesome-icon icon="edit" />
                            </button>
                            <button @click.prevent="fillpopupfields(timeslice, 'delete')"
                                    data-bs-toggle="modal" data-bs-target="#delete-timeslice-confirm"
                                    class="btn btn-danger btn-sm"
                            >
                                <font-awesome-icon icon="trash" />
                            </button>
                            {{ timeslice.fromTime.substring(0,5)}} - {{timeslice.endTime.substring(0,5) }}
                                ({{ transDaynames[timeslice.dayNumber-1] }})
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary btn-sm float-end"
                            @click.prevent="clearpopupfields"
                            data-bs-toggle="modal"
                            data-bs-target="#entertimeslicepopup"
                    >
                        <font-awesome-icon icon="plus" /> {{ addtimeslicebtntitle }}
                    </button>
                </div>
            </div>
        </div>

        <!-- popup enter timeslice specs -->
        <modal
            :popup-title="ucFirst(translate('generic.entertimeslice'))"
            :closetext="ucFirst(translate('generic.close'))"
            modal-id="entertimeslicepopup"
        >
            <div class="row">
                <div class="col-md-4">
                    <label for="daynumber">{{ucFirst(translate('generic.enterday'))}}: </label>
                    <select id="daynumber" v-model="chosenDay" class="form-control">
                        <option v-for="(dayName,index) in transDaynames" :value="(index+1)" :key="index">{{dayName}}</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="fromtime">{{ucFirst(translate('generic.enterfromtime'))}}: </label>
                    <input id="fromtime" class="form-control" required
                           v-model="chosenFromTime" :placeholder="translate('generic.hhmm')"
                    >
                </div>
                <div class="col-md-4">
                    <label for="endtime">{{ucFirst(translate('generic.entertotime'))}}: </label>
                    <input id="endtime" class="form-control" required
                           v-model="chosenEndTime" :placeholder="translate('generic.hhmm')"
                    >
                </div>
            </div>

            <div class="alert alert-danger alert-small mt-1" v-if="timeError !== ''">{{timeError}}</div>

            <template v-slot:okbutton>
                <button type="button" class="btn btn-success"
                        data-bs-dismiss="modal" @click.prevent="saveTimeslice" :disabled="timeError!==''"
                >
                    {{ ucFirst(translate('generic.save')) }}
                </button>
            </template>
        </modal>

        <!-- popup delete timeslice -->
        <modal
            :popup-title="ucFirst(translate('generic.areyousure'))"
            :closetext="ucFirst(translate('generic.cancel'))"
            modal-id="delete-timeslice-confirm">
            <div>
                <strong>{{ucFirst(translate('generic.youareabouttodeletetimeslice'))}}:</strong>
                <div class="alert alert-success">
                    {{transDaynames[deleteableTimeslice.dayNumber-1]}}:
                        {{deleteableTimeslice.fromTime.substring(0,5)}} - {{deleteableTimeslice.endTime.substring(0,5)}}
                </div>
            </div>
            <p>
                {{ucFirst(translate('generic.thiscannotbeundone'))}}
            </p>
            <template v-slot:okbutton>
                <button type="button" class="btn btn-danger"
                    data-bs-dismiss="modal" @click.prevent="deleteTimeslice">{{translate('generic.delete')}}
                </button>
            </template>
        </modal>
    </div>
</template>

<script setup>
/*
A timeslice is defined by
- a day number (1 .. number of daynames)
- a starttime
- an endtime
*/
import { ref, onMounted, watch } from 'vue';
import Modal from '../Layout/Modal.vue';
import _ from 'lodash';
import useLang from '../../composables/generic/useLang.js';
import useApi from '../../composables/generic/useApi.js';
import moment from 'moment';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

const { translate, ucFirst } = useLang();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

// Props
const props = defineProps({
    addtimeslicebtntitle: {
        type: String,
        required: true
    },
    maxnrofhours: {
        validator: (val) => {
            // only positive whole numbers, being at least 1
            const regex = /^[1-9]([0-9]+)?$/gm;
            return regex.exec(val);
        },
        required: true
    },
    tutorId: {
        type: Number,
        required: true
    }
});

// Reactive state
const canvas = ref(null);
const ctx = ref(null);
const startTime = ref(8);
const endTime = ref(23);
const canvasWidth = ref(800);
const canvasHeight = ref(325);
const lineHeight = ref(40); // for lines of text
const rowNamesWidth = ref(80); // left margin containing the rownames
const textpaddinghor = ref(2);
const textpaddingvert = ref(20);
const timeSlices = ref([]);
const dayrowYcoor = ref([]);
const timecolXcoor = ref([]);
const transDaynames = ref([]); // translated day names
const chosenDay = ref(0);
const chosenFromTime = ref('');
const chosenEndTime = ref('');
const editableTimeslice = ref(null);
const deleteableTimeslice = ref({ dayNumber: 0, fromTime: '', endTime: '' });
const blue = ref({ rgb: '55,152,207', hex: '#3798cf' });
const red = ref({ rgb: '189,83,48', hex: '#bd5330' });
const green = ref({ rgb: '49,184,154', hex: '#31b89a' });
const timeError = ref(''); // show user feedback if time entry is not complete or invalid

// Methods
/**
 * (re-)builds the complete board
 * this function is called initially plus everytime the number of timeslices or the content of the timeslices change
 */
const render = () => {
    if (endTime.value <= startTime.value) {
        console.log('endtime must be bigger than starttime');
        return;
    }
    // clear canvas for re-render
    ctx.value.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
    dayrowYcoor.value = [];

    // get dimensions to calculate with
    const nrOfFullHours = endTime.value - startTime.value;
    const nrOfPixelsOf1Hour = Math.floor((canvasWidth.value - rowNamesWidth.value) / nrOfFullHours);
    const nrOfPixelsOf1Minute = Math.abs(nrOfPixelsOf1Hour / 60);

    // building the raster of hours (vertical) ....
    for (let i = 0; i < nrOfFullHours; i++) {
        // vertical lines
        ctx.value.strokeStyle = `rgba(${red.value.rgb},0.9)`;
        ctx.value.beginPath();
        ctx.value.moveTo(i * nrOfPixelsOf1Hour + rowNamesWidth.value, 0);
        ctx.value.lineTo(i * nrOfPixelsOf1Hour + rowNamesWidth.value, canvasHeight.value);
        ctx.value.stroke();
        ctx.value.closePath();
        // time texts
        ctx.value.fillStyle = 'black';
        ctx.value.font = '12px Arial';
        const time = (i + startTime.value) + ':00';
        // save the hour x-coord for use in rendering timeslices
        timecolXcoor.value[i + startTime.value] = i * nrOfPixelsOf1Hour + rowNamesWidth.value;
        ctx.value.fillText(time, timecolXcoor.value[i + startTime.value] + textpaddinghor.value, textpaddingvert.value);
    }
    // ...and days (horizontal)
    for (let i = 1; i <= transDaynames.value.length; i++) {
        // seperator line (horizontal
        ctx.value.beginPath();
        ctx.value.moveTo(0, (i) * (lineHeight.value));
        ctx.value.lineTo(canvasWidth.value, (i) * (lineHeight.value));
        ctx.value.stroke();
        ctx.value.closePath();
        // rowtext
        ctx.value.fillStyle = 'black';
        ctx.value.font = '12px Arial';
        // save the height of the day-row for later use in timeslices
        dayrowYcoor.value[i] = ((i + 1) * lineHeight.value) - (Math.floor(lineHeight.value / 2));
        ctx.value.fillText(transDaynames.value[i - 1], (textpaddingvert.value / 2), dayrowYcoor.value[i]);
    }
    // headers
    // horizontal bar for column titles
    ctx.value.fillStyle = `rgba(${red.value.rgb},0.3)`;
    ctx.value.fillRect(0, 0, canvasWidth.value, lineHeight.value);
    // horizontal bar for column titles
    ctx.value.fillStyle = `rgba(${blue.value.rgb},0.3)`;
    ctx.value.fillRect(0, 0, 80, canvasHeight.value);

    // Last seperator line (horizontal), one more is the previous i plus 1 (but i is out of scope)
    const i = transDaynames.value.length;
    ctx.value.beginPath();
    ctx.value.moveTo(0, (i + 1) * (lineHeight.value));
    ctx.value.lineTo(canvasWidth.value, (i + 1) * (lineHeight.value));
    ctx.value.stroke();
    ctx.value.closePath();

    // render timeslices
    for (const timeSlice of timeSlices.value) {
        const timeCoor = getTimeCoor(timeSlice.fromTime, nrOfPixelsOf1Minute);
        const timeWidth = getTimeCoor(timeSlice.endTime, nrOfPixelsOf1Minute) - timeCoor;
        ctx.value.fillStyle = blue.value.hex;
        // render timeslice bar
        ctx.value.fillRect(timeCoor, (dayrowYcoor.value[timeSlice.dayNumber] - (lineHeight.value / 4)), timeWidth, lineHeight.value / 2);
    }
};

/**
 * Get current state from DB
 */
const getCurrentTimeSlicesFromDB = async () => {
    try {
        const resp = await apiGet(`/api/getAvailabilityOfTutor/${props.tutorId}`);
        // clear current content
        timeSlices.value = [];
        console.log(`received ${resp.data.length} timeslice(s)`);
        // [{id: 1, tutor_id: 3, day_number: 2, from_time: "10:00:00", to_time: "12:30:00",…}]
        resp.data.forEach((av) => {
            timeSlices.value.push({ tsId: av.id, dayNumber: av.day_number, fromTime: av.from_time, endTime: av.to_time });
        });
        render();
    } catch (error) {
        console.log('Error retrieving timeslices for tutor: ' + error);
    }
};

/**
 * ask user input for timeslice specs (daynr, start / end time)
 * and add to timeslices
 */
const saveTimeslice = async () => {
    try {
        // default: insert
        let url = '/api/addTimeslice';
        let method = 'post';
        let data = {
            chosenFromTime: chosenFromTime.value,
            chosenEndTime: chosenEndTime.value,
            dayNumber: chosenDay.value,
            tutorId: props.tutorId
        };

        // update or insert?
        if (editableTimeslice.value && editableTimeslice.value.tsId) {
            method = 'put';
            url = '/api/updateTimeslice';
            data = {
                chosenFromTime: chosenFromTime.value,
                chosenEndTime: chosenEndTime.value,
                dayNumber: chosenDay.value,
                tutorId: props.tutorId,
                timesliceId: editableTimeslice.value.tsId
            };
        }

        if (method === 'post') {
            await apiPost(url, data);
        } else {
            await apiPut(url, data);
        }

        await getCurrentTimeSlicesFromDB();
    } catch (error) {
        console.log('Error saving timeslice: ' + error);
    } finally {
        // reset
        editableTimeslice.value = null;
    }
};

/**
 * remove a timeslice. confirmation has already been asked
 */
const deleteTimeslice = async () => {
    if (deleteableTimeslice.value && deleteableTimeslice.value.tsId) {
        try {
            const url = '/api/deleteTimeslice';
            const data = {
                tutorId: props.tutorId,
                timesliceId: deleteableTimeslice.value.tsId
            };

            await apiDel(url, data);
            await getCurrentTimeSlicesFromDB();
        } catch (error) {
            console.log('Error deleting timeslice: ' + error);
        } finally {
            deleteableTimeslice.value = { dayNumber: 0, fromTime: '', endTime: '' };
        }
    }
};

/**
 * reset fields of pupup
 */
const clearpopupfields = () => {
    chosenDay.value = 0;
    chosenFromTime.value = '';
    chosenEndTime.value = '';
};

const fillpopupfields = (timeslice, mode) => {
    if (mode === 'edit') {
        editableTimeslice.value = timeslice;
        chosenDay.value = timeslice.dayNumber;
        chosenFromTime.value = timeslice.fromTime.substr(0, 5);
        chosenEndTime.value = timeslice.endTime.substr(0, 5);
    } else {
        deleteableTimeslice.value = timeslice;
        chosenDay.value = 0;
        chosenFromTime.value = '';
        chosenEndTime.value = '';
    }
};

/**
 * calculate the y-pixel to use for a certain time
 * @param time
 * @param nrOfPixelsOf1Minute
 */
const getTimeCoor = (time, nrOfPixelsOf1Minute) => {
    const parts = time.split(':');
    const minutes = parts[1];
    const hours = parts[0];
    // 09 should be 9 (as index for the array
    const hourIndex = (hours.substr(0, 1) === '0') ? hours.substr(1) : hours;
    if (timecolXcoor.value[hourIndex] === undefined) {
        const max = (timecolXcoor.value.length - 1);
        // fixme: too small or too big? now works only for too big
        console.log(`cant render ${hours}:${minutes}. too big? max= ${max} inserting biggest possible value.`);
        return canvasWidth.value;
    }
    return timecolXcoor.value[hourIndex] + (minutes * nrOfPixelsOf1Minute);
};

/**
 *
 * @param canvas
 * @param evt
 * @returns {{x: number, y: number}}
 */
const getMousePos = (canvas, evt) => {
    const rect = canvas.getBoundingClientRect();
    return {
        x: evt.clientX - rect.left,
        y: evt.clientY - rect.top
    };
};

/**
 *
 */
const checkTimeEntries = () => {
    let errorFrom = '';
    let errorEnd = '';
    let errorGen = '';

    // checks from time
    if (chosenFromTime.value === '') {
        errorFrom = translate('generic.timeisempty', { field: _.capitalize(translate('generic.from')) });
    } else {
        // check number
        if (!/^\d\d?:?\d?\d?$/gm.exec(chosenFromTime.value)) {
            errorFrom = translate('generic.chosentimeinvalidchar', { field: _.capitalize(translate('generic.from')) });
        } else {
            // we seem to have a valid time, check if is is before startTime
            const chosenFromHour = parseInt(chosenFromTime.value.split(':')[0]);
            if (chosenFromHour < startTime.value) {
                errorFrom = translate('generic.timeistooearly', { field: _.capitalize(translate('generic.from')) });
            }
        }
    }
    if ((errorFrom === '')) {
        if (!/^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/gm.exec(chosenFromTime.value)) {
            errorFrom = translate('generic.timeisnotvalid', { field: _.capitalize(translate('generic.from')) });
        }
    }
    // checks end time
    if (chosenEndTime.value === '') {
        errorFrom = translate('generic.timeisempty', { field: _.capitalize(translate('generic.until')) });
    } else {
        // check number
        if (!/^\d\d?:?\d?\d?$/gm.exec(chosenEndTime.value)) {
            errorEnd = translate('generic.chosentimeinvalidchar', { field: _.capitalize(translate('generic.until')) });
        }
    }

    if ((errorEnd === '')) {
        if (!/^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/gm.exec(chosenEndTime.value)) {
            errorEnd = translate('generic.timeisnotvalid', { field: _.capitalize(translate('generic.until')) });
        }
    }

    // if no errors so far: check if from < to
    if ((errorFrom === '') && (errorEnd === '')) {
        const fromtime = moment(chosenFromTime.value, 'HH:mm:ss'); // hour may only have 1 digit
        const endtime = moment(chosenEndTime.value, 'HH:mm:ss'); // hour may only have 1 digit
        if (fromtime > endtime) {
            errorGen = _.capitalize(translate('generic.fromtimegreaterthanendtime'));
        }
        if (chosenDay.value === 0) {
            errorGen = _.capitalize(translate('generic.pleaseselectaday'));
        }
    }

    if ((errorFrom !== '') && (errorEnd !== '')) {
        timeError.value = errorFrom + ', ' + errorEnd;
    } else if ((errorFrom === '') && (errorEnd !== '')) {
        timeError.value = errorEnd;
    } else {
        timeError.value = errorFrom;
    }
    // errorgen can only be filled if errorFrom and errorEnd are empty
    if (errorGen !== '') {
        timeError.value = errorGen;
    }
};

// Watchers
watch(chosenFromTime, () => {
    checkTimeEntries();
});

watch(chosenEndTime, () => {
    checkTimeEntries();
});

watch(chosenDay, () => {
    checkTimeEntries();
});

watch(() => props.tutorId, () => {
    getCurrentTimeSlicesFromDB();
});

// Lifecycle hooks
onMounted(() => {
    console.log('ScheduleBoard mounted.');
    const dayNames =
            `${ucFirst(translate('localisation.monday'))},
            ${ucFirst(translate('localisation.tuesday'))},
            ${ucFirst(translate('localisation.wednesday'))},
            ${ucFirst(translate('localisation.thursday'))},
            ${ucFirst(translate('localisation.friday'))},
            ${ucFirst(translate('localisation.saturday'))},
            ${ucFirst(translate('localisation.sunday'))},`;
    transDaynames.value = dayNames.split(',');
    // remove CRLF and other whitespace
    transDaynames.value.forEach((item, index, arr) => {
        arr[index] = item.trim();
    });
    canvas.value = document.getElementById('scheduleboard');
    canvas.value.width = canvasWidth.value;
    canvas.value.height = canvasHeight.value;
    canvas.value.style.width = canvasWidth.value;
    canvas.value.style.height = canvasHeight.value;
    ctx.value = canvas.value.getContext('2d');
    getCurrentTimeSlicesFromDB();
});
</script>

<style scoped>
#scheduleboard {
    border: solid #aaa 1px;
}
.mt-1 {
    margin-top: 1em;
}
.alert-small {
    padding-top: 2px;
    padding-bottom: 2px;
}
</style>
