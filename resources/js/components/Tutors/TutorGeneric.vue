<template>
    <div>
        <panel v-if="tutor">
            <template v-slot:title>
                {{ ucFirst(translate('generic.tutordata')) }}
            </template>
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.name'))} (${translate('generic.mandatory')})` }}</label>
                        <input type="text" class="form-control" v-model="tutor.name" required @input="dirty=true">
                    </div>
                </div>
                <div class="col-12 col-md-3">
                    <div class="mb-3">
                        <label>{{ ucFirst(translate('generic.telephone')) }}</label>
                        <input type="text" class="form-control" v-model="tutor.telephone" @input="dirty=true">
                    </div>
                </div>
                <div class="col-12 col-md-3">
                    <div class="mb-3">
                        <label>{{ ucFirst(translate('generic.telephoneextra')) }}</label>
                        <input type="text" class="form-control" v-model="tutor.telephone_extra" @input="dirty=true">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.email'))} (${translate('generic.mandatory')})` }}</label>
                        <input type="text" class="form-control" v-model="tutor.email" required @input="dirty=true">
                    </div>
                </div>
                <div class="col-12 col-md-3">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.hexcolor'))} (${translate('generic.mandatory')})` }}</label>
                        <div class="input-group mb-3">
                            <input class="form-control add-width add-cursor"
                                   type="color" v-model="tutor.hexcolor">
                            <input type="text" class="form-control" :value="tutor.hexcolor" readonly @input="dirty=true">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-3">
                    <div class="mb-3">
                        <label>{{ ucFirst(translate('generic.startdate')) }}</label>
                        <VueDatepicker
                            v-model="startDate"
                            v-bind="dpOptions"
                            @update:model-value="dirty=true"
                        />
                    </div>
                </div>
            </div>
            <!-- address fields: address_street_1, address_street_2, address_zipcode, address_city, address_country -->
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.streetline1'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_street_1" @input="dirty=true">
                    </div>
                </div>
                <div class="col-12 col-md-6">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.streetline2'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_street_2" @input="dirty=true">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-md-4">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.zipcode'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_zipcode" @input="dirty=true">
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.city'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_city" @input="dirty=true">
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    <div class="mb-3">
                        <label>{{ `${ucFirst(translate('generic.country'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_country" @input="dirty=true">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-4">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input
                                class="form-check-input"
                                type="checkbox"
                                :id="'tutorBlock_' + tutor.id"
                                v-model="tutor.is_blocked"
                                @change="dirty=true"
                            >
                            <label class="form-check-label" :for="'tutorBlock_' + tutor.id">
                                {{ tutor.is_blocked ? translate('generic.isblockedforloginclasse') : translate('generic.isnotblockedforloginclasse') }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    <div class="mb-3">
                        <label>{{ ucFirst(translate('generic.lastactivity')) }}</label>
                        <input type="text" class="form-control" :value="displayDateTime(tutor.last_active_at)" readonly>
                    </div>
                </div>
                <!-- only show end date field if this tutor has no future events -->
                <div class="col-12 col-md-4">
                    <div class="mb-3">
                        <label>{{ ucFirst(translate('generic.enddate')) }}</label>
                        <input
                            v-if="tutor.has_future_events"
                            :value="ucFirst(translate('generic.hasfutureevents'))"
                            class="form-control"
                            readonly
                        >
                        <VueDatepicker
                            v-else
                            v-model="endDate"
                            v-bind="dpOptions"
                            @update:model-value="dirty=true"
                        />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-md-3">
                    <div class="mb-3">
                        <button
                            :disabled="!canBeSaved"
                            @click="doSaveTutor"
                            class="btn btn-primary w-100"
                        >
                            <font-awesome-icon icon="save" />
                            {{ ucFirst(translate('generic.save'))}}
                        </button>
                    </div>
                </div>
                <div
                    v-if="!tutor.end_date && tutor.id > 0"
                    class="col-12 offset-md-6 col-md-3">
                    <div class="mb-3">
                        <button
                            data-bs-toggle="modal"
                            data-bs-target="#confirm-pw-reset"
                            class="btn btn-primary w-100"
                        >
                            <font-awesome-icon icon="key" />
                            {{ pwResetText }}
                        </button>
                    </div>
                </div>
            </div>
        </panel>
        <are-you-sure
            modal-id="confirm-pw-reset"
            :button-text="pwResetText"
            :confirm-text="ucFirst(translate('generic.explainpwreset'))"
            @confirmclicked="resetPassword"
        />
    </div>
</template>

<script setup>
import { computed, onUpdated, ref, watch, watchEffect } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import AreYouSure from '../Layout/AreYouSure.vue';
import Panel from '../Layout/Panel.vue';
import useLang from '../../composables/generic/useLang.js';
import useEditTutor from '../../composables/useEditTutor.js';
import useDatePicker from '../../composables/generic/useDatePicker.js';
import useToast from '../../composables/generic/useToast.js';
import useDateTime from "../../composables/generic/useDateTime.js";
import useUtils from "../../composables/generic/useUtils.js";
import useApi from '../../composables/generic/useApi.js';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { format, parse } from "date-fns";

const { scrollToSection } = useUtils();
const { saveTutor, tutor } = useEditTutor();
const { ucFirst, translate } = useLang();
const { dpOptions } = useDatePicker({ value: true });
const { successToast, failToast } = useToast();
const { displayDateTime } = useDateTime();
const { apiPost } = useApi();

const startDate = ref(null);
const endDate = ref(null);

/**
 * create a date-object from the incoming date string
 */
watchEffect(() => {
    startDate.value = tutor.value?.start_date
        ? parse(tutor.value.start_date, "yyyy-MM-dd", new Date())
        : null;
    endDate.value = tutor.value?.end_date
        ? parse(tutor.value.end_date, "yyyy-MM-dd", new Date())
        : null;
});

const doSaveTutor = () => {
    tutor.value.start_date = startDate.value
        ? format(startDate.value, "yyyy-MM-dd")
        : "";
    tutor.value.end_date = endDate.value
        ? format(endDate.value, "yyyy-MM-dd")
        : "";
    saveTutor();
};

// Call scrollToSection when the component is updated
onUpdated(() => {
    scrollToSection('layoutSidenav_content', 500);
});

const dirty = ref(false);

const canBeSaved = computed(() => {
    return tutor.value?.name.length > 3 &&
        tutor.value?.email.length > 6 &&
        tutor.value?.email.includes('@') &&
        tutor.value?.email.includes('.') &&
        tutor.value?.hexcolor.length === 7;
});

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});

const pwResetText = computed(() => {
    return tutor.value?.has_password
        ? ucFirst(translate('generic.resetpassword'))
        : ucFirst(translate('generic.createpassword'));
});

const resetPassword = async () => {
    try {
        await apiPost('/api/resetpassword', { tutorid: tutor.value?.id });
        successToast('gelukt!', ucFirst(translate('generic.success')));
    } catch (err) {
        failToast('Fout: ' + err, ucFirst(translate('generic.fail')));
    }
};
</script>

<style scoped>
.add-width {
    width: 3.5rem;
}
.add-cursor {
    cursor: pointer;
}
</style>
