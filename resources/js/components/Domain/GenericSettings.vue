<template>
    <panel :busy="busy">
        <template v-slot:title>
            {{ ucFirst(translate('generic.domainsettings')) }}<br/>
        </template>
        <template v-if="!busy && domainData?.domainName">
            <!-- ROW 1            -->
            <div class="d-flex flex-wrap">
                <div class="flex-grow-1 me-2 mb-3">
                    <!-- domain name -->
                    <label for="domain_name" class="form-label">{{ ucFirst(translate('generic.domainname')) }}</label>
                    <input class="form-control"
                           name="domain_name"
                           id="domain_name"
                           type="text"
                           v-model="domainData.domainName"
                           required
                    />
                </div>
                <div class="flex-grow-1 me-2 mb-3">
                    <!-- school website -->
                    <label for="school_website" class="form-label">{{ ucFirst(translate('generic.schoolwebsite')) }}</label>
                    <input class="form-control"
                           name="school_website"
                           id="school_website"
                           type="text"
                           v-model="domainData.websiteUrl"
                    />
                </div>
                <div class="flex-grow-1 mb-3">
                    <!-- default password -->
                    <label for="default_password" class="form-label">{{ ucFirst(translate('generic.defaultpassword')) }}</label>
                    <input class="form-control"
                           name="default_password"
                           id="default_password"
                           type="text"
                           v-model="domainData.defaultPassword"
                           disabled
                    />
                </div>
            </div>
            <!-- ROW 2            -->
            <div class="d-flex flex-wrap">
                <div class="flex-grow-1 me-2 mb-3">
                    <!-- warn birtday students -->
                    <label for="warn_birthday_students" class="form-label">
                        {{ ucFirst(translate('generic.warnbeforebirthdaystudents')) }}
                        <span v-tooltip="{ content: translate('generic.explainwarnbeforebirthday'), html: true }">
                            <i class="fas fa-question-circle"></i>
                        </span>
                    </label>
                    <input class="form-control"
                           name="warn_birthday_students"
                           id="warn_birthday_students"
                           type="text"
                           v-model="domainData.warnBeforeBirthday"
                    />
                </div>
                <div class="flex-grow-1 me-2 mb-3">
                    <!-- warn vat liable days -->
                    <label for="warn_vat_liable_days" class="form-label">
                        {{ ucFirst(translate('generic.warnbeforeadultstudents')) }}
                        <span v-tooltip="{ content: translate('generic.explainwarnbeforeadult'), html: true }">
                            <i class="fas fa-question-circle"></i>
                        </span>
                    </label>
                    <input class="form-control"
                           name="warn_vat_liable_days"
                           id="warn_vat_liable_days"
                           type="text"
                           v-model="domainData.warnBeforeAdult"
                    />
                </div>
                <div class="flex-grow-1 me-2 mb-3">
                    <!--  day for availability -->
                    <label for="day_for_availability" class="form-label">
                        {{ ucFirst(translate('generic.schedulethreshold')) }}
                        <span v-tooltip="{ content: translate('generic.explainschedulethreshold'), html: true }">
                            <i class="fas fa-question-circle"></i>
                        </span>
                    </label>
                    <input class="form-control"
                           name="day_for_availability"
                           id="day_for_availability"
                           type="text"
                           v-model="domainData.scheduleThreshold"
                    />
                </div>
                <div class="flex-grow-1 me-2 mb-3">
                    <!-- age adult -->
                    <label for="age_adult" class="form-label">
                        {{ ucFirst(translate('generic.adultthreshold')) }}
                        <span v-tooltip="{ content: translate('generic.explainadultthreshold'), html: true }">
                            <i class="fas fa-question-circle"></i>
                        </span>
                    </label>
                    <input class="form-control"
                           name="age_adult"
                           id="age_adult"
                           type="text"
                           v-model="domainData.adultThreshold"
                           required
                    />
                </div>
                <div class="flex-grow-1 mb-3">
                    <!-- course tax adults -->
                    <label for="course_tax_adults" class="form-label">
                        {{ ucFirst(translate('generic.coursetaxrateadults')) }}
                        <span v-tooltip="{ content: translate('generic.explaincoursetaxrateadults'), html: true }">
                            <i class="fas fa-question-circle"></i>
                        </span>
                    </label>
                    <input class="form-control"
                           name="course_tax_adults"
                           id="course_tax_adults"
                           type="text"
                           v-model="domainData.courseTaxRate"
                           required
                    />
                </div>
            </div>
            <!-- ROW 6            -->
            <div class="d-flex flex-wrap">
                <div class="flex-grow-1 me-2 mb-3">
                    <!--  rates & conditions url -->
                    <label
                        for="rates_conditions_url" class="form-label">{{
                            ucFirst(translate('generic.schoolratesandconditionsurl'))
                        }}</label>
                    <input class="form-control"
                           name="rates_conditions_url"
                           id="rates_conditions_url"
                           type="text"
                           v-model="domainData.ratesConditionsUrl"
                    />
                </div>
                <div class="flex-grow-1 me-2 mb-3">
                    <!--  privacy statement url -->
                    <label
                        for="privacy_statement_url" class="form-label">{{
                            ucFirst(translate('generic.schoolprivacystatementurl'))
                        }}</label>
                    <input class="form-control"
                           name="privacy_statement_url"
                           id="privacy_statement_url"
                           type="text"
                           v-model="domainData.privacyUrl"
                    />
                </div>
                <div class="flex-grow-1 me-2 mb-3">
                    <!--  allowed ip addresses -->
                    <label for="allowed_ip_addresses" class="form-label">
                        {{ ucFirst(translate('generic.allowedipaddressesbroadcast')) }}
                        <span v-tooltip="{ content: translate('generic.explainallowedipadd'), html: true }">
                            <i class="fas fa-question-circle"></i>
                        </span>
                    </label>
                    <input class="form-control"
                           name="allowed_ip_addresses"
                           id="allowed_ip_addresses"
                           type="text"
                           v-model="domainData.allowedIpAddresses"
                    />
                </div>
                <div class="flex-grow-1 me-2 mb-3">
                    <!--  Default domain language -->
                    <label for="default_domain_language" class="form-label">
                        {{ ucFirst(translate('localisation.defaultdomainlanguage')) }}
                    </label>
                    <select
                        class="form-select"
                        v-model="domainData.language"
                        id="default_domain_language"
                    >
                        <option
                            v-for="(lang, index) in validLanguages"
                            :key="index"
                            :value="lang.code"
                        >
                            {{ lang.label }}
                        </option>
                    </select>
                </div>
            </div>
        </template>
    </panel>
</template>

<script setup>
import { computed } from "vue";
import useDomain from "../../composables/useDomain.js";
import useLang from "../../composables/generic/useLang.js";
import Panel from "../Layout/Panel.vue";

const { ucFirst, translate } = useLang();
const { busy, domainData, validLanguages } = useDomain();

const currenLangLabel = computed(() => {
    if (domainData.value?.language?.length > 0) {
        const lang = validLanguages.find(lang => lang.code === domainData.language);
        return lang ? lang.label : 'test';
    } else {
        return 'please select a language';
    }
});
</script>

<style scoped>
</style>
