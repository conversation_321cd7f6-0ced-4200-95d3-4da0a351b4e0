<template>
    <show-course-group-list
        :course-groups="filteredCourseGroups"
        :title-small="ucFirst(translate('generic.explaincoursegroup')) +
            '&nbsp;' + ucFirst(translate('generic.groupscanonlybedeletedif'))"
        @getTutorsForCourseGroup="getTutorsForCourseGroup"
    />
    <show-course-group-list
        v-if="emptyCourseGroups.length > 0"
        :course-groups="emptyCourseGroups"
        :title-small="ucFirst(translate('generic.nocoupledcourses'))"
        @setCGIDToDelete="setCGIDToDelete"
        @getTutorsForCourseGroup="getTutorsForCourseGroup"
    />

    <!-- -->
    <are-you-sure
        modal-id="confirm-delete-cg"
        @confirmclicked="deleteCourseGroup"
    />

    <!-- pick tutors who are able to teach lessons in this course group -->
    <modal
        closetext="close"
        size="large"
        :popup-title="
            ucFirst(translate('generic.choosetutor')) + ' ' +
            translate('generic.for') + ' ' +
            activeCourseGroupName
        "
        modal-id="choosetutorpopup"
    >
        <table class="table table-sm">
            <thead>
            <tr>
                <th rowspan="2">{{ ucFirst(translateChoice('generic.tutors', 1)) }}</th>
                <th colspan="3" class="text-center">{{ ucFirst(translate('generic.agegroup')) }}</th>
            </tr>
            <tr>
                <td class="text-center mx-2">{{ translateChoice('generic.children', 2) }}</td>
                <td class="text-center mx-2">{{ translateChoice('generic.adolescents', 2) }}</td>
                <td class="text-center mx-2">{{ translateChoice('generic.adults', 2) }}</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(tutor, index) in assocTutors"
                :key="tutor.id"
            >
                <td>
                    <div class="form-check">
                        <input :id="'cb'+tutor.id"
                            class="form-check-input"
                            type="checkbox"
                            @click.prevent="toggleTutorForCourseGroup($event, tutor.id)"
                            v-model="tutor.assoc"
                        >
                        <label class="form-check-label" :for="'cb'+tutor.id">{{ tutor.name }}</label>
                    </div>
                </td>
                <td class="text-center">
                    <div class="form-check d-flex justify-content-center">
                        <input
                            class="form-check-input"
                            type="checkbox"
                            v-model="tutor.child"
                            @click="setTutorCheckbox($event, index, tutor.id, 'child')"
                            :id="'tutor_child_' + tutor.id"
                        >
                    </div>
                </td>
                <td class="text-center">
                    <div class="form-check d-flex justify-content-center">
                        <input
                            class="form-check-input"
                            type="checkbox"
                            v-model="tutor.adolescent"
                            @click="setTutorCheckbox($event, index, tutor.id, 'adolescent')"
                            :id="'tutor_adolescent_' + tutor.id"
                        >
                    </div>
                </td>
                <td class="text-center">
                    <div class="form-check d-flex justify-content-center">
                        <input
                            class="form-check-input"
                            type="checkbox"
                            v-model="tutor.adult"
                            @click="setTutorCheckbox($event, index, tutor.id, 'adult')"
                            :id="'tutor_adult_' + tutor.id"
                        >
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </modal>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import ShowCourseGroupList from './ShowCourseGroupList.vue';
import Modal from '../Layout/Modal.vue';
import useLang from '../../composables/generic/useLang.js';
import useToast from '../../composables/generic/useToast.js';
import AreYouSure from '../Layout/AreYouSure.vue';
import useBaseData from '../../composables/generic/useBaseData.js';
import useApi from '../../composables/generic/useApi.js';

const { ucFirst, translate, translateChoice } = useLang();
const { failToast, successToast } = useToast();
const { apiGet, apiPut, apiDel } = useApi();
const assocTutors = ref([]);
const activeCourseGroupId = ref(0);
const CGIDToDelete = ref(0);
const { allCourseGroups, emptyCourseGroups, filteredCourseGroups, initBaseData } = useBaseData();

const activeCourseGroupName = computed(() => {
    return allCourseGroups.value.find(cg => cg.id === activeCourseGroupId.value)?.name;
});

onMounted(async () => {
    await updateCourseGroups();
});

const setCGIDToDelete = (cgid) => {
    CGIDToDelete.value = cgid;
};

const deleteCourseGroup = async () => {
    try {
        await apiDel('/api/coursegroups/' + CGIDToDelete.value);
        successToast(ucFirst(translate('generic.coursegroupdeleted')));
        // re-retrieve new list course groups from the backend
        await updateCourseGroups();
    } catch (error) {
        failToast(translate('generic.error') + ': ' + error);
    }
};

/**
 * Gets the tutors of the requested course group including the associated age groups
 * @param courseGroupId
 */
const getTutorsForCourseGroup = async (courseGroupId) => {
    try {
        const response = await apiGet(`/api/gettutorsforcoursegroup/${courseGroupId}`);
        // Convert integer values (1) to boolean values for checkboxes
        assocTutors.value = response.data.map(tutor => ({
            ...tutor,
            child: tutor.child === 1 || tutor.child === true,
            adolescent: tutor.adolescent === 1 || tutor.adolescent === true,
            adult: tutor.adult === 1 || tutor.adult === true
        }));
        activeCourseGroupId.value = courseGroupId;
    } catch (error) {
        failToast(translate('generic.error') + ': ' + error);
    }
};

/**
 * set/unset the checkbox of a tutor to the value of the clicked checkbox
 * This checks all age groups of the coursegroup for this the tutor
 * @param event
 * @param tutorId
 */
const toggleTutorForCourseGroup = async (event, tutorId) => {
    const toggleVal = event.target.checked;
    try {
        await apiPut('/api/toggletutorforcoursegroup/', {
            coursegroupid: activeCourseGroupId.value,
            tutorid: tutorId,
            toggleval: toggleVal
        });
        await updateCourseGroups(); // update main screen
        await getTutorsForCourseGroup(activeCourseGroupId.value);
        successToast(ucFirst(translateChoice('generic.tutorattached', toggleVal)));
    } catch (error) {
        failToast(translate('generic.error') + ': ' + error);
    }
};

/**
 * attach or detach a tutor because one of 'child', 'adolescent' or 'adult' was clicked
 * on a not-attached tutor
 *
 * @param tutorId int
 * @param ageGroup string {'child', 'adolescent', 'adult'}
 */
const toggleTutorForCourseGroupOn = async (tutorId, ageGroup) => {
    try {
        await apiPut('/api/toggletutorforcourseandcheck/', {
            coursegroupid: activeCourseGroupId.value,
            tutorid: tutorId,
            agegroup: ageGroup
        });
        await updateCourseGroups();
        await getTutorsForCourseGroup(activeCourseGroupId.value);
    } catch (error) {
        failToast(translate('generic.error') + ': ' + error);
        console.log(
            `Error saving tutor-toggle: ON for coursegroup ${activeCourseGroupId.value}, for agegroup: ${ageGroup}: ${error}`
        );
    }
};

/**
 * Handle click on age group checkboxes
 * @param event
 * @param index int
 * @param tutorId int
 * @param ageGroup string {'child', 'adolescent', 'adult'}
 */
const setTutorCheckbox = async (event, index, tutorId, ageGroup) => {
    const toggleVal = event.target.checked;
    // if toggleVal = 'on' and the tutor is not yet associated, trigger toggleTutorForCourseGroup
    if (toggleVal && !(assocTutors.value[index].assoc)) {
        await toggleTutorForCourseGroupOn(tutorId, ageGroup);
    } else if (assocTutors.value[index].assoc) {
        // if tutor already assoc, toggle the age group on of off
        try {
            await apiPut('/api/toggleagegroupfortutor', {
                coursegroupid: activeCourseGroupId.value,
                tutorid: tutorId,
                toggleval: toggleVal,
                agegroup: ageGroup
            });
            await updateCourseGroups(); // possibly update main screen
            await getTutorsForCourseGroup(activeCourseGroupId.value);
        } catch (error) {
            failToast(`Error saving tutor-toggle: ON for coursegroup ${activeCourseGroupId.value}, for agegroup: ${ageGroup}: ${error}`,
                translate('generic.error') + ': ' + error);
        }
    }
};

const updateCourseGroups = async () => {
    await initBaseData({
        courseGroups: true
    }, true);
}

</script>

<style scoped>

</style>
