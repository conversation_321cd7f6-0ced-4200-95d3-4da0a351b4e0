<template>
<Panel>
    <template #title>
        <h3>{{ uc<PERSON>irst(translateChoice("generic.coupledcourses", 2)) }}</h3>
    </template>
    <table class="table table-striped">
        <thead>
        <tr>
            <th>{{ ucFirst(translate("generic.name")) }}</th>
            <th>{{ ucFirst(translateChoice("generic.recurrences", 1)) }}</th>
            <th>{{ ucFirst(translate("generic.istrialcourse")) }}</th>
            <th>{{ ucFirst(translate("generic.nrofactivestudents")) }}</th>
            <th>{{ ucFirst(translate("generic.nrofstudents")) }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="course in coursesOnCourseGroup" :key="course.id">
            <td><a :href="`/courses/${course.id}/edit`">{{ course.name }}</a></td>
            <td>{{ course.recurrenceoption.description }}</td>
            <td>{{ course.is_trial_course ? translate("generic.yes") : translate("generic.no") }}</td>
            <td>{{ course.current_students?.length }}</td>
            <td>{{ course.students?.length }}</td>
        </tr>
        </tbody>
    </table>
</Panel>
</template>

<script setup>
import Panel from "../Layout/Panel.vue";
import useCourseGroups from "../../composables/useCourseGroups.js";
import useLang from "../../composables/generic/useLang.js";

const { coursesOnCourseGroup } = useCourseGroups();
const { translate, translateChoice, ucFirst } = useLang();

</script>

<style scoped>

</style>
