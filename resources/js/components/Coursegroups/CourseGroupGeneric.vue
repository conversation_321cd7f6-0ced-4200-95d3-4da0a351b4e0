<template>
    <Panel>
        <template #title>
            <h3>{{ ucFirst(translate("generic.coursegroup")) }}</h3>
        </template>
        <template #subtitle>
            <small v-html="ucFirst(translate('generic.explaincoursegroup'))"/>
        </template>
        <!-- Bootstrap 5 flex layout -->
        <div class="d-flex flex-column flex-md-row">
            <!-- First column: name and webdescription -->
            <div class="flex-grow-1 me-md-3 mb-3 mb-md-0">
                <div class="mb-3">
                    <label for="name" class="form-label">{{ ucFirst(translate("generic.name")) }}</label>
                    <input type="text" class="form-control" id="name" v-model="name" required @keyup="dirty=true">
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">{{ ucFirst(translate("generic.webdescription")) }}</label>
                    <ckeditor
                        v-if="ClassicEditor && editorConfigSimple"
                        v-model="webDescription"
                        :editor="ClassicEditor"
                        :config="editorConfigSimple"
                        @input="dirty=true"
                    />
                </div>
            </div>
            
            <!-- Second column: switches -->
            <div class="flex-shrink-0" style="min-width: 250px;">
                <label for="name" class="form-label">Settings</label>
                <!-- isTrialGroup -->
                <div class="form-check form-switch mb-3">
                    <input
                        id="isTrialGroup"
                        class="form-check-input"
                        type="checkbox"
                        v-model="isTrialGroup"
                        @change="dirty=true"
                    >
                    <label class="form-check-label" for="isTrialGroup">
                        {{ ucFirst(translate('generic.istrialgroup')) }} ➞ {{ isTrialGroup ? translate('generic.yes') : translate('generic.no') }}
                    </label>
                    <small v-if="isTrialGroup" class="text-muted d-block mt-1">
                        {{ ucFirst(translate("generic.explaintrialsettrue")) }}
                    </small>
                </div>
                
                <!-- ignoreForPriceList -->
                <div class="form-check form-switch">
                    <input
                        id="ignoreForPriceList"
                        class="form-check-input"
                        type="checkbox"
                        v-model="ignoreForPriceList"
                        @change="dirty=true"
                    >
                    <label class="form-check-label" for="ignoreForPriceList">
                        {{ ucFirst(translate('generic.ignoreForPriceList')) }} ➞ {{ ignoreForPriceList ? translate('generic.yes') : translate('generic.no') }}
                    </label>
                    <small v-if="ignoreForPriceList" class="text-muted d-block mt-1">
                        {{ ucFirst(translate("generic.explainignoreforwebtrue")) }}
                    </small>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ClassicEditor } from 'ckeditor5';
import 'ckeditor5/ckeditor5.css';
import useConfigItems from '../../composables/generic/useConfigItems.js';
import useLang from "../../composables/generic/useLang.js";
import useCourseGroups from "../../composables/useCourseGroups.js";

const { editorConfigSimple, isLayoutReady } = useConfigItems();
const { ucFirst, translate } = useLang();
const { name, ignoreForPriceList, isTrialGroup, webDescription } = useCourseGroups();

// const webDescription1 = ref('');
const dirty = ref(false);

onMounted(() => {
    isLayoutReady.value = true;
});

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});
</script>

<style>
.ck-editor__editable:not(.ck-editor__nested-editable) {
    min-height: 200px;
}
</style>
