<template>
    <div>
        <Panel>
            <template #title>
                {{ ucFirst(translateChoice('generic.schoolyears')) }}
            </template>
            <template #subtitle>
                <button class="btn btn-primary" @click="schoolYearIDToEdit = 0">
                    {{ ucFirst(translate('generic.newschoolyear')) }}
                </button>
            </template>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>{{ ucFirst(translate('generic.functions')) }}</th>
                        <th>{{ ucFirst(translate('generic.label')) }}</th>
                        <th>{{ ucFirst(translate('generic.startdate')) }}</th>
                        <th>{{ ucFirst(translate('generic.enddate')) }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="schoolYear in allSchoolYears" :key="schoolYear.id">
                        <td>
                            <div class="btn-group" role="group" aria-label="functions">
                                <button
                                    class="btn btn-primary btn-sm mr-1"
                                    v-tooltip="translate('generic.edit')"
                                    @click="schoolYearIDToEdit = schoolYear.id"
                                >
                                    <font-awesome-icon icon="edit" />
                                </button>
                                <button
                                    class="btn btn-danger btn-sm"
                                    data-bs-toggle="modal"
                                    data-bs-target="#confirm-delete-schoolyear"
                                    :disabled="schoolYear.timetables_count > 0"
                                    v-tooltip="translate('generic.delete')"
                                    @click="schoolYearIDToDelete = schoolYear.id"
                                >
                                    <font-awesome-icon icon="trash" />
                                </button>
                            </div>
                        </td>
                        <td>{{ schoolYear.label }}</td>
                        <td>{{ displayDate(schoolYear.start_date) }}</td>
                        <td>{{ displayDate(schoolYear.end_date) }}</td>
                    </tr>
                </tbody>
            </table>
        </Panel>
        <AreYouSure
            :button-text="ucFirst(translate('generic.delete'))"
            modal-id="confirm-delete-schoolyear"
            @confirmclicked="deleteSchoolYear(schoolYearIdToDelete)"
        />
    </div>
</template>

<script setup>
import Panel from "../Layout/Panel.vue";
import AreYouSure from "../Layout/AreYouSure.vue";
import useLang from "../../composables/generic/useLang.js";
import useBaseData from "../../composables/generic/useBaseData.js";
import useDateTime from "../../composables/generic/useDateTime.js";
import useSchoolYear from "../../composables/useSchoolYear.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { translate, translateChoice, ucFirst } = useLang();
const { allSchoolYears } = useBaseData();
const { displayDate } = useDateTime();
const { deleteSchoolYear, schoolYearIDToDelete, schoolYearIDToEdit } = useSchoolYear();
</script>

<style scoped>
.table th:first-child {
    width: 8%;
}
/* label */
.table th:nth-child(2) {
    width: 18%;
}

.table th:nth-child(3) {
    width: 10%;
}

.table th:nth-child(4) {
    width: 64%;
}
</style>
