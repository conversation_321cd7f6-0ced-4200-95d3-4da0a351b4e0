<template>
    <div class="card">
        <div class="card-header">
            <h5>{{ ucFirst(translate('generic.overviewgroups')) }}</h5>
        </div>
        <div class="card-body">
            <div v-if="groupTimetables.length === 0 && !busy" class="alert alert-info">
                {{ ucFirst(translate('generic.nogroupsfound')) }}
            </div>
            <div v-else>
                <div class="row font-weight-bold mb-2">
                    <div class="col-md-2">{{ ucFirst(translate('generic.name')) }}</div>
                    <div class="col-md-2">{{ ucFirst(translate('generic.course')) }}</div>
                    <div class="col-md-3">{{ ucFirst(translateChoice('generic.recurrences', 1)) }}</div>
                    <div class="col-md-5">{{ ucFirst(translateChoice('generic.events', 2)) }}</div>
                </div>
                <timetable-row
                    v-for="timetable in groupTimetables"
                    :key="timetable.id"
                    :timetable="timetable"
                    :is-group="true"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import useLang from "../../composables/generic/useLang.js";
import useTimetableReport from "../../composables/useTimetableReport.js";
import TimetableRow from "./TimetableRow.vue";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, groupTimetables } = useTimetableReport();
</script>

<style scoped>
/* Any component-specific styles can go here */
</style>
