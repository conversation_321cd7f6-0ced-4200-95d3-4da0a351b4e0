<template>
    <panel :busy="busy" :status="panelStatus" class="h-100">
        <template v-slot:title>
            <i :class="['fa', icon]"></i>
            {{ paneltitle }}
        </template>
        <template v-if="filteredData.length > 0">
            <table class="table">
                <tr v-for="item in filteredData" :key="item.entity_id">
                    <td v-if="item.showas === 'alert'" class="text-danger">
                        <span v-tooltip="translate('generic.highpriority')">
                            <i class="fas fa-exclamation-triangle"></i>
                        </span>
                    </td>
                    <td v-else>
                        &nbsp;
                    </td>

                    <td>{{ item.message }}</td>
                    <td><a :href="`/students/${item.student.id}/edit`">{{ item.student.name }}</a></td>
                    <td><a :href="item.link">{{ ucFirst(translate('generic.solutionlink')) }}</a></td>
                </tr>
            </table>
        </template>
        <template v-else>
            {{ ucFirst(translate('generic.noalerts')) }}
        </template>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import useLang from '../../composables/generic/useLang.js';
import useApi from '../../composables/generic/useApi.js';
import useToast from '../../composables/generic/useToast.js';
import Panel from '../Layout/Panel.vue';

const props = defineProps({
    token: {
        type: String,
        default: ''
    },
    paneltitle: {
        type: String,
        required: true
    },
    subject: {
        type: String,
        required: true
    },
    icon: {
        type: String,
        default: 'fa-bell'
    }
});

const data = ref([]);
const busy = ref(false);
const { translate, ucFirst } = useLang();
const { apiGet } = useApi();
const { failToast } = useToast();

onMounted(async () => {
    try {
        busy.value = true;
        const response = await apiGet(`api/singlealertstatus?subject=${props.subject}`);
        data.value = response.data;
    } catch (error) {
        failToast(error);
    } finally {
        busy.value = false;
    }
});

/**
 * remove status and error elements
 * @type {ComputedRef<*>}
 */
const filteredData = computed(() => {
    return data.value?.status
        ? data.value.status
        : [];
});

/**
 * Determine the panel status based on whether there's data to show
 * @type {ComputedRef<string>}
 */
const panelStatus = computed(() => {
    return filteredData.value.length > 0 ? 'danger' : 'success';
});
</script>

<style scoped>

</style>
