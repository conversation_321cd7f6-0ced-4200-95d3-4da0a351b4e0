import useLang from '../../composables/generic/useLang.js';

const { translate, ucFirst } = useLang();

export default class PlaceholdersPlugin {
    constructor(editor) {
        this.editor = editor;
        this.placeholderItems = [
            { id: 'studentfullname', label: ucFirst(translate('generic.studentname')), value: '%studentfullname%' },
            { id: 'studentfirstname', label: ucFirst(translate('generic.studentfirstname')), value: '%studentfirstname%' },
            { id: 'studentaccesslink', label: ucFirst(translate('generic.studentaccesslink')), value: '%studentaccesslink%' },
            { id: 'salutationforfinancial', label: ucFirst(translate('generic.salutationforfinancial')), value: '%salutationforfinancial%' },
            { id: 'salutationforplanning', label: ucFirst(translate('generic.salutationforplanning')), value: '%salutationforplanning%' },
            { id: 'salutationforpromotion', label: ucFirst(translate('generic.salutationforpromotion')), value: '%salutationforpromotion%' },
            { id: 'schoollogo', label: ucFirst(translate('generic.schoollogo')), value: "%schoollogo|width='100'%" },
            { id: 'schoolname', label: ucFirst(translate('generic.schoolname')), value: '%schoolname%' },
            { id: 'schooltelephone', label: ucFirst(translate('generic.schooltelephone')), value: '%schooltelephone%' },
            { id: 'schoolcontactperson', label: ucFirst(translate('generic.schoolcontactperson')), value: '%schoolcontactperson%' },
            { id: 'schoolwebsite', label: ucFirst(translate('generic.schoolwebsite')), value: '%schoolwebsite%' }
        ];
        this.init();
    }

    init() {
        const editor = this.editor;
        
        editor.ui.componentFactory.add('placeholders', () => {
            // Create a container for the dropdown
            const container = document.createElement('div');
            container.className = 'ck ck-dropdown';

            // Create a select element
            const select = document.createElement('select');
            select.className = 'ck ck-button ck-dropdown__select';
            
            // Add a default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = ucFirst(translate('email.mailanchors'));
            defaultOption.selected = true;
            defaultOption.disabled = true;
            select.appendChild(defaultOption);
            
            // Add placeholder options
            this.placeholderItems.forEach(item => {
                const option = document.createElement('option');
                option.value = item.value;
                option.textContent = item.label;
                select.appendChild(option);
            });

            // Handle selection
            select.addEventListener('change', () => {
                const value = select.value;
                editor.model.change(writer => {
                    const insertPosition = editor.model.document.selection.getFirstPosition();
                    editor.model.insertContent(writer.createText(value), insertPosition);
                });
                select.selectedIndex = 0; // Reset to default option
            });

            container.appendChild(select);

            // Implement the expected interface
            return {
                element: container,
                render: () => {
                    return container;
                },
                destroy: () => {
                    container.remove();
                }
            };
        });
    }
} 
