/* *************************************************************
 * This file focuses solely on Vue application initialization
 * and configuration
 * ************************************************************* */
import { createApp } from 'vue';
import FloatingVue from 'floating-vue';
import 'floating-vue/dist/style.css';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { components } from './generated/component-imports.js';
import { initLogoutButton } from './globals.js';
import './globals.js';

// if we have signregistration in the URL: don't load Vue!
if (window.location.href.includes('signregistration')) {
    initLogoutButton();
} else {
    const app = createApp({
        mounted() {
            console.log('CLASS loaded');
        }
    });

    // Register components
    Object.entries(components).forEach(([name, component]) => {
        app.component(name, component);
    });

    // Use plugins
    app.use(FloatingVue, {
        themes: {
            'tooltip': {
                placement: 'top',
                delay: { show: 500, hide: 0 }
            }
        }
    });
    app.component('VueDatepicker', VueDatepicker);

    // Mount the app
    app.mount('#vuecontext');
    
    // Initialize logout button after Vue is mounted
    initLogoutButton();
}
