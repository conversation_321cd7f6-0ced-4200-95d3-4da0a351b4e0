import { computed, ref } from "vue";
import useApi from "./generic/useApi.js";
import useLang from "./generic/useLang.js";
import useToast from "./generic/useToast.js";

const newMode = ref(true);
const courseGroupId = ref(0);
const name = ref('');
const isTrialGroup = ref(false);
const ignoreForPriceList = ref(false);
const webDescription = ref('');
const coursesOnCourseGroup = ref([]);

const { ucFirst, translate } = useLang();
const { successToast, failToast } = useToast();
const { apiGet, apiPost, apiPut } = useApi();

export default function useCourseGroups() {

    const initCourseGroupData = async () => {
        if (courseGroupId.value > 0) {
            // load the coursegroup data
            try {
                const response = await apiGet('/api/coursegroups/' + courseGroupId.value);
                const coursegroup = response.data;
                name.value = coursegroup.name;
                isTrialGroup.value = !!coursegroup.is_trial_group;
                ignoreForPriceList.value = !!coursegroup.ignore_for_price_list;
                webDescription.value = coursegroup.webdescription || "";
                coursesOnCourseGroup.value = coursegroup.courses; // also has recurrence options and students
                newMode.value = false;
            } catch(error) {
                failToast(ucFirst(translate("generic.loadfailed")) + ": " + error);
            }
        }
    };

    const saveCourseGroupData = async () => {
        const data = {
            name: name.value,
            isTrialGroup: isTrialGroup.value,
            ignoreForPriceList: ignoreForPriceList.value,
            webDescription: webDescription.value || '',
        }
        let url, apiMethod;
        if (newMode.value) {
            url = '/api/coursegroups';
            apiMethod = apiPost;
        } else {
            url = '/api/coursegroups/' + courseGroupId.value;
            apiMethod = apiPut;
        }
        try {
            const response = await apiMethod(url, data);
            successToast(ucFirst(translate("generic.saved")));
            // Reset the dirty flag. We can navigate away without warning
            window.dirty = false;
            // Only if we are in new mode navigate: to edit mode.
            if (newMode.value) {
                setTimeout(() => {
                    window.location.href = '/coursegroups/' + response.data.id + '/edit';
                }, 1000);
            }
        } catch (error) {
            failToast(ucFirst(translate("generic.savefailed")) + ": " + error);
        }
    };

    const isSaveable = computed(() => {
        return name.value?.length > 0;
    });

    return {
        courseGroupId,
        coursesOnCourseGroup,
        ignoreForPriceList,
        initCourseGroupData,
        isSaveable,
        isTrialGroup,
        name,
        newMode,
        saveCourseGroupData,
        webDescription
    };

}
