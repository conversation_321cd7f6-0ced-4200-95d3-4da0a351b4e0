import { computed, ref } from "vue";
import useApi from "./generic/useApi.js";
import useToast from "./generic/useToast.js";
import useLang from "./generic/useLang.js";
import useBaseData from "./generic/useBaseData.js";
import { format } from "date-fns";

const { failToast, successToast } = useToast();
const { ucFirst, translate } = useLang();
const { initBaseData } = useBaseData();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

const schoolYearIDToDelete = ref(null);
const schoolYearIDToEdit = ref(-1);
const schoolYearToEdit = ref(null);
const busy = ref(false);
const startOfSchoolYearObj = ref(null);
const endOfSchoolYearObj = ref(null);

export default function useSchoolYear() {
    const getSchoolYear = async () => {
        busy.value = true;
        if (schoolYearIDToEdit.value > 0) {
            // Logic to fetch a school year by ID: GET /api/schoolyears/{schoolYearId}
            try {
                const response = await apiGet(`/api/schoolyears/${ schoolYearIDToEdit.value }`);
                schoolYearToEdit.value = response.data;
            } catch (err) {
                // Handle error
                failToast(err, ucFirst(translate("generic.errorloadingdata")))
            } finally {
                busy.value = false;
            }
        } else {
            // Logic to create a new school year
            schoolYearToEdit.value = {
                id: null,
                label: "",
                start_year: 0,
                start_date: "",
                end_year: 0,
                end_date: "",
            };
            busy.value = false;
        }
    }

    const deleteSchoolYear = async () => {
        // Logic to delete a school year
        busy.value = true;
        try {
            await apiDel(`/api/schoolyears/${ schoolYearIDToDelete.value }`);
            successToast(ucFirst(translate('generic.datasaved')));
            schoolYearIDToDelete.value = null;
            // retrieve the list of school years again
            await initBaseData({schoolYears: true}, true);
        } catch (err) {
            failToast(err, ucFirst(translate("generic.errorloadingdata")))
        } finally {
            busy.value = false;
        }

        schoolYearIDToDelete.value = null;
    };

    const isSaveable = computed(() => {
        return schoolYearToEdit.value?.label &&
               schoolYearToEdit.value.label.length > 2 &&
               schoolYearToEdit.value.start_date.length > 0 &&
               schoolYearToEdit.value.end_date.length > 0;
    });

    const saveSchoolyear = async () => {
        const busy = ref(true);
        let url = '';
        let method = '';
        let data = schoolYearToEdit.value;
        // replace in data the values of start_date and end_date with the
        // values in startOfSchoolYearObj and endOfSchoolYearObj respectively
        data = {
            ...data,
            start_date: format(startOfSchoolYearObj.value, "yyyy-MM-dd"),
            end_date: format(endOfSchoolYearObj.value, "yyyy-MM-dd")
        }
        if (schoolYearToEdit.value.id) {
            url = `/api/schoolyears/${schoolYearToEdit.value.id}`;
            method = 'put';
        } else {
            url = '/api/schoolyears';
            method = 'post';
        }
        try {
            if (method === 'post') {
                await apiPost(url, data);
            } else {
                await apiPut(url, data);
            }
            successToast(ucFirst(translate('generic.datasaved')));
            schoolYearToEdit.value = {};
            schoolYearIDToEdit.value = schoolYearToEdit.value.id;
            window.dirty = false;
            // retrieve the list of school years again
            await initBaseData({schoolYears: true}, true);
        } catch (error) {
            failToast(ucFirst(translate('generic.error')) + ' ' + error.response.data.message);
        } finally {
            busy.value = false;
        }
    }

    return {
        busy,
        deleteSchoolYear,
        endOfSchoolYearObj,
        getSchoolYear,
        isSaveable,
        saveSchoolyear,
        schoolYearIDToDelete,
        schoolYearIDToEdit,
        schoolYearToEdit,
        startOfSchoolYearObj
    }
}
