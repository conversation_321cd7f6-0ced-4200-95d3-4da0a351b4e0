import { ref } from 'vue';
import useApi from "./generic/useApi.js";

const { apiGet, apiPut } = useApi();
const domainData = ref(null);
const validLanguages = [
    {label: "English", code: "en"},
    {label: "Nederlands", code: "nl"}
];
const busy = ref(false);
export default function useDomain() {

    /**
     * Get domain data from the server
     * @returns {Promise<void>}
     */
    const getDomainData = async () => {
        busy.value = true;
        try {
            const response = await apiGet('/api/getdomaininfo')
            domainData.value = response.data.data;
        } catch (error) {
            throw new Error(serializeErrorMessages(error.response.data.errors));
        } finally {
            busy.value = false;
        }
    }

    /**
     * Update domain data on the server
     * @returns {Promise<void>}
     */
    const updateDomainData = async () => {
        busy.value = true;
        // put all name/value pairs under strAddress directly in domainData
        // before sending it to update the domain data
        for (const key in domainData.value.strAddress) {
            if (!domainData.value.hasOwnProperty(key)) {
                domainData.value[key] = domainData.value.strAddress[key];
            }
        }
        try {
            const response = await apiPut('/api/updatedomaininfo', domainData.value)
            domainData.value = response.data.data;
        } catch (error) {
            throw new Error(serializeErrorMessages(error.response.data.errors));
        } finally {
            busy.value = false;
        }
    }

    /**
     * Serializes error messages into a single <br> separated string.
     *
     * @param {Object} errors - The object containing error messages.
     * @returns {string} - The serialized error messages as a string.
     */
    const serializeErrorMessages = (errors) => {
        return Object.values(errors)
            .flat() // create a single array from nested arrays
            .join('<br>');
    };

    const getDomainDataByAccessToken = async () => {
        // get accesstoken from URL, e.g /schedulepreference/b9d08d15255131a1a484c
        const url = window.location.href;
        const accessToken = url.substring(url.lastIndexOf('/') + 1);
        busy.value = true;
        try {
            const response = await apiGet('/api/getdomaininfobyaccesstoken?accesstoken=' + accessToken)
            domainData.value = response.data.data;
        } catch (error) {
            throw new Error(serializeErrorMessages(error.response.data.errors));
        } finally {
            busy.value = false;
        }
    }

    return {
        busy,
        domainData,
        getDomainData,
        getDomainDataByAccessToken,
        updateDomainData,
        validLanguages
    }
}
