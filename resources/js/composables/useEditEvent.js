import { computed, ref } from 'vue';
import usePlanning from './usePlanning.js';
import useDateTime from "./generic/useDateTime.js";

const { events, studentGroup } = usePlanning();
const { getDateOfWeekByDayName }  = useDateTime();

const eventToEdit = ref(null);
const doRevert = ref(() => {});

export default function useEditEvents () {
    const setEventToEdit = (obj) => {
        eventToEdit.value = obj;
    };

    /**
     * this will contain the function to be called when
     * the user clicks "cancel". Provided by the fullcalendar package
     * currently not in use, this is for drag/drop of events on the calendar
     */
    const setDoRevert = (func) => {
        doRevert.value = func;
    };

    /**
     * if this is a group lesson we want to show the user all names of students in the group
     * and link to their student card, so we need to return an array of objects {student id, student name}
     */
    const studentNames = computed(() => {
        return eventToEdit.value && eventToEdit.value
            ? eventToEdit.value.students.map(student => {
                return {
                    id: student.id,
                    name: student.name
                };
            })
            : '';
    });

    /**
     * Create an extra event based on the week number and year.
     *
     * @param {number} weekNumber - The week number.
     * @param {number} year - The year.
     */
    const createExtraEvent = (weekNumber, year) => {
        // create a clone of the first event, not a copy by reference
        const eventCopy = { ...events.value[0] };
        // Get the correct date for the event in the target week.
        const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        // Determine the correct date for the event in the target week (so the same day-of-week and the same time).
        // The time stays the same.
        const originatingDay = new Date(eventCopy.start).getDay();
        const targetDate = getDateOfWeekByDayName(weekNumber, parseInt(year), weekdays[originatingDay]);

        // Extract time in HH:MM:SS format to ensure consistent formatting
        const startDateTime = new Date(eventCopy.start);
        const endDateTime = new Date(eventCopy.end);
        const startTime = startDateTime.toTimeString().substring(0, 8); // HH:MM:SS
        const endTime = endDateTime.toTimeString().substring(0, 8); // HH:MM:SS

        eventCopy.start = targetDate + ' ' + startTime;
        eventCopy.end = targetDate + ' ' + endTime;
        eventCopy.id = 999999;
        eventCopy.eventId = 999999;
        events.value.push(eventCopy);
    }

    return {
        createExtraEvent,
        doRevert,
        eventToEdit,
        setDoRevert,
        setEventToEdit,
        studentNames,
    };
}
