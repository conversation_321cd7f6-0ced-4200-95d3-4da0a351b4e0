import { ref } from 'vue';
import useToast from "./generic/useToast.js";
import useLang from "./generic/useLang.js";
import useApi from "./generic/useApi.js";

const { failToast, successToast } = useToast();
const { translate, translateChoice, ucFirst } = useLang();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

const busy = ref(false);
const defaultChecklists = ref([]);
const defaultChecklistIdToDelete = ref(0);
const defaultChecklistToEdit = ref(null);

export default function useDefaultChecklists() {

    const getDefaultChecklists = async () => {
        busy.value = true;
        try {
            const response = await apiGet('/api/defaultchecklists');
            defaultChecklists.value = response.data;
            // turn all auto_add fields from '1' to true, which is what the switch expects
            defaultChecklists.value = defaultChecklists.value.map(checklist => ({
                ...checklist,
                auto_add: Boolean(checklist.auto_add)
            }));
        } catch (error) {
            failToast(error);
        } finally {
            busy.value = false;
        }
    }

    const removeDefaultChecklist = async () => {
        try {
            busy.value = true;
            await apiDel(`/api/defaultchecklists/${defaultChecklistIdToDelete.value}`);
            await getDefaultChecklists();
            defaultChecklistToEdit.value = null; // closes the edit section
            successToast(ucFirst(translateChoice('generic.defaultchecklists', 1)) + ' ' + translate('generic.deleted'));
        } catch (error) {
            failToast(error);
        } finally {
            busy.value = false;
        }
    };

    const saveDefaultChecklist = async () => {
        try {
            busy.value = true;
            if (defaultChecklistToEdit.value.id > 0) {
                // update record
                await apiPut(`/api/defaultchecklists/${defaultChecklistToEdit.value.id}`, defaultChecklistToEdit.value);
                console.log(defaultChecklistToEdit.value);
            } else {
                // new record
                await apiPost('/api/defaultchecklists', defaultChecklistToEdit.value);
            }
            // retrieve the new list from the backend
            await getDefaultChecklists();
        } catch (error) {
            throw error;
        } finally {
            busy.value = false;
            defaultChecklistToEdit.value = null; // closes the edit section
        }
    }

    const getNrOfItemsFilled = (defaultChecklist) => {
        let count = 0;
        for (let i = 1; i <= 12; i++) {
            if (defaultChecklist['item' + i] !== "" && defaultChecklist['item' + i] !== null) {
                count++;
            }
        }
        return count;
    }

    const emptyDefaultChecklistToEdit = () => {
        defaultChecklistToEdit.value = {
            id: 0,
            name: "",
            auto_add: false,
            item1: "",
            item2: "",
            item3: "",
            item4: "",
            item5: "",
            item6: "",
            item7: "",
            item8: "",
            item9: "",
            item10: "",
            item11: "",
            item12: "",
        };
    }

    return {
        busy,
        emptyDefaultChecklistToEdit,
        getDefaultChecklists,
        getNrOfItemsFilled,
        defaultChecklists,
        defaultChecklistIdToDelete,
        defaultChecklistToEdit,
        removeDefaultChecklist,
        saveDefaultChecklist,
    };
}
