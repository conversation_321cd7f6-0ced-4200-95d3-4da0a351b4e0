import { ref } from "vue";

// State variables for the conflicts side panel
const selectedDateException = ref(null);
const selectedConflicts = ref([]);
const isSidePanelOpen = ref(false);

export default function useConflictsSidePanel() {
    /**
     * <PERSON><PERSON> click on a conflict badge
     * This will open the side panel and populate it with the selected conflicts
     * 
     * @param {Array} lessonConflicts - The array of lesson conflicts from useDateExceptions
     * @param {number} dateExceptionId - The ID of the date exception that was clicked
     * @param {Event} event - The click event
     */
    const handleConflictClick = (lessonConflicts, dateExceptionId, event) => {
        event.stopPropagation();
        const conflictData = lessonConflicts.find(lc =>
            parseInt(lc.dateExceptionId) === parseInt(dateExceptionId)
        );

        selectedConflicts.value = conflictData?.conflicts;
        selectedDateException.value = conflictData?.dateException;
        isSidePanelOpen.value = true;
    };

    /**
     * Close the side panel
     */
    const closeSidePanel = () => {
        isSidePanelOpen.value = false;
    };

    return {
        handleConflictClick,
        isSidePanelOpen,
        selectedConflicts,
        selectedDateException,
        closeSidePanel
    };
}
