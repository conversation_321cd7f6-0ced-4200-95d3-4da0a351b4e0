import { computed, ref } from 'vue';

import useApi from './generic/useApi.js';
import useToast from "./generic/useToast.js";

const { failToast } = useToast();
const { apiGet } = useApi();

// api data
const studentPrefs = ref([]);
const student = ref([]);
const activeRegistrations = ref([]);

// local data
const studentId = ref(0);
const accessToken = ref(null);
const busy = ref(false);

const isAdmin = computed(() => accessToken.value === null);

export default function useSchedulePreferences() {
    /**
     * Gets the student preferences
     */
    const getStudentPrefs = async () => {
        busy.value = true;
        initializeFromUrl();
        const url = isAdmin.value
            ? `/api/admingetuserprefs?student=${studentId.value}`
            : `/api/getuserprefs?token=${accessToken.value}`;
        try {
            const response = await apiGet(url);
            studentPrefs.value = response.data.preferences;
            student.value = response.data.student;
            activeRegistrations.value = response.data.activeRegistrations;
        } catch (error) {
            failToast(error);
        } finally {
            busy.value = false;
        }
    };

    /**
     * Initializes accessToken and studentId based on the URL
     */
    const initializeFromUrl = () => {
        const url = window.location.pathname;
        if (url.includes('schedulepreference')) {
            accessToken.value = url.split('/').pop();
        } else if (url.includes('students/preferredschedule')) {
            studentId.value = url.split('/').pop();
        }
    };

    return {
        accessToken,
        activeRegistrations,
        busy,
        getStudentPrefs,
        student,
        studentId,
        studentPrefs,
        isAdmin,
    };
}
