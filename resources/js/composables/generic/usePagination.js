import { ref, computed } from 'vue';

export default function usePagination(initialPerPage = 25) {
    const paginationData = ref({
        current_page: 1,
        last_page: 1,
        per_page: initialPerPage,
        total: 0,
        from: 0,
        to: 0
    });

    const currentPage = ref(1);
    const perPage = ref(initialPerPage);

    const updatePaginationData = (data) => {
        paginationData.value = {
            current_page: data.current_page || 1,
            last_page: data.last_page || 1,
            per_page: data.per_page || perPage.value,
            total: data.total || 0,
            from: data.from || 0,
            to: data.to || 0
        };
        currentPage.value = data.current_page || 1;
    };

    const resetPagination = () => {
        currentPage.value = 1;
        paginationData.value = {
            current_page: 1,
            last_page: 1,
            per_page: perPage.value,
            total: 0,
            from: 0,
            to: 0
        };
    };

    const getPaginationParams = () => {
        return {
            page: currentPage.value,
            per_page: perPage.value
        };
    };

    const changePage = (page) => {
        if (page >= 1 && page <= paginationData.value.last_page && page !== currentPage.value) {
            currentPage.value = page;
            return true; // Indicates that page changed
        }
        return false;
    };

    const changePerPage = (newPerPage) => {
        if (newPerPage !== perPage.value) {
            perPage.value = newPerPage;
            currentPage.value = 1; // Reset to first page when changing page size
            return true; // Indicates that per page changed
        }
        return false;
    };

    const hasMultiplePages = computed(() => {
        return paginationData.value.last_page > 1;
    });

    const isFirstPage = computed(() => {
        return currentPage.value === 1;
    });

    const isLastPage = computed(() => {
        return currentPage.value === paginationData.value.last_page;
    });

    return {
        // State
        paginationData,
        currentPage,
        perPage,

        // Computed
        hasMultiplePages,
        isFirstPage,
        isLastPage,

        // Methods
        updatePaginationData,
        resetPagination,
        getPaginationParams,
        changePage,
        changePerPage
    };
}
