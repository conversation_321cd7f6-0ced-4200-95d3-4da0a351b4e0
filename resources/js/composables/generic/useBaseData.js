import { computed, ref } from 'vue';
import useToast from './useToast.js';
import useLang from './useLang.js';
import useApi from './useApi.js';

import { toSvg } from 'jdenticon';

const { ucFirst, translate } = useLang();
const { apiGet } = useApi();

const domain = ref({});
const allTutors = ref([]);
const allActiveUsers = ref([]); // this includes admins and tutors
const allLocations = ref([]);
const allCourses = ref([]);
const allCourseGroups = ref([]);
const allStudentGroups = ref([]);
const allSchoolYears = ref([]);
const allAttendanceoptions = ref([]);
const allDateExceptions = ref([]);
const allStudents = ref([]);
const allRecurrenceOptions = ref([]);

const loadingBaseData = ref(false)
// Track active promises for each data type
const activePromises = {
    activeUsers: null,
    attendanceOptions: null,
    courseGroups: null,
    courses: null,
    dateExceptions: null,
    domain: null,
    locations: null,
    recurrenceOptions: null,
    schoolYears: null,
    studentGroups: null,
    students: null,
    tutors: null
};
let activePromiseCount = 0;  // Counter to track how many promises are currently active

const loadingPerRequest = {
    attendanceoptions: false,
    coursegroups: false,
    courses: false,
    dateexceptions: false,
    getactiveusers: false,
    getdomaininfo: false,
    getschoolyears: false,
    gettutors: false,
    locations: false,
    recoptions: false,
    studentgroups: false,
    students: false
}

export default function useBaseData () {
    /**
     * Initialize or update base data
     * @param {Object} options - Specify which data to fetch
     * @param {boolean} options.activeUsers - Fetch active users
     * @param {boolean} options.attendanceOptions - Fetch attendance options
     * @param {boolean} options.courseGroups - Fetch course groups
     * @param {boolean} options.courses - Fetch courses
     * @param {boolean} options.dateExceptions - Fetch courses
     * @param {boolean} options.domain - Fetch domain info
     * @param {boolean} options.locations - Fetch locations
     * @param {boolean} options.recurrenceOptions - Fetch locations
     * @param {boolean} options.schoolYears - Fetch school years
     * @param {boolean} options.studentGroups - Fetch student groups
     * @param {boolean} options.students - Fetch students
     * @param {boolean} options.tutors - Fetch tutors
     *
     * @param {boolean} forceUpdate - Force update for the selected data
     */
    const initBaseData = async (options = {}, forceUpdate = false) => {
        const defaultOptions = {
            activeUsers: false,
            attendanceOptions: false,
            courseGroups: false,
            courses: false,
            dateExceptions: false,
            domain: false,
            locations: false,
            recurrenceOptions: false,
            schoolYears: false,
            studentGroups: false,
            students: false,
            tutors: false
        };

        const finalOptions = { ...defaultOptions, ...options };

        // Start a new data fetch if needed
        loadingBaseData.value = true;

        const promises = [];
        const promiseKeys = [];

        // Only create promises for data types that aren't already loading or if forceUpdate is true
        if (finalOptions.activeUsers && (forceUpdate || !activePromises.activeUsers)) {
            const promise = getAllActiveUser(forceUpdate);
            promises.push(promise);
            promiseKeys.push('activeUsers');
            activePromises.activeUsers = promise;
            activePromiseCount++;
        } else if (activePromises.activeUsers) {
            promises.push(activePromises.activeUsers);
        }

        if (finalOptions.attendanceOptions && (forceUpdate || !activePromises.attendanceOptions)) {
            const promise = getAttendanceoptions();
            promises.push(promise);
            promiseKeys.push('attendanceOptions');
            activePromises.attendanceOptions = promise;
            activePromiseCount++;
        } else if (activePromises.attendanceOptions) {
            promises.push(activePromises.attendanceOptions);
        }

        if (finalOptions.courseGroups && (forceUpdate || !activePromises.courseGroups)) {
            const promise = getCourseGroups(forceUpdate);
            promises.push(promise);
            promiseKeys.push('courseGroups');
            activePromises.courseGroups = promise;
            activePromiseCount++;
        } else if (activePromises.courseGroups) {
            promises.push(activePromises.courseGroups);
        }

        if (finalOptions.courses && (forceUpdate || !activePromises.courses)) {
            const promise = getCourses(forceUpdate);
            promises.push(promise);
            promiseKeys.push('courses');
            activePromises.courses = promise;
            activePromiseCount++;
        } else if (activePromises.courses) {
            promises.push(activePromises.courses);
        }

        if (finalOptions.dateExceptions && (forceUpdate || !activePromises.dateExceptions)) {
            const promise = getDateExceptions(forceUpdate);
            promises.push(promise);
            promiseKeys.push('dateExceptions');
            activePromises.dateExceptions = promise;
            activePromiseCount++;
        } else if (activePromises.dateExceptions) {
            promises.push(activePromises.dateExceptions);
        }

        if (finalOptions.domain && (forceUpdate || !activePromises.domain)) {
            const promise = getDomainInfo();
            promises.push(promise);
            promiseKeys.push('domain');
            activePromises.domain = promise;
            activePromiseCount++;
        } else if (activePromises.domain) {
            promises.push(activePromises.domain);
        }

        if (finalOptions.locations && (forceUpdate || !activePromises.locations)) {
            const promise = getAllLocations(forceUpdate);
            promises.push(promise);
            promiseKeys.push('locations');
            activePromises.locations = promise;
            activePromiseCount++;
        } else if (activePromises.locations) {
            promises.push(activePromises.locations);
        }

        if (finalOptions.recurrenceOptions && (forceUpdate || !activePromises.recurrenceOptions)) {
            const promise = getAllRecurrenceOptions(forceUpdate);
            promises.push(promise);
            promiseKeys.push('recurrenceOptions');
            activePromises.recurrenceOptions = promise;
            activePromiseCount++;
        } else if (activePromises.recurrenceOptions) {
            promises.push(activePromises.recurrenceOptions);
        }

        if (finalOptions.schoolYears && (forceUpdate || !activePromises.schoolYears)) {
            const promise = getSchoolYears(forceUpdate);
            promises.push(promise);
            promiseKeys.push('schoolYears');
            activePromises.schoolYears = promise;
            activePromiseCount++;
        } else if (activePromises.schoolYears) {
            promises.push(activePromises.schoolYears);
        }

        if (finalOptions.studentGroups && (forceUpdate || !activePromises.studentGroups)) {
            const promise = getStudentGroups();
            promises.push(promise);
            promiseKeys.push('studentGroups');
            activePromises.studentGroups = promise;
            activePromiseCount++;
        } else if (activePromises.studentGroups) {
            promises.push(activePromises.studentGroups);
        }

        if (finalOptions.students && (forceUpdate || !activePromises.students)) {
            const promise = getStudents();
            promises.push(promise);
            promiseKeys.push('students');
            activePromises.students = promise;
            activePromiseCount++;
        } else if (activePromises.students) {
            promises.push(activePromises.students);
        }

        if (finalOptions.tutors && (forceUpdate || !activePromises.tutors)) {
            const promise = getAllTutors(forceUpdate);
            promises.push(promise);
            promiseKeys.push('tutors');
            activePromises.tutors = promise;
            activePromiseCount++;
        } else if (activePromises.tutors) {
            promises.push(activePromises.tutors);
        }

        // Create a combined promise for all requested data
        return Promise.all(promises).finally(() => {
            // Clear the active promises for the completed requests
            promiseKeys.forEach(key => {
                activePromises[key] = null;
                activePromiseCount--;
            });
            
            // Only set loadingBaseData to false when all promises are complete
            if (activePromiseCount === 0) {
                loadingBaseData.value = false;
            }
        });
    };

    const { failToast } = useToast();

    const getAllActiveUser = async (forceUpdate = false) => {
        if ((allActiveUsers.value.length === 0 || forceUpdate) && !loadingPerRequest.getactiveusers) {
            try {
                loadingPerRequest.getactiveusers = true;
                const resp = await apiGet('/api/getactiveusers');
                allActiveUsers.value = resp.data;
            } catch (err) {
                failToast(ucFirst(translate('generic.errorloadingactiveusers')) + ' ' + err);
            } finally {
                loadingPerRequest.getactiveusers = false;
            }
        }
    };

    const getAllTutors = async (forceUpdate = false) => {
        if ((allTutors.value.length === 0 || forceUpdate) && !loadingPerRequest.gettutors) {
            try {
                loadingPerRequest.gettutors = true;
                const resp = await apiGet('/api/gettutors');
                allTutors.value = resp.data;
            } catch (err) {
                failToast(
                    ucFirst(translate('generic.errorloadingtutors')) + ' ' + err
                );
            } finally {
                loadingPerRequest.gettutors = false;
            }
        }
    };

    const getAllLocations = async (forceUpdate = false) => {
        if ((allLocations.value.length === 0 || forceUpdate) && !loadingPerRequest.locations) {
            try {
                loadingPerRequest.locations = true;
                const resp = await apiGet('/api/locations');
                allLocations.value = resp.data.data;
                // generate icons
                allLocations.value.forEach((location, idx) => {
                    allLocations.value[idx].icon = toSvg(`location_${location.domain_id}_${location.id}`, 35);
                    allLocations.value[idx].iconSmall = toSvg(`location_${location.domain_id}_${location.id}`, 20);
                });
            } catch (err) {
                failToast(ucFirst(translate('generic.errorloadinglocations')) + ' ' + err);
            } finally {
                loadingPerRequest.locations = false;
            }
        }
    };

    const getAllRecurrenceOptions = async (forceUpdate = false) => {
        if ((allRecurrenceOptions.value.length === 0 || forceUpdate) && !loadingPerRequest.recoptions) {
            try {
                loadingPerRequest.recoptions = true;
                const response = await apiGet('/api/recoptions');
                allRecurrenceOptions.value = response.data;
            } catch (err) {
                failToast(
                    ucFirst(translate('generic.errorloadingrecurrenceoptions')) + ' ' + err
                );
            } finally {
                loadingPerRequest.recoptions = false;
            }
        }
    }

    const getCourses = async (forceUpdate = false) => {
        if ((allCourses.value.length === 0 || forceUpdate) && !loadingPerRequest.courses) {
            try {
                loadingPerRequest.courses = true;
                const response = await apiGet('/api/courses?excludeArchive=0');
                allCourses.value = response.data.data;
            } catch (err) {
                failToast(ucFirst(translate('generic.errorloadingcourses')) + ' ' + err);
            } finally {
                loadingPerRequest.courses = false;
            }
        }
    };

    const getSchoolYears = async (forceUpdate = false) => {
        if ((allSchoolYears.value.length === 0 || forceUpdate) && !loadingPerRequest.getschoolyears) {
            try {
                loadingPerRequest.getschoolyears = true;
                const response = await apiGet('/api/getschoolyears');
                allSchoolYears.value = response.data;
            } catch (err) {
                failToast(
                    ucFirst(translate('generic.errorloadingschoolyears')) + ' ' + err
                );
            } finally {
                loadingPerRequest.getschoolyears = false;
            }
        }
    };

    const getDomainInfo = async () => {
        try {
            const response = await apiGet('/api/getdomaininfo');
            domain.value = response.data.data;
        } catch (err) {
            failToast(
                ucFirst(translate('generic.errorloadingdomaininfo')) + ' ' + err
            );
        }
    };

    /**
     * get the current school year or the first future school year
     * return null if no current or future school year is found
     */
    const currentOrFutureSchoolYear = computed(() => {
        const now = new Date();
        const retSchoolYears = allSchoolYears.value.filter(schoolYear => {
            const end = new Date(schoolYear.end_date);
            return (end >= now);
        });
        return retSchoolYears.length > 0 ? retSchoolYears[0] : null;
    });

    const getCourseGroups = async (forceUpdate = false) => {
        if ((allCourseGroups.value.length === 0 || forceUpdate) && !loadingPerRequest.coursegroups) {
            try {
                loadingPerRequest.coursegroups = true;
                const response = await apiGet('/api/coursegroups');
                allCourseGroups.value = response.data;
            } catch (err) {
                failToast(
                    ucFirst(translate('generic.errorloadingcoursegroups')) + ' ' + err
                );
            } finally {
                loadingPerRequest.coursegroups = false;
            }
        }
    };

    const getStudentGroups = async () => {
        if (allStudentGroups.value.length === 0 && !loadingPerRequest.studentgroups) {
            try {
                loadingPerRequest.studentgroups = true;
                const response = await apiGet('/api/studentgroups');
                allStudentGroups.value = response.data.data;
            } catch (err) {
                failToast(
                    ucFirst(translate('generic.errorloadingstudentgroups')) + ' ' + err
                );
            } finally {
                loadingPerRequest.studentgroups = false;
            }
        }
    };

    const getStudents = async () => {
        if (allStudents.value.length === 0 && !loadingPerRequest.students) {
            try {
                loadingPerRequest.students = true;
                const response = await apiGet('/api/students?onlystudents=true&onlyactive=true');
                allStudents.value = response.data.data;
            } catch (err) {
                failToast(
                    ucFirst(translate('generic.errorloadingstudents')) + ' ' + err
                );
            } finally {
                loadingPerRequest.students = false;
            }
        }
    };

    const getAttendanceoptions = async () => {
        if (allAttendanceoptions.value.length === 0 && !loadingPerRequest.attendanceoptions) {
            try {
                loadingPerRequest.attendanceoptions = true;
                const response = await apiGet('/api/attendanceoptions');
                allAttendanceoptions.value = response.data;
            } catch (e) {
                failToast(
                    ucFirst(translate('generic.errorloadingattendanceoptions')) + ' ' + e
                );
            } finally {
                loadingPerRequest.attendanceoptions = false;
            }
        }
    };

    const getDateExceptions = async (forceUpdate = false) => {
        if ((allDateExceptions.value.length === 0 || forceUpdate) && !loadingPerRequest.dateexceptions) {
            try {
                loadingPerRequest.dateexceptions = true;
                const response = await apiGet('/api/dateexceptions');
                allDateExceptions.value = response.data.data;
            } catch (e) {
                failToast(
                    ucFirst(translate('generic.errorloadingdateexceptions')) + ' ' + e
                );
            } finally {
                loadingPerRequest.dateexceptions = false;
            }
        }
    }

    /**
     * get only course groups that have at least one course that is not archived
     * AND only return the not-archived courses in the course group
     * @type {ComputedRef<*[]>}
     */
    const filteredCourseGroups = computed(() => {
        const returnCourseGroups = [];
        allCourseGroups.value.forEach((cg) => {
            const theCoursesOfThisCourseGroup = cg.courses.filter(course => !(parseInt(course.archive) === 1));
            // if there's no course without the ARCHIEF label, don't include the course group in the result
            if (theCoursesOfThisCourseGroup.length > 0) {
                // sort the courses
                theCoursesOfThisCourseGroup.sort((courseEntryA, courseEntryB) => {
                    if (courseEntryA.name < courseEntryB.name) {
                        return -1;
                    }
                    if (courseEntryA.name > courseEntryB.name) {
                        return 1;
                    }
                    return 0;
                });
                returnCourseGroups.push(cg);
                returnCourseGroups[returnCourseGroups.length - 1].courses = theCoursesOfThisCourseGroup;
            }
        });
        return returnCourseGroups;
    });

    /**
     * get all courses that have no courses with the ARCHIEF label
     */
    const emptyCourseGroups = computed(() => {
        return allCourseGroups.value.filter(cg => cg.courses.length === 0);
    });

    /**
     * get only courses that are not archived
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const filteredCourses = computed(() => {
        return allCourses.value.filter(course => !(parseInt(course.archive) === 1));
    });

    /**
     * get only duo and group courses
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const duoAndGroupCourses = computed(() => {
        return allCourses.value.filter(course => course.group_size_min > 1);
    });

    /**
     * get only individual courses
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const individualCourses = computed(() => {
        return allCourses.value.filter(course => course.group_size_max === 1);
    });

    /**
     * fixme: better solution for hardcoded "HUUR" term, should be a flag in the database
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const filteredCoursesNoRental = computed(() => {
        return allCourses.value.filter(course => {
            return !(
                parseInt(course.archive) === 1 ||
                course.name.includes('HUUR') ||
                course.name.includes('huur')
            );
        });
    });

    const allArchivedCourses = computed(() => {
        return allCourses.value.filter(course => course.archive !== 0);
    });

    return {
        allActiveUsers,
        allArchivedCourses,
        allAttendanceoptions,
        allCourses,
        allCourseGroups,
        allDateExceptions,
        allLocations,
        allRecurrenceOptions,
        allSchoolYears,
        allStudentGroups,
        allStudents,
        allTutors,
        domain,
        duoAndGroupCourses,
        individualCourses,
        loadingBaseData,
        currentOrFutureSchoolYear,
        emptyCourseGroups,
        filteredCourseGroups,
        filteredCourses,
        filteredCoursesNoRental,
        initBaseData
    };
}
