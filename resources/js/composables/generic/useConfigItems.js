import { computed, ref } from "vue";
import useLang from './useLang.js';
import {
    Autoformat,
    AutoImage,
    Autosave,
    Bold,
    Essentials,
    Heading,
    HorizontalLine,
    ImageBlock,
    ImageInline,
    ImageInsertViaUrl,
    ImageResize,
    ImageStyle,
    ImageToolbar,
    Indent,
    IndentBlock,
    Italic,
    Link,
    LinkImage,
    List,
    MediaEmbed,
    Paragraph,
    PasteFromOffice,
    Table,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TextTransformation,
    Underline
} from 'ckeditor5';

import translations from 'ckeditor5/translations/nl.js'; // todo make locale dynamic
const isLayoutReady = ref(false);


/**
 * Create a free account with a trial: https://portal.ckeditor.com/checkout?plan=free
 */
const LICENSE_KEY = 'GPL'; // or <YOUR_LICENSE_KEY>.

const { lang, translate, translateChoice, ucFirst } = useLang();

export default function useConfigItems() {
    class PlaceholdersPlugin {
        constructor(editor) {
            this.editor = editor;
            this.init();
        }

        init() {
            const editor = this.editor;

            editor.ui.componentFactory.add('placeholders', () => {
                // Create a container for the dropdown
                const container = document.createElement('div');
                container.className = 'ck ck-dropdown';

                // Create a select element
                const select = document.createElement('select');
                select.className = 'ck ck-button ck-dropdown__select';

                // Add a default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = ucFirst(translate('email.mailanchors'));
                defaultOption.selected = true;
                defaultOption.disabled = true;
                select.appendChild(defaultOption);

                // Add placeholder options
                placeholderItems.forEach(item => {
                    const option = document.createElement('option');

                    if (item.type === 'separator') {
                        // Create a disabled separator option
                        option.disabled = true;
                        option.value = '';
                        option.textContent = item.label;
                        option.style.fontWeight = 'bold';
                        option.style.backgroundColor = '#f0f0f0';
                    } else {
                        // Regular placeholder option
                        option.value = item.value;
                        option.textContent = item.label;
                    }

                    select.appendChild(option);
                });

                // Handle selection
                select.addEventListener('change', () => {
                    const value = select.value;
                    editor.model.change(writer => {
                        const insertPosition = editor.model.document.selection.getFirstPosition();
                        editor.model.insertContent(writer.createText(value), insertPosition);
                    });
                    select.selectedIndex = 0; // Reset to default option
                });

                container.appendChild(select);

                // Implement the expected interface
                return {
                    element: container,
                    render: () => {
                        return container;
                    },
                    destroy: () => {
                        container.remove();
                    }
                };
            });
        }
    }

    class ImageViaUrlPlugin {
        static get pluginName() {
            return 'CustomImageViaUrl';
        }

        constructor(editor) {
            this.editor = editor;
            this.init();
        }

        init() {
            const editor = this.editor;

            // Register schema if not already registered
            if (!editor.model.schema.isRegistered('customImage')) {
                editor.model.schema.register('customImage', {
                    allowWhere: '$block',
                    allowAttributes: ['src'],
                    isObject: true
                });
            }

            // Separate conversion rules for data and editing
            editor.conversion.for('dataDowncast').elementToElement({
                model: 'customImage',
                view: (modelElement, { writer }) => {
                    return writer.createEmptyElement('img', {
                        src: modelElement.getAttribute('src'),
                        style: 'max-width: 100%; height: auto;'
                    });
                }
            });

            editor.conversion.for('editingDowncast').elementToElement({
                model: 'customImage',
                view: (modelElement, { writer }) => {
                    return writer.createEmptyElement('img', {
                        src: modelElement.getAttribute('src'),
                        style: 'max-width: 100%; height: auto;'
                    });
                }
            });

            editor.ui.componentFactory.add('imageViaUrl', () => {
                // Create a container for the dropdown
                const container = document.createElement('div');
                container.className = 'ck ck-dropdown';

                // Create the input container
                const inputContainer = document.createElement('div');
                inputContainer.className = 'ck ck-dropdown__panel';
                inputContainer.style.padding = '10px';
                inputContainer.style.display = 'none';

                // Create the input elements
                const inputWrapper = document.createElement('div');
                inputWrapper.className = 'ck-labeled-field-view';
                inputWrapper.style.display = 'flex';
                inputWrapper.style.gap = '8px';

                const urlInput = document.createElement('input');
                urlInput.type = 'text';
                urlInput.className = 'ck ck-input';
                urlInput.placeholder = ucFirst(translate('generic.enterimageurl'));

                const buttonWrapper = document.createElement('div');
                buttonWrapper.style.display = 'flex';
                buttonWrapper.style.gap = '4px';

                const confirmButton = document.createElement('button');
                confirmButton.className = 'ck ck-button';
                confirmButton.innerHTML = `
                    <svg class="ck ck-icon" viewBox="0 0 20 20">
                        <path d="M8.315 13.859l-3.182-3.417a.506.506 0 0 1 0-.684l.643-.683a.437.437 0 0 1 .642 0l2.22 2.393 4.942-5.327a.436.436 0 0 1 .643 0l.643.684a.504.504 0 0 1 0 .683l-5.91 6.35a.437.437 0 0 1-.642 0"/>
                    </svg>
                `;

                const cancelButton = document.createElement('button');
                cancelButton.className = 'ck ck-button';
                cancelButton.innerHTML = `
                    <svg class="ck ck-icon" viewBox="0 0 20 20">
                        <path d="M11.591 10.177l4.243 4.242a1 1 0 01-1.415 1.415l-4.242-4.243-4.243 4.243a1 1 0 01-1.414-1.415l4.243-4.242L4.52 5.934a1 1 0 011.414-1.414l4.243 4.242 4.242-4.242a1 1 0 111.415 1.414l-4.243 4.243z"/>
                    </svg>
                `;

                // Create the main button
                const button = document.createElement('button');
                button.className = 'ck ck-button';
                button.innerHTML = `
                    <span class="ck ck-icon">
                        <svg class="ck ck-icon__svg" viewBox="0 0 20 20">
                            <path d="M6.91 10.54c.26-.23.64-.21.88.03l3.36 3.14 2.23-2.06a.64.64 0 0 1 .87 0l2.52 2.97V4.5H3.2v10.12l3.71-4.08zm10.27-7.51c.6 0 1.09.47 1.09 1.05v11.84c0 .59-.49 1.06-1.09 1.06H2.79c-.6 0-1.09-.47-1.09-1.06V4.08c0-.58.49-1.05 1.1-1.05h14.38zm-5.22 5.56a1.96 1.96 0 1 1 3.4-1.96 1.96 1.96 0 0 1-3.4 1.96z"/>
                        </svg>
                    </span>
                    <span class="ck ck-button__label">${ucFirst(translate('generic.imageurl'))}</span>
                `;

                // Event handlers
                button.addEventListener('click', () => {
                    inputContainer.style.display = inputContainer.style.display === 'none' ? 'block' : 'none';
                });

                confirmButton.addEventListener('click', () => {
                    const url = urlInput.value.trim();
                    if (url) {
                        editor.model.change(writer => {
                            const imageElement = writer.createElement('customImage', {
                                src: url
                            });
                            editor.model.insertContent(imageElement);
                        });

                        // Reset and close the dropdown
                        urlInput.value = '';
                        inputContainer.style.display = 'none';
                    }
                });

                cancelButton.addEventListener('click', () => {
                    urlInput.value = '';
                    inputContainer.style.display = 'none';
                });

                // Enter key handler for the input
                urlInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        confirmButton.click();
                    }
                });

                // Assemble all elements
                buttonWrapper.appendChild(confirmButton);
                buttonWrapper.appendChild(cancelButton);
                inputWrapper.appendChild(urlInput);
                inputWrapper.appendChild(buttonWrapper);
                inputContainer.appendChild(inputWrapper);
                container.appendChild(button);
                container.appendChild(inputContainer);

                return {
                    element: container,
                    render: () => {
                        return container;
                    },
                    destroy: () => {
                        container.remove();
                    }
                };
            });
        }
    }

    /**
     * Currently not in use. This should be an API call. Se also useMailTemplateVariables
     * @type {[{id: string, type: string, label: string},{id: string, label: (string|*), value: string},{id: string, label: (string|*), value: string},{id: string, label: (string|*), value: string},{id: string, type: string, label: string},null,null,null,null,null,null,null,null,null]}
     */
    const placeholderItems = [
        // Student related placeholders
        { id: 'student_separator', type: 'separator', label: '--- ' + ucFirst(translate('generic.student')) + ' ---' },
        { id: 'studentfullname', label: ucFirst(translate('generic.studentname')), value: '%studentfullname%' },
        { id: 'studentfirstname', label: ucFirst(translate('generic.studentfirstname')), value: '%studentfirstname%' },
        { id: 'studentaccesslink', label: ucFirst(translate('generic.studentaccesslink')), value: '%studentaccesslink%' },

        // Salutation related placeholders
        { id: 'salutation_separator', type: 'separator', label: '--- ' + ucFirst(translate('generic.salutation')) + ' ---' },
        { id: 'salutationforfinancial', label: ucFirst(translate('generic.salutationforfinancial')), value: '%salutationforfinancial%' },
        { id: 'salutationforplanning', label: ucFirst(translate('generic.salutationforplanning')), value: '%salutationforplanning%' },
        { id: 'salutationforpromotion', label: ucFirst(translate('generic.salutationforpromotion')), value: '%salutationforpromotion%' },

        // School related placeholders
        { id: 'school_separator', type: 'separator', label: '--- ' + ucFirst(translateChoice('generic.schools', 1)) + ' ---' },
        { id: 'schoollogo', label: ucFirst(translate('generic.schoollogo')), value: "%schoollogo|width='100'%" },
        { id: 'schoolname', label: ucFirst(translate('generic.schoolname')), value: '%schoolname%' },
        { id: 'schooltelephone', label: ucFirst(translate('generic.schooltelephone')), value: '%schooltelephone%' },
        { id: 'schoolcontactperson', label: ucFirst(translate('generic.schoolcontactperson')), value: '%schoolcontactperson%' },
        { id: 'schoolwebsite', label: ucFirst(translate('generic.schoolwebsite')), value: '%schoolwebsite%' }
    ];

    /**
     * This configuration was generated using the CKEditor 5 Builder. You can modify it anytime using this link:
     * https://ckeditor.com/ckeditor-5/builder/#installation/NoNgNARATAdCMAYKQCwGYpQKwEYEJBywHY1iQBOHEADnxAVwRpVbQyhzpuQgDsANsgRhgOMCJHipAXUgBTAEYgsAYygATCDKA===
     */
    const configMailTemplate = computed(() => {
        if (!isLayoutReady.value) {
            return null;
        }

        return {
            toolbar: {
                items: [
                    'undo',
                    'redo',
                    '|',
                    'heading',
                    '|',
                    'bold',
                    'italic',
                    'underline',
                    '|',
                    'horizontalLine',
                    'link',
                    'insertImageViaUrl',
                    'mediaEmbed',
                    'insertTable',
                    '|',
                    // 'placeholders', should be a api call
                    // '|',
                    'bulletedList',
                    'numberedList',
                    'outdent',
                    'indent'
                ],
                shouldNotGroupWhenFull: false
            },
            extraPlugins: [PlaceholdersPlugin, ImageViaUrlPlugin],
            plugins: [
                Autoformat,
                AutoImage,
                Autosave,
                Bold,
                Essentials,
                Heading,
                HorizontalLine,
                ImageBlock,
                ImageInline,
                ImageInsertViaUrl,
                ImageResize,
                ImageStyle,
                ImageToolbar,
                Indent,
                IndentBlock,
                Italic,
                Link,
                LinkImage,
                List,
                MediaEmbed,
                Paragraph,
                PasteFromOffice,
                Table,
                TableCellProperties,
                TableColumnResize,
                TableProperties,
                TableToolbar,
                TextTransformation,
                Underline
            ],
            heading: {
                options: [
                    {
                        model: 'paragraph',
                        title: 'Paragraph',
                        class: 'ck-heading_paragraph'
                    },
                    {
                        model: 'heading1',
                        view: 'h1',
                        title: 'Heading 1',
                        class: 'ck-heading_heading1'
                    },
                    {
                        model: 'heading2',
                        view: 'h2',
                        title: 'Heading 2',
                        class: 'ck-heading_heading2'
                    },
                    {
                        model: 'heading3',
                        view: 'h3',
                        title: 'Heading 3',
                        class: 'ck-heading_heading3'
                    },
                    {
                        model: 'heading4',
                        view: 'h4',
                        title: 'Heading 4',
                        class: 'ck-heading_heading4'
                    },
                    {
                        model: 'heading5',
                        view: 'h5',
                        title: 'Heading 5',
                        class: 'ck-heading_heading5'
                    },
                    {
                        model: 'heading6',
                        view: 'h6',
                        title: 'Heading 6',
                        class: 'ck-heading_heading6'
                    }
                ]
            },
            image: {
                toolbar: ['imageStyle:inline', 'imageStyle:wrapText', 'imageStyle:breakText', '|', 'resizeImage']
            },
            initialData: '',
            language: 'nl',
            licenseKey: LICENSE_KEY,
            link: {
                addTargetToExternalLinks: true,
                defaultProtocol: 'https://',
                decorators: {
                    toggleDownloadable: {
                        mode: 'manual',
                        label: 'Downloadable',
                        attributes: {
                            download: 'file'
                        }
                    }
                }
            },
            placeholder: 'Type or paste your content here!',
            table: {
                contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
            },
            translations: [translations]
        };
    });

    const editorConfigSimple = {
        toolbar: {
            items: [
                'heading',
                '|',
                'bold',
                'italic',
                'link',
                'bulletedList',
                'numberedList',
                '|',
                'undo',
                'redo'
            ],
            shouldNotGroupWhenFull: false
        },
        language: lang,
        heading: {
            options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
            ]
        },
        removePlugins: ['CKFinder'],
        plugins: [
            Autoformat,
            AutoImage,
            Autosave,
            Bold,
            Essentials,
            Heading,
            HorizontalLine,
            ImageBlock,
            ImageInline,
            ImageInsertViaUrl,
            ImageResize,
            ImageStyle,
            ImageToolbar,
            Indent,
            IndentBlock,
            Italic,
            Link,
            LinkImage,
            List,
            MediaEmbed,
            Paragraph,
            PasteFromOffice,
            Table,
            TableCellProperties,
            TableColumnResize,
            TableProperties,
            TableToolbar,
            TextTransformation,
            Underline
        ],
        licenseKey: LICENSE_KEY,
        ckfinder: {
            enabled: false
        }
    };

    return {
        configMailTemplate,
        // editorConfigWithMediaAndImage,
        editorConfigSimple,
        isLayoutReady
    };
}
