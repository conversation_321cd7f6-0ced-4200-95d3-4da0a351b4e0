import { computed } from 'vue';
import useLang from './useLang.js';
import { parse, format, getHours, getMinutes, set } from 'date-fns';

/**
 * Composable for date picker options compatible with @vuepic/vue-datepicker
 * Ensures model value is always in DB format (yyyy-MM-dd HH:mm:ss or yyyy-MM-dd),
 * and display/selection is locale-specific.
 * 
 * Note: This composable uses the teleport option to ensure the datepicker appears above Bootstrap modals.
 * The teleport option renders the datepicker dropdown directly to the body element, which places it 
 * outside the modal's DOM hierarchy and stacking context.
 * 
 * How teleport works:
 * 1. Bootstrap modals create their own stacking context with a z-index (typically around 1050)
 * 2. Elements inside the modal (like datepickers) are constrained by this stacking context
 * 3. The teleport option moves the dropdown outside of the modal's DOM hierarchy
 * 4. By placing the dropdown directly on the body element, it's no longer affected by the modal's
 *    stacking context limitations
 * 5. This allows the dropdown to appear correctly above the modal footer and other UI elements
 * 
 * @param {boolean} isWholeDay - Whether the date picker should only show date (true) or date and time (false)
 * @param {string} [forceLang] - Force a specific language ('nl' or 'en'), otherwise use the user's language
 * @param {boolean} [allowUserInput] - Whether the user can input dates manually (true) or not (false)
 * @param {boolean} [showClear=false] - Whether to show the clear button
 * @returns {Object} - Date picker options
 */
export default function useDatePicker(
    isWholeDay,
    forceLang = null,
    allowUserInput = true,
    showClear = false
) {
    const { lang } = useLang();

    // Display formats
    const DISPLAY_FORMAT_DATE_NL = 'dd-MM-yyyy';
    const DISPLAY_FORMAT_DATE_EN = 'MM/dd/yyyy';
    const DISPLAY_FORMAT_DATETIME_NL = 'dd-MM-yyyy HH:mm';
    const DISPLAY_FORMAT_DATETIME_EN = 'MM/dd/yyyy HH:mm';
    const DISPLAY_FORMAT_TIME = 'HH:mm';

    // Options for date and time picker in Dutch
    const dpOptionsDateTimeNl = {
        format: DISPLAY_FORMAT_DATETIME_NL,
        locale: 'nl-NL',
        enableTimePicker: true,
    };

    // Options for date and time picker in English
    const dpOptionsDateTimeEn = {
        format: DISPLAY_FORMAT_DATETIME_EN,
        locale: 'en-US',
        enableTimePicker: true,
    };

    // Options for date-only picker in Dutch
    const dpOptionsDateNl = {
        format: DISPLAY_FORMAT_DATE_NL,
        locale: 'nl-NL',
        enableTimePicker: false,
    };

    // Options for date-only picker in English
    const dpOptionsDateEn = {
        format: DISPLAY_FORMAT_DATE_EN,
        locale: 'en-US',
        enableTimePicker: false,
    };

    // Options for time-only picker in Dutch
    const dpOptionsTimeNl = {
        format: DISPLAY_FORMAT_TIME,
        locale: 'nl-NL',
        timePicker: true,
        is24: true,
        minutesIncrement: 5
    };

    // Options for time-only picker in English
    const dpOptionsTimeEn = {
        format: DISPLAY_FORMAT_TIME,
        locale: 'en-US',
        timePicker: true,
        is24: true,
        minutesIncrement: 5
    };

    /**
     * Computed property that returns the appropriate date picker options
     * based on the user's language and whether it's a whole day or not
     *
     */
    const dpOptions = computed(() => {
        // Determine which language to use (forced or from user settings)
        const currentLang = forceLang || lang.value;

        // Get the base options based on language and whether it's a whole day
        let options;
        if (isWholeDay.value) {
            options = (currentLang === 'nl') ? { ...dpOptionsDateNl } : { ...dpOptionsDateEn };
        } else {
            options = (currentLang === 'nl') ? { ...dpOptionsDateTimeNl } : { ...dpOptionsDateTimeEn };
        }

        // Add clearable property if showClear is true
        if (showClear) {
            options.clearable = true;
        }
        if (allowUserInput) {
            options.textInput = true;
        }
        
        // Enable teleport to body to ensure the datepicker appears above Bootstrap modals
        options.teleport = true;

        return options;
    });

    /**
     * Computed property that returns time picker options
     * based on the user's language
     */
    const dpOptionsTime = computed(() => {
        // Always get the current language at call time
        const currentLang = forceLang || lang.value;
        let options = (currentLang === 'nl') ? { ...dpOptionsTimeNl } : { ...dpOptionsTimeEn };
        if (showClear) {
            options.clearable = true;
        }
        if (allowUserInput) {
            options.textInput = true;
        }
        
        // Enable teleport to body to ensure the timepicker appears above Bootstrap modals
        options.teleport = true;
        
        return options;
    });

    /**
     * returns a string representation of a time object ready for the database.
     * if no seconds are provided, defaults to '00'.
     * @param obj of the form { hours: number, minutes: number, seconds?: number }
     * @return {string|null} 'HH:mm:ss' or null if no time is provided
     */
    const toDbTime = (obj) => {
        if (!obj || (obj.hours === undefined && obj.minutes === undefined)) return null;

        // Create a date object with the specified hours, minutes, and seconds
        const date = set(new Date(), {
            hours: obj.hours !== undefined ? obj.hours : 0,
            minutes: obj.minutes !== undefined ? obj.minutes : 0,
            seconds: obj.seconds !== undefined ? obj.seconds : 0
        });

        // Format the date as a time string
        return format(date, 'HH:mm:ss');
    }

    /**
     * Converts a time string from the database to an object with hours and minutes.
     * @param str 'yyyy-mm-dd HH:mm[:ss]',  'HH:mm[:ss]'
     * @returns {{hours: number, minutes: number}}
     */
    const fromDbTime = (str) => {
        if (!str) {
            // If no string is provided, use current time
            const now = new Date();
            return { hours: getHours(now), minutes: getMinutes(now) };
        }

        let date;

        try {
            if (/^\d{2}:\d{2}:\d{2}$/.test(str)) {
                // Format: HH:mm:ss
                date = parse(str, 'HH:mm:ss', new Date());
            } else if (/^\d{2}:\d{2}$/.test(str)) {
                // Format: HH:mm
                date = parse(str, 'HH:mm', new Date());
            } else if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(str)) {
                // Format: yyyy-MM-dd HH:mm:ss
                date = parse(str, 'yyyy-MM-dd HH:mm:ss', new Date());
            } else {
                // If format is not recognized, use current time
                date = new Date();
            }

            return { hours: getHours(date), minutes: getMinutes(date) };
        } catch (error) {
            // If parsing fails, use current time
            const now = new Date();
            return { hours: getHours(now), minutes: getMinutes(now) };
        }
    }


    return {
        dpOptions,
        dpOptionsTime,
        fromDbTime,
        toDbTime
    };
}
