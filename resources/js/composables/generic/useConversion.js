export default function useConversion () {
    const convertBytesToHumanReadable = (bytes = 0) => {
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes == null || bytes === '' || bytes === 0 || isNaN(bytes)) {
            return '0 Bytes';
        }
        const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
        if (i === 0) {
            return bytes + ' ' + sizes[i];
        }
        return (bytes / Math.pow(1024, i)).toFixed(1) + ' ' + sizes[i];
    };

    return {
        convertBytesToHumanReadable
    };
}
