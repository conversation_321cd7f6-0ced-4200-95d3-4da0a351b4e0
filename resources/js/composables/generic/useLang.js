import { computed } from 'vue';
import _ from 'lodash';

export default function useLang () {
    const lang = computed(() => {
        return trans('generic.language'); // 'en' or 'nl'
    });

    /**
     * Set the first letter of incoming string to its capital version
     * @param input
     * @returns {string}
     */
    const ucFirst = (input) => {
        return typeof input === 'undefined' ? '?' : input.charAt(0).toUpperCase() + input.slice(1);
    };

    /**
     * Translate a key to the current language
     * @param {string} key - The key to translate
     * @param {object} params - The parameters to replace in the translation, 
     *                          the key should be of the form ':key' and will be
     *                          replaced with the value of the param[key]
     * @returns {string}
     */
    const trans = (key, params = '') => {
        let transString = _.get(window.trans, key, key);
        if (params !== '') {
            for (const key of Object.keys(params)) {
                transString = transString.replace(':' + key, params[key]);
            }
        }
        return transString;
    };

    /**
     * Translate a key to the current language with a choice of singular or plural, depending on the receivedcount
     * @param {string} key - The key to translate
     * @param {number} count - The number of items
     * @param {object} params - The parameters to replace in the translation, 
     *                          the key should be of the form ':key' and will be replaced with the value of the param[key]
     * @returns {string}
     */
    const transChoice = (key, count, params = '') => {
        const termMulti = _.get(window.trans, key, key);
        const parts = termMulti.split('|');
        let transString = (count === 1) ? parts[0] : parts[1];
        if (params !== '') {
            for (const key of Object.keys(params)) {
                transString = transString.replace(':' + key, params[key]);
            }
        }
        return transString;
    };

    // prevent naming collisions with the old functions
    return {
        lang,
        translate: trans,
        translateChoice: transChoice,
        ucFirst
    };
}
