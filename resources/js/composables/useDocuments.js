import { computed, ref } from "vue";
import useApi from './generic/useApi.js';
import useToast from './generic/useToast.js';
import useLang from "./generic/useLang.js";
import useLibraries from "./useLibraries.js";

const documents = ref([]);
const documentSearchKey = ref("");
const tags = ref([]);
const { translate, translateChoice, ucFirst } = useLang();
const { failToast, successToast } = useToast();
const { getLibraries } = useLibraries();
const { apiGet, apiPut, apiPost, apiDel } = useApi();

const linkToAdd = ref({
    label: '',
    url: '',
    description: ''
});

// Add editing state
const linkToEdit = ref(null);
const isEditing = ref(false);

// Add a current library context
const currentLibraryId = ref(null);

const setCurrentLibrary = (libraryId) => {
    currentLibraryId.value = libraryId;
};

const startEditingLink = (document) => {
    linkToEdit.value = document;
    isEditing.value = true;
    // Populate the form with existing data
    linkToAdd.value = {
        label: document.label,
        url: document.url,
        description: document.description || ''
    };
};

const cancelEditingLink = () => {
    linkToEdit.value = null;
    isEditing.value = false;
    // Reset the form
    linkToAdd.value = {
        label: '',
        url: '',
        description: ''
    };
};

const updateLinkDocument = async () => {
    try {
        const response = await apiPut(`/api/updatelink/${linkToEdit.value.id}`, linkToAdd.value);
        const updatedDocument = response.data.document;
        
        // Update the document in the current library if we have one
        if (currentLibraryId.value) {
            await getLibraries(); // Refresh to get updated data
        }
        
        // Reset editing state
        cancelEditingLink();
        
        return updatedDocument;
    } catch (error) {
        throw error;
    }
};

const storeLinkDocument = async () => {
    if (isEditing.value) {
        return await updateLinkDocument();
    }
    
    try {
        const response = await apiPost('/api/storelink', linkToAdd.value);
        const newDocument = response.data.document;
        
        // If we have a current library, automatically link the document
        if (currentLibraryId.value) {
            await apiPut(`/api/attachdoctolib/${currentLibraryId.value}/${newDocument.id}`);
            // Refresh the library contents
            await getLibraries();
        }
        
        // Reset the form
        linkToAdd.value = {
            label: '',
            url: '',
            description: ''
        };
        
        return newDocument;
    } catch (error) {
        throw error;
    }
};

export default function useDocuments() {

    const getDocuments = async (libraryId) => {
        const response = await apiGet(`/api/documents/${libraryId}`);
        documents.value = response.data;
    };

    const filteredDocuments = computed(() => {
        return documents.value.filter(doc => doc.label.toLowerCase().includes(documentSearchKey.value.toLowerCase()));
    });

    const addDocumentToLibrary = async (libraryId, docId) => {
        try {
            await apiPut(`/api/attachdoctolib/${libraryId}/${docId}`);
            documentSearchKey.value = '';
            await getLibraries();
        } catch (error) {
            failToast(`${ucFirst(translate('generic.attachingdocumentfailed'))}: ${error}`);
        }
    }

    const removeDocumentFromLibrary = async (libraryId, docId) => {
        try {
            await apiPut(`/api/detachdocfromlib/${libraryId}/${docId}`);
            documentSearchKey.value = '';
            await getLibraries();
        } catch (error) {
            failToast(`${ucFirst(translate('generic.detachingdocumentfailed'))}: ${error}`);
        }
    }

    const getTags = async () => {
        try {
            const response = await apiGet('/api/document-tags');
            tags.value = response.data;
        } catch (error) {
            failToast(`${ucFirst(translate('generic.loadingtagsfailed'))}: ${error}`);
        }
    }

    const addTagToDocument = async (documentId, tagName) => {
        try {
            const response = await apiPost(`/api/documents/${documentId}/tags`, {
                tag_name: tagName
            });
            successToast(`${ucFirst(translateChoice('generic.tags', 1))} "${tagName}" ${translate('generic.addedsuccessfully')}`);
            return response.data.tag;
        } catch (error) {
            failToast(`${ucFirst(translate('generic.addingtagfailed'))}: ${error}`);
            throw error;
        }
    }

    const removeTagFromDocument = async (documentId, tagId) => {
        try {
            await apiDel(`/api/documents/${documentId}/tags/${tagId}`);
            successToast(`${ucFirst(translateChoice('generic.tags', 1))} ${translate('generic.removedsuccessfully')}`);
        } catch (error) {
            failToast(`${ucFirst(translate('generic.removingtagfailed'))}: ${error}`);
            throw error;
        }
    }

    const getDocumentTags = async (documentId) => {
        try {
            const response = await apiGet(`/api/documents/${documentId}/tags`);
            return response.data;
        } catch (error) {
            failToast(`${ucFirst(translate('generic.loadingdocumenttagsfailed'))}: ${error}`);
            return [];
        }
    }

    return {
        addDocumentToLibrary,
        addTagToDocument,
        cancelEditingLink,
        currentLibraryId,
        documentSearchKey,
        filteredDocuments,
        getDocuments,
        getDocumentTags,
        getTags,
        isEditing,
        linkToAdd,
        linkToEdit,
        removeDocumentFromLibrary,
        removeTagFromDocument,
        setCurrentLibrary,
        startEditingLink,
        storeLinkDocument,
        tags,
    };
}
