import { ref } from "vue";
import useApi from "./generic/useApi.js";
import useLang from "./generic/useLang.js";
import useToast from "./generic/useToast.js";

const { apiDel, apiGet } = useApi();
const { ucFirst, translate } = useLang();
const { failToast } = useToast();

const lessonConflicts = ref([]);
const dateExceptionToDelete = ref(0);
const dateExceptionIdToEdit = ref(0);
const tutorId = ref(0);
const chosenYearId = ref(0);
const showPastExceptions = ref(false);
const filteredDateExceptions = ref([]);

export default function useDateExceptions() {

    /**
     * filter school year into dateExceptionsByYear by comparing with the currently chosen year id
     * filter that list on tutors, where 0 means all tutors (including entire school)
     * and -1 means only "entire school" exceptions.
     * if tutor id other than 0 or -1: show the date exceptions of the selected tutor.
     * @returns {*[]}
     */
    const getDateExceptionsForList = async () => {
        // only if schoolyear is chosen!
        if (chosenYearId.value === 0) return;
        const queryParams = {
            sy: chosenYearId.value,
            tutor: tutorId.value,
            past: showPastExceptions.value ? 1 : 0
        };
        try {
            const response = await apiGet('/api/dateexceptionsforlist', { params: queryParams });
            filteredDateExceptions.value = response.data;
            getLessonConflicts();
        } catch (error) {
            failToast(error.message, translate('generic.error'));
        }
    }

    const deleteDateException = async () => {
        try {
            await apiDel(`/api/dateexceptions/${dateExceptionToDelete.value}`);
        } catch (error) {
            throw error;
        }
    };

    /**
     * get the lesson conflicts for all date exceptions from the server
     * @visibility private
     */
    const getLessonConflicts = async () => {
        lessonConflicts.value = [];
        for (const de of filteredDateExceptions.value) {
            try {
                const response = await apiGet(`/api/dateexceptions/${de.id}/lessonconflicts`);
                lessonConflicts.value.push(response.data);
            } catch (error) {
                failToast(
                    error.message,
                    ucFirst(translate('generic.error'))
                );
            }
        }
    };

    /**
     * show the number of conflicts for a date exception
     * if there are any sticky events, count them separately so we know if all conflicts have been resolved
     * @param dateExceptionId
     * @returns {{result: string, code: string}|{result: string, code: (string)}}
     */
    const showConflictsForDateException = (dateExceptionId) => {
        // CLASS colors
        const colorTable = {
            green: '#4C9B5E',
            red: '#C75454'
        };
        const conflicts = lessonConflicts.value.find(lc => {
            return parseInt(lc.dateExceptionId) === parseInt(dateExceptionId)
        });
        if (!conflicts) return {code: colorTable.green, result: "0/0"};
        // if conflicts.conflicts is an object, turn it into an array
        // turns out this only happens when we have 1 conflict
        if (!Array.isArray(conflicts.conflicts)) {
            conflicts.conflicts = [conflicts.conflicts];
        }
        const numberOfEventsThatAreSticky = conflicts.conflicts.filter(e => e.flag_sticky).length;
        return {
            code: conflicts.conflicts.length > numberOfEventsThatAreSticky ? colorTable.red : colorTable.green,
            result: `${conflicts.conflicts.length}&nbsp;/&nbsp;${numberOfEventsThatAreSticky}`
        };
    };

    return {
        chosenYearId,
        dateExceptionIdToEdit,
        dateExceptionToDelete,
        deleteDateException,
        filteredDateExceptions,
        getDateExceptionsForList,
        lessonConflicts,
        showConflictsForDateException,
        showPastExceptions,
        tutorId,
    }
}
