import { ref } from 'vue';
import useApi from './generic/useApi.js';
import useToast from './generic/useToast.js';
import useLang from './generic/useLang.js';

const logentries = ref([]);
const logentry = ref({});
const logentryToDelete = ref({ entry: '' });
const loading = ref(false);

export default function useStudentLog() {
    const { apiGet, apiPost, apiDel } = useApi();
    const { successToast, failToast } = useToast();
    const { translate, ucFirst } = useLang();

    const getLogentries = async (studentId) => {
        loading.value = true;
        try {
            const response = await apiGet(`/api/studentlogentries/${studentId}`);
            logentries.value = response.data.data;
        } catch (error) {
            failToast(`Error retrieving student logentries: ${error}`);
        } finally {
            loading.value = false;
        }
    };

    const setLogentry = (logentryData) => {
        if (!logentryData) {
            logentry.value = {};
        } else {
            logentry.value = logentryData;
        }
    };

    const saveLogEntry = async (studentId) => {
        const logid = logentry.value.id || 0;
        const axiosURL = `/api/studentlogentry/${logid}`;
        const data = { entry: logentry.value.entry, student_id: studentId };

        try {
            await apiPost(axiosURL, data);
            await getLogentries(studentId);
            successToast(ucFirst(translate('generic.savesuccess')));
        } catch (error) {
            failToast(ucFirst(translate('generic.savingfailed')) + ` - ${error}`);
        }
    };

    const setLogentryToDelete = (logentryData) => {
        logentryToDelete.value = logentryData;
    };

    const deleteLogentry = async (studentId) => {
        try {
            await apiDel('/api/studentlogentry/' + logentryToDelete.value.id);
            await getLogentries(studentId);
            successToast(ucFirst(translate('generic.deletesuccessful')));
        } catch (error) {
            failToast(ucFirst(translate('generic.deletefailed')) + ` - ${error}`);
        }
    };

    return {
        deleteLogentry,
        getLogentries,
        loading,
        logentries,
        logentry,
        logentryToDelete,
        saveLogEntry,
        setLogentry,
        setLogentryToDelete
    };
} 
