import { ref } from 'vue';
import useApi from "./generic/useApi.js";
import useToast from "./generic/useToast.js";
import useLang from "./generic/useLang.js";

const studentGroups = ref([]);
const { apiGet, apiDel } = useApi();
const { failToast } = useToast();
const { ucFirst, translate } = useLang();
const busy = ref(false);
const studentGroupToDelete = ref(0);

export default function useStudentGroups() {
    const getStudentGroups = async () => {
        busy.value = true;
        try {
            const response = await apiGet('/api/studentgroups');
            studentGroups.value = response.data.data;
            busy.value = false;
        } catch (err) {
            failToast(
                ucFirst(translate('generic.errorloadingstudentgroups')) + ' ' + err,
                ucFirst(translate('generic.error'))
            );
            busy.value = false;
        }
    };
    const deleteStudentGroup = async () => {
        busy.value = true;
        try {
            await apiDel(`/api/studentgroups/${studentGroupToDelete.value}`);
            // reload
            await getStudentGroups();
            busy.value = false;
        } catch (err) {
            failToast(
                ucFirst(translate('generic.errordeletingstudentgroup')) + ' ' + err,
                ucFirst(translate('generic.error'))
            );
            busy.value = false;
        }
    };
    return {
        busy,
        deleteStudentGroup,
        getStudentGroups,
        studentGroups,
        studentGroupToDelete
    };
}
