import { computed, ref } from 'vue';
import useApi from './generic/useApi.js';
import useToast from './generic/useToast.js';

const emailLog = ref([]);
const fetchTimer = ref(null);
const showingAll = ref(false);
const { apiGet, apiDel } = useApi();
const { failToast, successToast } = useToast();

export default function useEmailLog() {

    const fetchEmailLog = async (params = {}) => {
        try {
            const response = await apiGet('/api/emaillogentries', { params });
            emailLog.value = response.data.data || [];
            showingAll.value = params.all === 'true';
            return response.data; // Return full response for pagination data
        } catch (error) {
            failToast(error.message);
            throw error;
        }
    };

    const listHasQueuedEmails = computed(() => {
        return emailLog.value.some((entry) => entry.status === 'queued');
    });

    const getEmailLog = async (params = {}) => {
        const response = await fetchEmailLog(params);

        // Auto-refresh logic for queued emails
        // Only if we're not showing all entries and we're on page 1
        if (listHasQueuedEmails.value && !params.all && (!params.page || params.page === 1)) {
            fetchTimer.value = setInterval(async () => {
                await fetchEmailLog(params);
                if (!listHasQueuedEmails.value) {
                    clearInterval(fetchTimer.value);
                }
            }, 5000);
        } else {
            if (fetchTimer.value) {
                clearInterval(fetchTimer.value);
            }
        }

        return response;
    };

    const deleteFromEmailLog = async (id) => {
        try {
            await apiDel(`/api/emaillogentries/${id}`);
            successToast('Email log entry deleted successfully');
        } catch (error) {
            failToast(error.message);
            throw error;
        }
    };

    const stopAutoRefresh = () => {
        if (fetchTimer.value) {
            clearInterval(fetchTimer.value);
            fetchTimer.value = null;
        }
    };

    return {
        // State
        emailLog,
        showingAll,
        listHasQueuedEmails,

        // Methods
        getEmailLog,
        fetchEmailLog,
        deleteFromEmailLog,
        stopAutoRefresh
    };
}
