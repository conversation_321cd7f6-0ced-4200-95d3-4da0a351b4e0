import useDateTime from './generic/useDateTime.js';

// Import the date utility functions
const { getWeekNumber, getHighestWeekNumber } = useDateTime();

export default function useEventWeeks() {
    /**
     * Determine the date range (years and weeks) based on school year and events
     * @param {Object} schoolYear - The current or future school year
     * @param {Array} events - The events to analyze
     * @returns {Object} - The date range information
     */
    const determineDateRange = (schoolYear, events) => {
        // Default to school year's start and end years
        let lowerValuesYear = schoolYear?.start_year;
        let upperValuesYear = schoolYear?.end_year;
        
        // Default week values based on school year
        let firstWeekInStartYear = getWeekNumber(schoolYear?.start_date);
        let lastWeekInStartYear = getHighestWeekNumber(lowerValuesYear); // 52 or 53
        let lastWeekInUpperYear = getWeekNumber(schoolYear?.end_date);
        
        // Check if there are events outside the school year range
        if (events.length > 0) {
            // Sort events by date to find earliest and latest
            const sortedEvents = [...events].sort((a, b) => {
                return new Date(a.start) - new Date(b.start);
            });
            
            // Get earliest and latest event years and weeks
            const earliestEvent = sortedEvents[0];
            const latestEvent = sortedEvents[sortedEvents.length - 1];
            const earliestEventYear = parseInt(earliestEvent.start.substring(0, 4));
            const latestEventYear = parseInt(latestEvent.start.substring(0, 4));
            const earliestEventWeek = getWeekNumber(earliestEvent.start);
            const latestEventWeek = getWeekNumber(latestEvent.start);
            
            // Adjust years if events exist outside school year range
            if (earliestEventYear < lowerValuesYear) {
                lowerValuesYear = earliestEventYear;
                // If earliest event is in an earlier year, use its week number
                firstWeekInStartYear = earliestEventWeek;
            } else if (earliestEventYear === lowerValuesYear && earliestEventWeek < firstWeekInStartYear) {
                // If earliest event is in same year but earlier week, use its week number
                firstWeekInStartYear = earliestEventWeek;
            }
            
            if (latestEventYear > upperValuesYear) {
                upperValuesYear = latestEventYear;
                // If latest event is in a later year, use its week number
                lastWeekInUpperYear = latestEventWeek;
            } else if (latestEventYear === upperValuesYear && latestEventWeek > lastWeekInUpperYear) {
                // If latest event is in same year but later week, use its week number
                lastWeekInUpperYear = latestEventWeek;
            }
        }

        return {
            lowerValuesYear,
            upperValuesYear,
            firstWeekInStartYear,
            lastWeekInStartYear,
            lastWeekInUpperYear
        };
    };

    /**
     * Create an empty array of weeks for the given date range
     * @param {number} year1 - The start year
     * @param {number} startweek1 - The first week of the start year
     * @param {number} endWeek1 - The last week of the start year
     * @param {number} year2 - The end year
     * @param {number} startWeek2 - The first week of the end year
     * @param {number} endWeek2 - The last week of the end year
     * @returns {Array} - An array of week objects with empty events arrays
     */
    const getEmptyStartArrayOfWeeks = (year1, startweek1, endWeek1, year2, startWeek2, endWeek2) => {
        const weeks = [];
        for (let i = startweek1; i <= endWeek1; i++) {
            weeks.push({ weekNumber: i, year: year1, events: [] });
        }
        for (let i = startWeek2; i <= endWeek2; i++) {
            weeks.push({ weekNumber: i, year: year2, events: [] });
        }
        return weeks;
    };

    /**
     * Group events by week and year
     * @param {Array} events - The events to group
     * @param {Array} eventsArray - The array of week objects to fill
     * @returns {Array} - The updated array of week objects with events
     */
    const groupEventsByWeek = (events, eventsArray) => {
        // Sort events by start date
        events.sort((a, b) => {
            return new Date(a.start) - new Date(b.start);
        });
        
        // Group events by week number and year
        events.forEach((event) => {
            const weekNumber = getWeekNumber(event.start);
            const year = parseInt(event.start.substring(0, 4));
            const existingWeek = eventsArray.find((week) => week.weekNumber === weekNumber && week.year === year);
            if (!existingWeek) {
                console.error('week not found in array', weekNumber, year);
                return;
            }
            existingWeek.events.push(event);
            // if we have more than 1 event in this week, sort them by start date.
            // this is unlikely to happen often
            if (existingWeek.events.length > 1) {
                existingWeek.events.sort((a, b) => {
                    return new Date(a.start) - new Date(b.start);
                });
            }
        });
        
        return eventsArray;
    };

    /**
     * Handle week numbering across year boundaries and fill in missing weeks
     * @param {Array} eventsArray - The array of week objects
     * @param {number} lowerValuesYear - The start year
     * @param {number} lastWeekInStartYear - The last week of the start year
     * @param {number} upperValuesYear - The end year
     * @returns {Array} - The updated array of week objects
     */
    const handleWeekNumberingAndFillMissingWeeks = (eventsArray, lowerValuesYear, lastWeekInStartYear, upperValuesYear) => {
        // Find the split point where week numbers reset (year boundary)
        const splitIndex = eventsArray.findIndex((week, index) => {
            return index > 0 && week.weekNumber < eventsArray[index - 1].weekNumber;
        });
        
        if (splitIndex > -1) {
            const firstPart = eventsArray.slice(0, splitIndex);
            const secondPart = eventsArray.slice(splitIndex);
            
            // Check if we are missing week numbers at the end of the start year
            if (firstPart[firstPart.length - 1].weekNumber < lastWeekInStartYear) {
                const missingWeeks = [];
                for (let i = firstPart[firstPart.length - 1].weekNumber + 1; i <= lastWeekInStartYear; i++) {
                    missingWeeks.push({ weekNumber: i, year: lowerValuesYear, events: [] });
                }
                firstPart.push(...missingWeeks);
            } else {
                firstPart[firstPart.length - 1].year = lowerValuesYear;
            }
            
            // Check if we are missing a week in the consecutive numbering of weeks of the first part
            firstPart.forEach((week, index) => {
                if (index > 0 && week.weekNumber - firstPart[index - 1].weekNumber > 1) {
                    // we are missing a week, so add it
                    const missingWeeks = [];
                    for (let i = firstPart[index - 1].weekNumber + 1; i < week.weekNumber; i++) {
                        missingWeeks.push({ weekNumber: i, year: lowerValuesYear, events: [] });
                    }
                    firstPart.splice(index, 0, ...missingWeeks);
                } else {
                    // add the year to the week
                    week.year = lowerValuesYear;
                }
            });
            
            // If this part does not start with week 1, we are missing weeks at the beginning.
            // Those weeks are in the previous year (lowerValuesYear)
            if (secondPart[0].weekNumber > 1) {
                const missingWeeks = [];
                for (let i = 1; i < secondPart[0].weekNumber; i++) {
                    missingWeeks.push({ weekNumber: i, year: lowerValuesYear, events: [] });
                }
                secondPart.unshift(...missingWeeks);
            } else {
                // add the year to the week
                secondPart[0].year = lowerValuesYear;
            }
            
            // Check if we are missing a week in the consecutive numbering of weeks of the second part
            secondPart.forEach((week, index) => {
                if (index > 0 && week.weekNumber - secondPart[index - 1].weekNumber > 1) {
                    // we are missing a week, so add it
                    const missingWeeks = [];
                    for (let i = secondPart[index - 1].weekNumber + 1; i < week.weekNumber; i++) {
                        missingWeeks.push({ weekNumber: i, year: upperValuesYear, events: [] });
                    }
                    secondPart.splice(index, 0, ...missingWeeks);
                } else {
                    // add the year to the week
                    week.year = upperValuesYear;
                }
            });
            
            eventsArray = [...firstPart, ...secondPart];
        }
        
        return eventsArray;
    };

    /**
     * Process events and organize them by week
     * @param {Object} schoolYear - The current or future school year
     * @param {Array} events - The events to process
     * @returns {Array} - An array of week objects with events
     */
    const processEventsByWeek = (schoolYear, events) => {
        // Determine date range
        const {
            lowerValuesYear,
            upperValuesYear,
            firstWeekInStartYear,
            lastWeekInStartYear,
            lastWeekInUpperYear
        } = determineDateRange(schoolYear, events);
        
        // Create empty array of weeks
        let eventsArray = getEmptyStartArrayOfWeeks(
            lowerValuesYear,
            firstWeekInStartYear,
            lastWeekInStartYear,
            upperValuesYear,
            1,
            lastWeekInUpperYear
        );
        
        // Group events by week
        eventsArray = groupEventsByWeek(events, eventsArray);
        
        // Handle week numbering across year boundaries and fill in missing weeks
        eventsArray = handleWeekNumberingAndFillMissingWeeks(
            eventsArray,
            lowerValuesYear,
            lastWeekInStartYear,
            upperValuesYear
        );
        
        return eventsArray;
    };

    return {
        determineDateRange,
        getEmptyStartArrayOfWeeks,
        groupEventsByWeek,
        handleWeekNumberingAndFillMissingWeeks,
        processEventsByWeek
    };
}
