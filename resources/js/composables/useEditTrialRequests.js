import { computed, ref, watch } from "vue";
import useApi from "./generic/useApi.js";
import useDateTime from "./generic/useDateTime.js";
import useToast from "./generic/useToast.js";
import useLang from "./generic/useLang.js";

const trialRequests = ref([]);
const trialRequest = ref({});
const idToView = ref(0);
const idToDelete = ref(0);
const orderBy = ref("created_at");
const dirAsc = ref(true);
const allStatuses = ref([]);

const { displayAge } = useDateTime();
const { failToast, successToast } = useToast();
const { translate, ucFirst } = useLang();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

export default function useEditTrialRequests() {

    /**
     * lookup the requested trial request by the trial student id
     */
    watch(idToView, async () => {
        if (idToView.value === 0) {
            trialRequest.value = {};
        } else if (idToView.value > 0) {
            trialRequest.value = trialRequests.value.find(entry => entry.trialstudent.id === idToView.value);
        }
    });

    /**
     * refresh or initiate from backend
     * @returns {Promise<void>}
     */
    const getAllTrialRequests = async () => {
        try {
            const resp = await apiGet('/api/gettrialrequests');
            trialRequests.value = resp.data;
        } catch (error) {
            failToast(error.message);
        }
    }

    const getAllStatuses = async () => {
        try {
            const resp = await apiGet("/api/trialrequeststatuses");
            allStatuses.value = resp.data;
        } catch (error) {
            failToast(error.message);
        }
    }

    const orderedTrialRequests = computed(() => {
        if (dirAsc.value) {
            return trialRequests.value.sort((a, b) => {
                if (orderBy.value === "created_at") {
                    return a.created_at > b.created_at ? 1 : -1;
                } else if (orderBy.value === "status") {
                    return a.trialstudent.trialrequeststatus.id > b.trialstudent.trialrequeststatus.id ? 1 : -1;
                } else {
                    return a.trialstudent.lastname > b.trialstudent.lastname ? 1 : -1;
                }
            });
        } else {
            return trialRequests.value.sort((a, b) => {
                if (orderBy.value === "created_at") {
                    return a.created_at < b.created_at ? 1 : -1;
                } else if (orderBy.value === "status") {
                    return a.trialstudent.trialrequeststatus.id < b.trialstudent.trialrequeststatus.id ? 1 : -1;
                } else {
                    return a.trialstudent.lastname < b.trialstudent.lastname ? -1 : 1;
                }
            });
        }
    });

    /**
     * form 1 string based on firstname, lastname and optionally preposition
     * prevents having two spaces if preposition is empty
     r* @param trialStudent
     * @returns {string}
     */
    const getFullName = (trialStudent) => {
        const fullName = trialStudent.preposition != null && trialStudent.preposition !== ''
            ? `${trialStudent.firstname} ${trialStudent.preposition} ${trialStudent.lastname}`
            : `${trialStudent.firstname} ${trialStudent.lastname}`;
        return `${fullName} (${displayAge(trialStudent.date_of_birth)})`;
    };

    const deleteTrialRequest = async () => {
        try {
            await apiDel(`/api/trialstudents/${idToDelete.value}`);
            // refresh list from backend
            await getAllTrialRequests();
            successToast(ucFirst(translate('generic.deletesuccessful')));
        } catch (error) {
            failToast(ucFirst((translate('generic.deletefailed'))) + ': ' + error.message);
        }
    };

    const createStudentFromTrialRequest = async (trialStudentId) => {
        try {
            const data = {
                trialStudentId
            };
            const response = await apiPost('/api/studentfromtrialstudents', data);
            const generatedStudentId = response.data.generated_student_id;
            successToast(ucFirst(translate('generic.studentcreatedfromtrialrequest')));
            // redirect after 2 seconds (read success message first)
            setTimeout(() => {
                window.location.href = `/students/${generatedStudentId}/edit`;
            }, 1000);
        } catch (error) {
            failToast(ucFirst(translate('generic.error')) + ': ' + error.message);
        }
    };

    const updateTrialRequest = async (trialStudent) => {
        try {
            await apiPut(`/api/trialstudents/${trialStudent.id}`, trialStudent);
            successToast(ucFirst(translate('generic.updatesuccess')));
            // refresh list from backend
            await getAllTrialRequests();
        } catch (error) {
            failToast(ucFirst(translate('generic.updatefailed')) + ": " + error.message);
        }
    }

    return {
        allStatuses,
        createStudentFromTrialRequest,
        deleteTrialRequest,
        dirAsc,
        getAllStatuses,
        getAllTrialRequests,
        getFullName,
        idToDelete,
        idToView,
        orderBy,
        orderedTrialRequests,
        trialRequest,
        trialRequests,
        updateTrialRequest
    };
}
