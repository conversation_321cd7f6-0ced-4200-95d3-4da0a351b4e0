// New Planning form variables
import { computed, ref } from 'vue';
import moment from 'moment';
import useBaseData from './generic/useBaseData.js';

const date = ref(moment().format('DD-MM-YYYY'));
const time = ref(moment().format('HH:mm'));
// refactor: date and time should be a date object and a string (plain javascript, not dependent on package)
// when using these values we can use wichever package we want (probably date-fns)
const dbStartDate = ref(null);
const dbStartTime = ref("");

const repeats = ref(0);
const locationId = ref(0);
const locationIdAlt = ref(0);
const tutorId = ref(0);

const { allLocations } = useBaseData();

export default function usePlanningCreateForm () {
    const locationsFirstChoice = computed(() => {
        return allLocations.value;
    });

    const locationsSecondChoice = computed(() => {
        return allLocations.value.filter((location) => {
            return location.id !== locationId.value;
        });
    });

    return {
        date,
        locationId,
        locationIdAlt,
        locationsFirstChoice,
        locationsSecondChoice,
        repeats,
        dbStartDate,
        dbStartTime,
        time,
        tutorId
    };
}
