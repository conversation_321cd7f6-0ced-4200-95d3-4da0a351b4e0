import { computed, ref } from 'vue';
import useApi from "./generic/useApi.js";
import useToast from "./generic/useToast.js";

const { apiGet } = useApi();
const { failToast } = useToast();
const registrationData = ref([]);
const busy = ref(false);

export default function useRegistrations () {
    const getRegistrationData = async () => {
        // Fetch data from API getregistrationdata
        try {
            busy.value = true;
            const response = await apiGet('/api/getregistrationdata');
            registrationData.value = response.data;
        } catch (error) {
            failToast(error.message);
        } finally {
            busy.value = false;
        }
    };

    const registrationsForThisYear = computed(() => {
        return registrationData.value?.registrationsThisYear;
    });

    const registrationsForCourses = computed(() => {
        return registrationData.value?.registrations;
    });

    return {
        busy,
        getRegistrationData,
        registrationData,
        registrationsForCourses,
        registrationsForThisYear
    }
}
