import { ref, watch, computed } from 'vue';
import useToast from './generic/useToast.js';
import useLang from './generic/useLang.js';
import useApi from './generic/useApi.js';
const { failToast, successToast } = useToast();
const { translate, ucFirst } = useLang();

// Single student variables
const studentId = ref(0);
const validStudentId = ref(false);
const studentDataById = ref({firstname: '', lastname: ''});
const delStudentLastname = ref('');
const validLastnameEntered = ref(false);

// Multiple students variables
const students = ref([]);
const busy = ref(false);
const studentSearchFilter = ref('');

const { apiGet, apiDel, apiPut } = useApi();

export default function useStudent() {
    // Single student functions
    const getStudentData = async () => {
        const response = await apiGet(`/api/students/${studentId.value}`);
        studentDataById.value = response.data;
    };

    const deleteStudent = async () => {
        if (validLastnameEntered.value) {
            try {
                await apiDel(`/api/students/full/${studentId.value}`);
                successToast(translate('generic.studentdataremoved'));
            } catch (error) {
                failToast(translate('generic.studentdatacouldnotberemoved'));
            }
        } else {
                failToast(`${translate('generic.lastnameincorrect')} ${delStudentLastname.value} vs ${studentDataById.value.lastname}`);
        }
    };

    const resetStudentId = () => {
        studentId.value = 0;
        studentDataById.value = {firstname: '', lastname: ''};
        delStudentLastname.value = '';
        validStudentId.value = false;
        validLastnameEntered.value = false;
    };

    watch(studentId, () => {
        validStudentId.value = !isNaN(studentId.value) && studentId.value > 0;
    });

    watch(delStudentLastname, () => {
        validLastnameEntered.value = delStudentLastname.value.toLowerCase() === studentDataById.value.lastname.toLowerCase();
    });

    // Multiple students functions
    const getStudents = async () => {
        busy.value = true;
        try {
            const response = await apiGet('/api/students?onlystudents=true');
            students.value = response.data.data;
        } catch (error) {
            throw error;
        } finally {
            busy.value = false;
        }
    };

    const savePinForStudent = async (studentid, apipin) => {
        if (apipin.match(/^\d{6}$/gm)) {
            try {
                await apiPut('/api/studentsavepin', { studentid, apipin });
                return { success: true, message: ucFirst(translate('generic.pinsavedcorrectly')) };
            } catch (error) {
                throw error;
            }
        } else {
            throw new Error(ucFirst(translate('generic.pinnotvalid')) + ': ' + translate('generic.pinrestrictionsare'));
        }
    };

    const showCourseDetails = (detailsArray) => {
        return detailsArray.map(c => {
            return (c.please_keep_scheduled_time !== null)
                ? `${c.name} &nbsp;<i class='fas fa-lock'></i>`
                : c.name;
        }).join('<br>');
    };

    const activeStudents = computed(() => {
        return students.value.filter(
            student =>
                (
                    (student.currentcoursessarray.length !== 0) &&
                    (student.name.toLowerCase().includes(studentSearchFilter.value.toLowerCase()))
                )
        );
    });

    const inactiveStudents = computed(() => {
        return students.value.filter(
            student =>
                (
                    (student.currentcoursessarray.length === 0) &&
                    (student.name.toLowerCase().includes(studentSearchFilter.value.toLowerCase()))
                )
        );
    });

    return {
        // Single student exports
        deleteStudent,
        getStudentData,
        resetStudentId,
        studentId,
        validStudentId,
        studentDataById,
        delStudentLastname,
        validLastnameEntered,

        // Multiple students exports
        getStudents,
        savePinForStudent,
        showCourseDetails,
        students,
        busy,
        studentSearchFilter,
        activeStudents,
        inactiveStudents
    };
}
