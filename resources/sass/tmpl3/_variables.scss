// SASS Variables for Vue Single File Components
// This file is kept minimal for Vue SFC compatibility
// Main styles are now in public/css/class-variables.css and public/css/class-styles.css

// CLASS Brand colors
$classred: #C75454;
$classblue: #6A6E8F;
$classdarkblue: #425668;
$classgreen: #4C9B5E;
$classyellow: #EDA707;
$classbgblue: #6A6E8F;
$classlightblue: #a8e0ec;
$classlightestblue: rgba(138, 221, 236, 0.39);
$classlight: #f8f9fa;
$classdark: #343a40;

// Base colors
$white: #ffffff;
$black: #000000;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

// Theme colors
$primary: $classblue;
$success: $classgreen;
$danger: $classred;
$warning: $classyellow;
