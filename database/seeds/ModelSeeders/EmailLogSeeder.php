<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory;
use Carbon\Carbon;

class EmailLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * @return void
     */
    public function run()
    {
        $faker = Factory::create();
        DB::table('emaillogentries')->insert([
            [
                'id' => 1,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => 1,
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => Carbon::now()->subMonth(),
                'updated_at' => Carbon::now()->subMonth(),
            ],
            [
                'id' => 2,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->randomHtml(2, 3),
                'attachments' => $faker->paragraph,
                'studentids' => "1,2",
                'unique_token' => $faker->uuid,
                'status' => 'unknown',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 3,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(5),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "1,2",
                'unique_token' => $faker->uuid,
                'status' => 'failed',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 4,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(7),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "1,2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 5,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "1,2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 6,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 7,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 8,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 9,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 10,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 11,
                'domain_id' => 9,
                'to' => $faker->email,
                'cc' => $faker->email,
                'bcc' => $faker->email,
                'from' => $faker->email,
                'subject' => $faker->sentence(10),
                'body' => $faker->paragraph,
                'attachments' => $faker->paragraph,
                'studentids' => "2",
                'unique_token' => $faker->uuid,
                'status' => 'sent',
                'log' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }
}
