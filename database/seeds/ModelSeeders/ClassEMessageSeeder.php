<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory;

class ClassEMessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Factory::create();
        DB::table('messages')->insert([
            [
                'id' => 1,
                'domain_id' => 9,
                'from_id' => 1,
                'from_type' => 'admin',
                'from_label' => '<PERSON>',
                'to_id' => 2,
                'to_type' => 'tutor',
                'to_label' => 'Petra Kats',
                'subject' => 'Afspraak gewijzigd',
                'body' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
                'read_at' => now(),
            ], [
                'id' => 2,
                'domain_id' => 9,
                'from_id' => 2,
                'from_type' => 'tutor',
                'from_label' => 'Petra Kats',
                'to_id' => 1,
                'to_type' => 'admin',
                'to_label' => '<PERSON>',
                'subject' => 'Afspraak gewijzigd',
                'body' => $faker->paragraph,
                'created_at' => now(),
                'updated_at' => now(),
                'read_at' => null,
            ]
        ]);
    }
}
