<?php
namespace Database\Seeders\ModelSeeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory;

class TasksSeeder extends Seeder {

	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run() {
        $faker = Factory::create();
        /*
        create table tasks
(
    id               int unsigned auto_increment primary key,
    domain_id        int unsigned default '0' not null,
    tasktype_id      int unsigned             not null,
    student_id       int unsigned             null,
    course_id        int unsigned             null,
    tutor_id         int unsigned             null,
    registration_id  int unsigned             null,
    event_id         int unsigned             null,
    assigned_user_id int unsigned             null,
    date_opened      date                     not null,
    date_due         date                     null,
    date_closed      date                     null,
    remarks          text                     null,
    created_at       timestamp                null,
    updated_at       timestamp                null,
    constraint tasks_assigned_user_id_foreign
        foreign key (assigned_user_id) references users (id),
    constraint tasks_course_id_foreign
        foreign key (course_id) references courses (id),
    constraint tasks_domain_id_foreign
        foreign key (domain_id) references domains (id),
    constraint tasks_event_id_foreign
        foreign key (event_id) references events (id),
    constraint tasks_registration_id_foreign
        foreign key (registration_id) references course_student (id),
    constraint tasks_student_id_foreign
        foreign key (student_id) references students (id),
    constraint tasks_tasktype_id_foreign
        foreign key (tasktype_id) references tasktypes (id)
            on delete cascade,
    constraint tasks_tutor_id_foreign
        foreign key (tutor_id) references users (id)
)
    collate = utf8mb4_unicode_ci;

create index domain_task_domain_id_foreign
    on tasks (domain_id);
        */

        $tasks = [];

        // Task 1: Trial course registration (actionnaftertriallesson)
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 1, // actionnaftertriallesson
            'student_id' => 4,
            'course_id' => 6, // trial course
            'tutor_id' => 2,
            'registration_id' => 1, // trial course registration
            'assigned_user_id' => 1,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => null,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 2: Single lesson registration (actionaftersinglelesson)
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $dateClosed = $faker->boolean(30) ? $faker->dateTimeBetween($dateOpened, $dateDue)->format('Y-m-d') : null;
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 2, // actionaftersinglelesson
            'student_id' => 7,
            'course_id' => 4, // regular course with recurrence
            'tutor_id' => 1,
            'registration_id' => 11, // regular course registration
            'assigned_user_id' => null, // not assigned
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => $dateClosed,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 3: Trial course registration (actionnaftertriallesson)
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $dateClosed = $faker->boolean(30) ? $faker->dateTimeBetween($dateOpened, $dateDue)->format('Y-m-d') : null;
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 1, // actionnaftertriallesson
            'student_id' => 3,
            'course_id' => 3, // trial course
            'tutor_id' => 2,
            'registration_id' => null,
            'assigned_user_id' => 1,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => $dateClosed,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 4: Regular course task
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $dateClosed = $faker->boolean(30) ? $faker->dateTimeBetween($dateOpened, $dateDue)->format('Y-m-d') : null;
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 2, // actionaftersinglelesson
            'student_id' => 1,
            'course_id' => 1, // regular course
            'tutor_id' => 1,
            'registration_id' => 9, // regular course registration
            'assigned_user_id' => 2,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => $dateClosed,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 5: Regular course task
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 2, // actionaftersinglelesson
            'student_id' => 5,
            'course_id' => 2, // regular course
            'tutor_id' => 2,
            'registration_id' => 7, // regular course registration
            'assigned_user_id' => 1,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => null,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 6: Regular course task
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $dateClosed = $faker->boolean(30) ? $faker->dateTimeBetween($dateOpened, $dateDue)->format('Y-m-d') : null;
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 1, // actionnaftertriallesson
            'student_id' => 6,
            'course_id' => 1, // regular course
            'tutor_id' => 1,
            'registration_id' => 4, // regular course registration
            'assigned_user_id' => 2,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => $dateClosed,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 7: Regular course task
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $dateClosed = $faker->boolean(30) ? $faker->dateTimeBetween($dateOpened, $dateDue)->format('Y-m-d') : null;
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 2, // actionaftersinglelesson
            'student_id' => 8,
            'course_id' => 4, // regular course
            'tutor_id' => 2,
            'registration_id' => 8, // regular course registration
            'assigned_user_id' => 1,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => $dateClosed,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 8: Regular course task
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 1, // actionnaftertriallesson
            'student_id' => 9,
            'course_id' => 5, // regular course
            'tutor_id' => 1,
            'registration_id' => 12, // regular course registration
            'assigned_user_id' => 2,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => null,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 9: Regular course task
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $dateClosed = $faker->boolean(30) ? $faker->dateTimeBetween($dateOpened, $dateDue)->format('Y-m-d') : null;
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 2, // actionaftersinglelesson
            'student_id' => 2,
            'course_id' => 4, // regular course
            'tutor_id' => 2,
            'registration_id' => 6, // regular course registration
            'assigned_user_id' => 1,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => $dateClosed,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Task 10: Trial course task
        $dateOpened = $faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d');
        $dateDue = $faker->dateTimeBetween($dateOpened, '+30 days')->format('Y-m-d');
        $tasks[] = [
            'domain_id' => 9,
            'tasktype_id' => 1, // actionnaftertriallesson
            'student_id' => 3,
            'course_id' => 6, // trial course
            'tutor_id' => 1,
            'registration_id' => null,
            'assigned_user_id' => 2,
            'date_opened' => $dateOpened,
            'date_due' => $dateDue,
            'date_closed' => null,
            'remarks' => $faker->paragraph,
            'created_at' => now(),
            'updated_at' => now()
        ];

	    DB::table('tasks')->insert($tasks);
	}

}
