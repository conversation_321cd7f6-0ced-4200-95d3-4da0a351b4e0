<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('users')->insert([[
            'id'		    => 1,
            'domain_id'     => 9,
            'name'		    => '<PERSON>',
            'avatar'        => '/images/test/jan.jpg',
            'hexcolor'      => '#C75454',
            'email'		    => '<EMAIL>',
            'password'	    => Hash::make('cladmin')
        ], [
            'id'		    => 2,
            'domain_id'     => 9,
            'name'		    => 'Petra Kats',
            'avatar'        => '/images/test/petra.jpg',
            'hexcolor'      => '#6A6E8F',
            'email'		    => '<EMAIL>',
            'password'	    => Hash::make('cltutor')
        ], [
            'id'		    => 3,
            'domain_id'     => 9,
            'name'		    => 'Scolavisa Demo',
            'avatar'        => '/images/test/russian-blue-cat.jpg',
            'hexcolor'      => '#6699CC',
            'email'		    => '<EMAIL>',
            'password'	    => Hash::make('clscolavisa')
        ], [
            'id'		    => 4,
            'domain_id'     => 10,
            'name'		    => 'Janneke van Kanines',
            'avatar'        => '/images/test/janneke.png',
            'hexcolor'      => '#C75454',
            'email'		    => '<EMAIL>',
            'password'	    => Hash::make('cladmin')
        ]
        ]);
        DB::table('role_user')->insert([[
            "user_id" => "1",
            "role_id" => "1"    // Jan: admin
        ], [
            "user_id" => "1",
            "role_id" => "2"    // Jan: tutor
        ], [
            "user_id" => "2",
            "role_id" => "2"    // Petra: tutor, for ClassE and planning in Class
        ], [
            "user_id" => "3",
            "role_id" => "1"    // scolavisa: admin
        ], [
            "user_id" => "3",
            "role_id" => "9"    // scolavisa: scolavisa, for Pulse
        ], [
            "user_id" => "4",
            "role_id" => "1"    // Janneke: admin
        ], [
            "user_id" => "4",
            "role_id" => "2"    // Janneke: tutor
        ]]);
        // set availability of tutors
        DB::table('availabilities')->insert([[
            'id'            => 1,
            'tutor_id'      => 1,
            'day_number'    => 1,
            'from_time'     => '09:00:00',
            'to_time'       => '18:00:00',
        ], [
            'id'            => 2,
            'tutor_id'      => 1,
            'day_number'    => 2,
            'from_time'     => '19:00:00',
            'to_time'       => '22:30:00',
        ], [
            'id'            => 3,
            'tutor_id'      => 2,
            'day_number'    => 2,
            'from_time'     => '09:00:00',
            'to_time'       => '12:00:00',
        ], [
            'id'            => 4,
            'tutor_id'      => 2,
            'day_number'    => 2,
            'from_time'     => '13:00:00',
            'to_time'       => '19:00:00',
        ], [
            'id'            => 5,
            'tutor_id'      => 2,
            'day_number'    => 3,
            'from_time'     => '09:00:00',
            'to_time'       => '12:00:00',
        ], [
            'id'            => 6,
            'tutor_id'      => 2,
            'day_number'    => 3,
            'from_time'     => '13:00:00',
            'to_time'       => '19:00:00',
        ], [
            'id'            => 7,
            'tutor_id'      => 2,
            'day_number'    => 4,
            'from_time'     => '09:00:00',
            'to_time'       => '12:00:00',
        ], [
            'id'            => 8,
            'tutor_id'      => 2,
            'day_number'    => 4,
            'from_time'     => '13:00:00',
            'to_time'       => '21:00:00',
        ], [
            'id'            => 9,
            'tutor_id'      => 2,
            'day_number'    => 5,
            'from_time'     => '13:00:00',
            'to_time'       => '21:00:00',
        ]]);
    }
}
