<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CoursegroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('coursegroups')->insert([[
            'id'                => 1,
            'domain_id'         => 9,
            'name'              => 'Verzorging',
            'is_trial_group'    => 0,
            'ignore_for_price_list'=> 0
        ], [
            'id'                => 2,
            'domain_id'         => 9,
            'name'              => 'Proeflessen',
            'is_trial_group'    => 1,
            'ignore_for_price_list'=> 1
        ]]);
        // all coursegroups should have a primary teacher
        DB::table('coursegroup_tutor')->insert([[
            'coursegroup_id'        => 1,
            'tutor_id'              => 1,
            'age_group_adult'       => 1,
            'age_group_adolescent'  => 0,
            'age_group_child'       => 0,
        ], [
            'coursegroup_id'        => 1,
            'tutor_id'              => 2,
            'age_group_adult'       => 0,
            'age_group_adolescent'  => 1,
            'age_group_child'       => 1,
        ], [
            'coursegroup_id'        => 2,
            'tutor_id'              => 1,
            'age_group_adult'       => 1,
            'age_group_adolescent'  => 1,
            'age_group_child'       => 1,
        ]]);
    }
}
