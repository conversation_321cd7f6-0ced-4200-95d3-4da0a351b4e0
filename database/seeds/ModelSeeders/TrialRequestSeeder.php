<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory;

class TrialRequestSeeder extends Seeder {
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Factory::create();
        DB::table('trialstudents')->insert([
            [
                'id' => 1,
                'domain_id' => 9,
                'trialrequeststatus_id' => 1,
                'firstname' => $faker->firstName,
                'preposition' => 'van',
                'lastname' => $faker->lastName,
                'date_of_birth' => '1990-01-01',
                'telephone' => '0612345678',
                'email' => $faker->email,
                'course_id' => 3,
                // somewhere next month
                'requested_startdate' => $faker->dateTimeBetween('now', '+1 month')->format('Y-m-d'),
                'remarks' => $faker->text(100),
                'created_at' => now(),
                'updated_at' => now(),
            ],[
                'id' => 2,
                'domain_id' => 9,
                'trialrequeststatus_id' => 1,
                'firstname' => $faker->firstName,
                'preposition' => 'van',
                'lastname' => $faker->lastName,
                'date_of_birth' => '1990-01-01',
                'telephone' => '0612345678',
                'email' => $faker->email,
                'course_id' => 6,
                // somewhere next month
                'requested_startdate' => $faker->dateTimeBetween('now', '+1 month')->format('Y-m-d'),
                'remarks' => $faker->text(100),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }
}
