<?php

namespace Database\Seeders;

use Database\Seeders\ModelSeeders\AttendanceOptionsSeeder;
use Database\Seeders\ModelSeeders\CoursegroupSeeder;
use Database\Seeders\ModelSeeders\CoursesSeeder;
use Database\Seeders\ModelSeeders\DateExceptionsSeeder;
use Database\Seeders\ModelSeeders\DefaultChecklistSeeder;
use Database\Seeders\ModelSeeders\DomainSeeder;
use Database\Seeders\ModelSeeders\EmailLogSeeder;
use Database\Seeders\ModelSeeders\EventsSeeder;
use Database\Seeders\ModelSeeders\LibrarySeeder;
use Database\Seeders\ModelSeeders\LocationsSeeder;
use Database\Seeders\ModelSeeders\MailTemplatesSeeder;
use Database\Seeders\ModelSeeders\LoginSecuritySeeder;
use Database\Seeders\ModelSeeders\RecurrenceSeeder;
use Database\Seeders\ModelSeeders\RegistrationsSeeder;
use Database\Seeders\ModelSeeders\RolesSeeder;
use Database\Seeders\ModelSeeders\SchoolyearsSeeder;
use Database\Seeders\ModelSeeders\StudentgroupSeeder;
use Database\Seeders\ModelSeeders\StudentListSeeder;
use Database\Seeders\ModelSeeders\StudentsSeeder;
use Database\Seeders\ModelSeeders\TasksSeeder;
use Database\Seeders\ModelSeeders\TasktypesSeeder;
use Database\Seeders\ModelSeeders\TimetableSeeder;
use Database\Seeders\ModelSeeders\TrialcourseCoursesSeeder;
use Database\Seeders\ModelSeeders\TrialRequestSeeder;
use Database\Seeders\ModelSeeders\TrialRequestStatussesSeeder;
use Database\Seeders\ModelSeeders\UserSeeder;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // first, purge all tables, disregarding foreign key constraints
        // artisan db:wipe && artisan migrate --seed
        $this->call([
            DomainSeeder::class,
            RolesSeeder::class,
            UserSeeder::class,
            LoginSecuritySeeder::class,
            TasktypesSeeder::class,
            SchoolyearsSeeder::class,
            RecurrenceSeeder::class,
            CoursegroupSeeder::class,
            LocationsSeeder::class,
            MailTemplatesSeeder::class,
            StudentsSeeder::class,
            CoursesSeeder::class,
            StudentgroupSeeder::class,
            StudentlistSeeder::class,
            RegistrationsSeeder::class,
            TimetableSeeder::class,
            EventsSeeder::class,
            DateExceptionsSeeder::class,
            EmailLogSeeder::class,
            AttendanceOptionsSeeder::class,
            TrialRequestStatussesSeeder::class,
            TrialcourseCoursesSeeder::class,
            TrialRequestSeeder::class,
            TasksSeeder::class,
            DefaultChecklistSeeder::class,
            LibrarySeeder::class
        ]);
    }
}
