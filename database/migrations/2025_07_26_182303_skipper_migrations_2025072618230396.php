<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: c6105f83-a5b1-4c0d-a7d5-96c4739fd389
 * Migration local datetime: 2025-07-26 18:23:03.960131
 * Migration UTC datetime: 2025-07-26 16:23:03.960131
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2025072618230396 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('coursegroups', function (Blueprint $table) {
            $table->boolean('ignore_for_price_list')->default(0)->after('is_trial_group');
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('coursegroups', function (Blueprint $table) {
            $table->dropColumn('ignore_for_price_list');
        });
    }
}
