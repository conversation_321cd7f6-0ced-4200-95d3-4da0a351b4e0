<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Models\Course;

return new class extends Migration
{
    /**
     * Path to the JSON file containing course age group data
     */
    protected $jsonFilePath = 'course_agegroup.json';
    
    /**
     * Path to store the backup of updated course IDs for rollback
     */
    protected $backupFilePath = 'course_agegroup_updated_ids.json';
    
    /**
     * Run the migrations.
     * 
     * Reads the JSON file and updates courses with age range data.
     * Skips courses that don't exist.
     */
    public function up(): void
    {
        // Check if the JSON file exists
        if (!Storage::exists($this->jsonFilePath)) {
            Log::error("JSON file not found: {$this->jsonFilePath}");
            return;
        }
        
        // Read the JSON file
        $jsonContent = Storage::get($this->jsonFilePath);
        $courseData = json_decode($jsonContent, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error("Error parsing JSON file: " . json_last_error_msg());
            return;
        }
        
        $updatedCourseIds = [];
        $skippedCourseIds = [];
        $totalCourses = count($courseData);

        // Process each course in the JSON file
        foreach ($courseData as $course) {
            $courseId = $course['courseId'];
            $minAge = $course['from'];
            $maxAge = $course['to'];
            
            // Check if the course exists
            $exists = Course::query()
                ->where([['id', "=", $courseId], ['archive', "=", 0]])
                ->exists();
            
            if ($exists) {
                // Update the course with age range data
                $course = Course::query()->find($courseId);
                if ($course) {
                    $course->target_age_min = $minAge;
                    $course->target_age_max = $maxAge;
                    $course->save();
                }
                $updatedCourseIds[] = $courseId;
            } else {
                $skippedCourseIds[] = $courseId;
            }
        }
        
        // Store the updated course IDs for rollback
        Storage::put($this->backupFilePath, json_encode($updatedCourseIds));
        
        // Log summary
        Log::info("Course age range migration completed:");
        Log::info("- Total courses in JSON: {$totalCourses}");
        Log::info("- Updated courses: " . count($updatedCourseIds));
        Log::info("- Skipped courses: " . count($skippedCourseIds));
    }

    /**
     * Reverse the migrations.
     * 
     * Resets the age range fields to null for all courses that were updated.
     */
    public function down(): void
    {
        // Check if the backup file exists
        if (!Storage::exists($this->backupFilePath)) {
            Log::error("Backup file not found: {$this->backupFilePath}");
            return;
        }
        
        // Read the backup file
        $jsonContent = Storage::get($this->backupFilePath);
        $updatedCourseIds = json_decode($jsonContent, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error("Error parsing backup file: " . json_last_error_msg());
            return;
        }
        
        $resetCount = 0;
        
        // Reset the age range fields for each updated course
        foreach ($updatedCourseIds as $courseId) {
            $course = Course::withoutGlobalScopes()->find($courseId);
            if ($course) {
                $course->target_age_min = null;
                $course->target_age_max = null;
                $course->save();
            }
            
            $resetCount++;
        }
        
        // Delete the backup file
        Storage::delete($this->backupFilePath);
        
        // Log summary
        Log::info("Course age range rollback completed:");
        Log::info("- Reset courses: {$resetCount}");
    }
};
