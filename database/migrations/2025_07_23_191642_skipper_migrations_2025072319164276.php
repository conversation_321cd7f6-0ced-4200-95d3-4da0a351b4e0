<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: 635962b8-f85c-4482-b1a8-b6c1124673fb
 * Migration local datetime: 2025-07-23 19:16:42.765024
 * Migration UTC datetime: 2025-07-23 17:16:42.765024
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2025072319164276 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->string('domain_name', 255)->after('id')->change();
        });
        Schema::table('domains', function (Blueprint $table) {
            $table->string('name', 50)->after('privacy_url')->change();
        });
        Schema::table('courses', function (Blueprint $table) {
            $table->integer('target_age_min')->nullable(true)->default(null)->after('group_size_max');
        });
        Schema::table('courses', function (Blueprint $table) {
            $table->integer('target_age_max')->nullable(true)->default(null)->after('target_age_min');
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('target_age_max');
        });
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('target_age_min');
        });
        Schema::table('domains', function (Blueprint $table) {
            $table->string('name', 50)->after('privacy_url')->change();
        });
        Schema::table('domains', function (Blueprint $table) {
            $table->string('domain_name', 255)->after('id')->change();
        });
    }
}
